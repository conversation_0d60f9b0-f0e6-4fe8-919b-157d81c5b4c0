import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, UtensilsCrossed, Save } from 'lucide-react';
import { DietProtocolManualCreator } from '../components/DietProtocolManualCreator';
import { useCreateProtocolWithCheck } from '../hooks/useProtocolManagement';
import { apiService } from '../services/api';
import { toast } from 'react-toastify';

export function CreateDietProtocolManualPage() {
  const navigate = useNavigate();
  const createProtocolWithCheck = useCreateProtocolWithCheck();

  const handleSave = async (protocol: any) => {
    try {
      // Map type string to type_id number
      const getTypeId = (type: string): number => {
        const typeMap: { [key: string]: number } = {
          'weight-loss': 1,
          'maintenance': 2,
          'muscle-gain': 3
        };
        return typeMap[type] || 1; // Default to weight-loss if type not found
      };

      // Transform weeklyMeals object to meals array
      const transformMealsToArray = (weeklyMeals: any) => {
        const meals: any[] = [];

        Object.entries(weeklyMeals).forEach(([dayOfWeek, dayMeals]: [string, any]) => {
          if (Array.isArray(dayMeals)) {
            dayMeals.forEach((meal: any) => {
              meals.push({
                name: meal.name,
                day_of_week: dayOfWeek,
                meal_time: meal.time,
                foods: meal.foods.map((food: any) => ({
                  name: food.name,
                  quantity: Number(food.quantity) || 0,
                  unit: food.unit || 'g',
                  calories: Number(food.calories) || 0,
                  protein: Number(food.protein) || 0,
                  carbs: Number(food.carbs) || 0,
                  fat: Number(food.fat) || 0,
                  fiber: Number(food.fiber) || 0
                }))
              });
            });
          }
        });

        return meals;
      };

      // Create protocol data in the backend expected format
      const protocolData = {
        name: protocol.name,
        type_id: getTypeId(protocol.type),
        objective: protocol.objective || '',
        nutritional_goals: protocol.nutritional_goals || protocol.goals,
        meals: transformMealsToArray(protocol.weeklyMeals),
        supplements: protocol.supplements?.map((supplement: any) => ({
          name: supplement.name,
          dosage: supplement.dosage,
          supplement_time: supplement.supplement_time,
          notes: supplement.notes || ''
        })) || [],
        general_notes: protocol.notes || ''
      };

      console.log('🚀 Sending protocol data to API:', protocolData);

      // Validate required fields before sending
      if (!protocolData.name) {
        throw new Error('Protocol name is required');
      }
      if (!protocolData.type_id || typeof protocolData.type_id !== 'number') {
        throw new Error('Valid type_id is required');
      }
      if (!protocolData.nutritional_goals || typeof protocolData.nutritional_goals !== 'object') {
        throw new Error('Nutritional goals are required');
      }
      if (!Array.isArray(protocolData.meals)) {
        throw new Error('Meals must be an array');
      }

      console.log('✅ Validation passed, using protocol creation hook...');

      // Use the new hook that handles active protocol finalization properly
      createProtocolWithCheck.mutate({
        protocolData,
        protocolType: 'diet',
        shouldFinalizeActive: true
      }, {
        onSuccess: (response) => {
          console.log('✅ Diet protocol created successfully:', response);
          toast.success('Protocolo de dieta salvo com sucesso!', { position: 'bottom-right' });
          // Adicionar um atraso de 2 segundos antes do redirecionamento para melhorar a experiência do usuário
          setTimeout(() => {
            navigate('/dashboard/diet');
          }, 2000);
        },
        onError: (error: any) => {
          console.error('Error saving diet protocol:', error);

          // Extract specific error messages from API response
          let errorMessage = 'Erro ao salvar protocolo de dieta';

          if (error?.response?.data?.message) {
            if (Array.isArray(error.response.data.message)) {
              errorMessage = error.response.data.message.join(', ');
            } else {
              errorMessage = error.response.data.message;
            }
          } else if (error?.message) {
            errorMessage = error.message;
          }

          console.error('🚨 Detailed error:', {
            status: error?.response?.status,
            statusText: error?.response?.statusText,
            data: error?.response?.data,
            message: error?.message
          });

          toast.error(errorMessage, { position: 'bottom-right' });
        }
      });
    } catch (error: any) {
      console.error('Error in handleSave:', error);
      toast.error('Erro ao processar protocolo de dieta', { position: 'bottom-right' });
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/diet');
  };

  return (
    <div className="min-h-screen bg-snapfit-black">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <button
              onClick={handleCancel}
              className="p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                <UtensilsCrossed className="w-4 h-4 text-snapfit-green" />
              </div>
              <h1 className="text-lg font-medium text-white">
                Criar Protocolo de Dieta Manual
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 pb-20">
        <div className="max-w-4xl mx-auto">
          {/* Informações sobre criação manual */}
          <div className="bg-snapfit-green/10 rounded-xl p-4 border border-snapfit-green/20 mb-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5">
                <UtensilsCrossed className="w-4 h-4 text-snapfit-green" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-white mb-2">Criação Manual de Protocolo</h3>
                <div className="text-xs text-gray-400 space-y-1">
                  <p>• Configure objetivos nutricionais personalizados</p>
                  <p>• Selecione alimentos do banco de dados integrado</p>
                  <p>• Organize refeições por dia da semana</p>
                  <p>• Adicione suplementos com horários específicos</p>
                  <p>• Cálculo automático de calorias e macronutrientes</p>
                </div>
              </div>
            </div>
          </div>

          {/* Componente de criação manual */}
          <div className="bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20">
            <DietProtocolManualCreator
              onSave={handleSave}
              onCancel={handleCancel}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
