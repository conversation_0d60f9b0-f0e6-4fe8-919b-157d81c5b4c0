/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ([
/* 0 */,
/* 1 */
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),
/* 2 */
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),
/* 3 */
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),
/* 4 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const config_1 = __webpack_require__(6);
const database_config_1 = __webpack_require__(7);
const auth_module_1 = __webpack_require__(8);
const users_module_1 = __webpack_require__(37);
const protocols_module_1 = __webpack_require__(53);
const diet_module_1 = __webpack_require__(64);
const micronutrients_module_1 = __webpack_require__(76);
const recipes_module_1 = __webpack_require__(86);
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRoot(database_config_1.databaseConfig),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            diet_module_1.DietModule,
            protocols_module_1.ProtocolsModule,
            micronutrients_module_1.MicronutrientsModule,
            recipes_module_1.RecipesModule
        ],
        controllers: [],
        providers: [],
    })
], AppModule);


/***/ }),
/* 5 */
/***/ ((module) => {

module.exports = require("@nestjs/typeorm");

/***/ }),
/* 6 */
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),
/* 7 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.databaseConfig = void 0;
const isProduction = process.env.NODE_ENV === 'production';
exports.databaseConfig = {
    type: 'mysql',
    host: isProduction ?
        (process.env.PROD_DB_HOST || '************') :
        (process.env.DB_HOST || 'localhost'),
    port: parseInt(process.env.DB_PORT || '3306'),
    username: isProduction ?
        (process.env.PROD_DB_USERNAME || 'u164938089_snapfit') :
        (process.env.DB_USERNAME || 'root'),
    password: isProduction ?
        (process.env.PROD_DB_PASSWORD || 'Snapfit123') :
        (process.env.DB_PASSWORD || ''),
    database: isProduction ?
        (process.env.PROD_DB_DATABASE || 'u164938089_snapfit') :
        (process.env.DB_DATABASE || 'snapfit_dev'),
    entities: isProduction ?
        ['dist/**/*.entity{.ts,.js}'] :
        [__dirname + '/../**/*.entity{.ts,.js}'],
    synchronize: !isProduction,
    migrations: isProduction ? ['dist/migrations/*{.ts,.js}'] : undefined,
    migrationsRun: isProduction,
    logging: process.env.NODE_ENV !== 'production',
    extra: {
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000,
    },
    retryAttempts: 3,
    retryDelay: 3000,
};


/***/ }),
/* 8 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthModule = void 0;
const common_1 = __webpack_require__(2);
const jwt_1 = __webpack_require__(9);
const passport_1 = __webpack_require__(10);
const auth_service_1 = __webpack_require__(11);
const auth_controller_1 = __webpack_require__(28);
const users_module_1 = __webpack_require__(37);
const jwt_strategy_1 = __webpack_require__(47);
const local_strategy_1 = __webpack_require__(49);
const typeorm_1 = __webpack_require__(5);
const user_entity_1 = __webpack_require__(13);
const mail_module_1 = __webpack_require__(51);
const sms_module_1 = __webpack_require__(52);
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mail_module_1.MailModule,
            sms_module_1.SmsModule,
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User]),
            users_module_1.UsersModule,
            passport_1.PassportModule,
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'your-secret-key',
                signOptions: { expiresIn: '7d' },
            }),
        ],
        providers: [auth_service_1.AuthService, local_strategy_1.LocalStrategy, jwt_strategy_1.JwtStrategy],
        controllers: [auth_controller_1.AuthController],
        exports: [auth_service_1.AuthService],
    })
], AuthModule);


/***/ }),
/* 9 */
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),
/* 10 */
/***/ ((module) => {

module.exports = require("@nestjs/passport");

/***/ }),
/* 11 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthService = void 0;
const common_1 = __webpack_require__(2);
const jwt_1 = __webpack_require__(9);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const mail_service_1 = __webpack_require__(26);
const sms_service_1 = __webpack_require__(27);
let AuthService = class AuthService {
    constructor(userRepository, dataSource, jwtService, mailService, smsService) {
        this.userRepository = userRepository;
        this.dataSource = dataSource;
        this.jwtService = jwtService;
        this.mailService = mailService;
        this.smsService = smsService;
    }
    async register(registerDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const result = await queryRunner.query('SELECT register_user($1, $2, $3, $4)', [registerDto.email, registerDto.password, registerDto.name, registerDto.role]);
            const userId = result[0].register_user;
            const user = await this.userRepository.findOne({ where: { id: userId } });
            if (!user)
                throw new Error('Failed to create user');
            await queryRunner.commitTransaction();
            if (registerDto.email) {
                await this.requestVerification(user.id, 'email');
            }
            if (registerDto.phone) {
                await this.requestVerification(user.id, 'phone');
            }
            return user;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async validateUser(email, password, deviceInfo) {
        try {
            const result = await this.dataSource.query('SELECT * FROM login_user($1, $2, $3)', [email, password, JSON.stringify(deviceInfo || {})]);
            return result[0];
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
    async login(user, deviceInfo) {
        return this.validateUser(user.email, user.password, deviceInfo);
    }
    async requestPasswordReset(email) {
        const user = await this.userRepository.findOne({ where: { email } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const result = await this.dataSource.query('SELECT request_password_reset($1)', [email]);
        const tokenId = result[0].request_password_reset;
        await this.mailService.sendPasswordResetEmail(user.email, tokenId);
    }
    async resetPassword(token, newPassword) {
        const [tokenId, tokenValue] = token.split(':');
        const result = await this.dataSource.query('SELECT verify_reset_token($1, $2)', [tokenId, tokenValue]);
        const isValid = result[0].verify_reset_token;
        if (!isValid) {
            throw new common_1.UnauthorizedException('Invalid or expired token');
        }
        const changeResult = await this.dataSource.query('SELECT change_password($1, $2, $3)', [tokenId, tokenValue, newPassword]);
        const success = changeResult[0].change_password;
        if (!success) {
            throw new common_1.UnauthorizedException('Failed to reset password');
        }
    }
    async requestVerification(userId, type) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const result = await this.dataSource.query('SELECT request_verification($1, $2)', [userId, type]);
        const codeId = result[0].request_verification;
        if (type === 'email') {
            await this.mailService.sendVerificationEmail(user.email, codeId);
        }
        else {
            await this.smsService.sendVerificationSms(user.phone, codeId);
        }
    }
    async verifyCode(userId, code) {
        const result = await this.dataSource.query('SELECT verify_code($1, $2)', [userId, code]);
        return result[0].verify_code;
    }
    async refreshToken(refreshToken, deviceInfo) {
        try {
            const result = await this.dataSource.query('SELECT * FROM refresh_token($1, $2)', [refreshToken, JSON.stringify(deviceInfo || {})]);
            return result[0];
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(sessionId, allDevices = false) {
        await this.dataSource.query('SELECT logout_user($1, $2)', [sessionId, allDevices]);
    }
    async getUserSessions(userId) {
        const result = await this.dataSource.query('SELECT * FROM get_user_sessions($1)', [userId]);
        return result;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _b : Object, typeof (_c = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _c : Object, typeof (_d = typeof mail_service_1.MailService !== "undefined" && mail_service_1.MailService) === "function" ? _d : Object, typeof (_e = typeof sms_service_1.SmsService !== "undefined" && sms_service_1.SmsService) === "function" ? _e : Object])
], AuthService);


/***/ }),
/* 12 */
/***/ ((module) => {

module.exports = require("typeorm");

/***/ }),
/* 13 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserRole = exports.User = void 0;
const typeorm_1 = __webpack_require__(12);
const user_role_enum_1 = __webpack_require__(14);
Object.defineProperty(exports, "UserRole", ({ enumerable: true, get: function () { return user_role_enum_1.UserRole; } }));
const assessment_entity_1 = __webpack_require__(15);
const workout_entity_1 = __webpack_require__(16);
const meal_entity_1 = __webpack_require__(18);
const protocol_entity_1 = __webpack_require__(20);
const progress_analysis_entity_1 = __webpack_require__(22);
const water_log_entity_1 = __webpack_require__(23);
const activity_entity_1 = __webpack_require__(24);
const daily_nutrition_entity_1 = __webpack_require__(25);
let User = class User {
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: user_role_enum_1.UserRole, default: user_role_enum_1.UserRole.USER }),
    __metadata("design:type", typeof (_a = typeof user_role_enum_1.UserRole !== "undefined" && user_role_enum_1.UserRole) === "function" ? _a : Object)
], User.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "photo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], User.prototype, "birthDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], User.prototype, "height", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], User.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "activityLevel", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User, { nullable: true }),
    __metadata("design:type", User)
], User.prototype, "coach", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User, { nullable: true }),
    __metadata("design:type", User)
], User.prototype, "nutritionist", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => assessment_entity_1.Assessment, assessment => assessment.user),
    __metadata("design:type", Array)
], User.prototype, "assessments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => workout_entity_1.Workout, workout => workout.user),
    __metadata("design:type", Array)
], User.prototype, "workouts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => meal_entity_1.Meal, meal => meal.user),
    __metadata("design:type", Array)
], User.prototype, "meals", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => protocol_entity_1.Protocol, protocol => protocol.user),
    __metadata("design:type", Array)
], User.prototype, "protocols", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => protocol_entity_1.Protocol, protocol => protocol.createdBy),
    __metadata("design:type", Array)
], User.prototype, "createdProtocols", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => progress_analysis_entity_1.ProgressAnalysis, analysis => analysis.user),
    __metadata("design:type", Array)
], User.prototype, "progressAnalyses", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => protocol_entity_1.Protocol),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", typeof (_c = typeof protocol_entity_1.Protocol !== "undefined" && protocol_entity_1.Protocol) === "function" ? _c : Object)
], User.prototype, "currentDietProtocol", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => water_log_entity_1.WaterLog, waterLog => waterLog.user),
    __metadata("design:type", Array)
], User.prototype, "waterLogs", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => activity_entity_1.Activity, activity => activity.user),
    __metadata("design:type", Array)
], User.prototype, "activities", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => daily_nutrition_entity_1.DailyNutrition, dailyNutrition => dailyNutrition.user),
    __metadata("design:type", Array)
], User.prototype, "dailyNutrition", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], User.prototype, "updatedAt", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users')
], User);


/***/ }),
/* 14 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "user";
    UserRole["ADMIN"] = "admin";
    UserRole["COACH"] = "coach";
    UserRole["NUTRITIONIST"] = "nutritionist";
})(UserRole || (exports.UserRole = UserRole = {}));


/***/ }),
/* 15 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Assessment = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let Assessment = class Assessment {
};
exports.Assessment = Assessment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Assessment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.assessments),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], Assessment.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 5, scale: 2 }),
    __metadata("design:type", Number)
], Assessment.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Assessment.prototype, "bodyFat", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], Assessment.prototype, "photos", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], Assessment.prototype, "measurements", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Assessment.prototype, "date", void 0);
exports.Assessment = Assessment = __decorate([
    (0, typeorm_1.Entity)('assessments')
], Assessment);


/***/ }),
/* 16 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Workout = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const exercise_entity_1 = __webpack_require__(17);
let Workout = class Workout {
};
exports.Workout = Workout;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Workout.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Workout.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.workouts),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], Workout.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Workout.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => exercise_entity_1.Exercise, exercise => exercise.workout, { cascade: true }),
    __metadata("design:type", Array)
], Workout.prototype, "exercises", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Workout.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Workout.prototype, "completed", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Workout.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Workout.prototype, "date", void 0);
exports.Workout = Workout = __decorate([
    (0, typeorm_1.Entity)('workouts')
], Workout);


/***/ }),
/* 17 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Exercise = void 0;
const typeorm_1 = __webpack_require__(12);
const workout_entity_1 = __webpack_require__(16);
let Exercise = class Exercise {
};
exports.Exercise = Exercise;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Exercise.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Exercise.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Exercise.prototype, "sets", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Exercise.prototype, "reps", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Exercise.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Exercise.prototype, "muscleGroup", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => workout_entity_1.Workout, workout => workout.exercises),
    __metadata("design:type", typeof (_a = typeof workout_entity_1.Workout !== "undefined" && workout_entity_1.Workout) === "function" ? _a : Object)
], Exercise.prototype, "workout", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Exercise.prototype, "rpe", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Exercise.prototype, "restTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Exercise.prototype, "completed", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Exercise.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Exercise.prototype, "gifUrl", void 0);
exports.Exercise = Exercise = __decorate([
    (0, typeorm_1.Entity)('exercises')
], Exercise);


/***/ }),
/* 18 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Meal = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const food_entity_1 = __webpack_require__(19);
let Meal = class Meal {
};
exports.Meal = Meal;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Meal.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "time", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.meals),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], Meal.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => food_entity_1.Food, food => food.meal, { cascade: true }),
    __metadata("design:type", Array)
], Meal.prototype, "foods", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Meal.prototype, "completed", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Meal.prototype, "date", void 0);
exports.Meal = Meal = __decorate([
    (0, typeorm_1.Entity)('meals')
], Meal);


/***/ }),
/* 19 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Food = void 0;
const typeorm_1 = __webpack_require__(12);
const meal_entity_1 = __webpack_require__(18);
let Food = class Food {
};
exports.Food = Food;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Food.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Food.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => meal_entity_1.Meal, meal => meal.foods),
    __metadata("design:type", typeof (_a = typeof meal_entity_1.Meal !== "undefined" && meal_entity_1.Meal) === "function" ? _a : Object)
], Food.prototype, "meal", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Food.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "calories", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "protein", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "carbs", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "fat", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Food.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Food.prototype, "category", void 0);
exports.Food = Food = __decorate([
    (0, typeorm_1.Entity)('foods')
], Food);


/***/ }),
/* 20 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Protocol = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const protocol_type_enum_1 = __webpack_require__(21);
let Protocol = class Protocol {
};
exports.Protocol = Protocol;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Protocol.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: protocol_type_enum_1.ProtocolType }),
    __metadata("design:type", typeof (_a = typeof protocol_type_enum_1.ProtocolType !== "undefined" && protocol_type_enum_1.ProtocolType) === "function" ? _a : Object)
], Protocol.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "objective", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Object)
], Protocol.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Protocol.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Protocol.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Protocol.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.protocols),
    __metadata("design:type", typeof (_d = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _d : Object)
], Protocol.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.createdProtocols),
    __metadata("design:type", typeof (_e = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _e : Object)
], Protocol.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "createdById", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'active' }),
    __metadata("design:type", String)
], Protocol.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], Protocol.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], Protocol.prototype, "updatedAt", void 0);
exports.Protocol = Protocol = __decorate([
    (0, typeorm_1.Entity)('protocols')
], Protocol);


/***/ }),
/* 21 */
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProtocolType = void 0;
var ProtocolType;
(function (ProtocolType) {
    ProtocolType["WORKOUT"] = "workout";
    ProtocolType["DIET"] = "diet";
})(ProtocolType || (exports.ProtocolType = ProtocolType = {}));


/***/ }),
/* 22 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProgressAnalysis = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let ProgressAnalysis = class ProgressAnalysis {
};
exports.ProgressAnalysis = ProgressAnalysis;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProgressAnalysis.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], ProgressAnalysis.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProgressAnalysis.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Object)
], ProgressAnalysis.prototype, "workoutAnalysis", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Object)
], ProgressAnalysis.prototype, "nutritionAnalysis", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Array)
], ProgressAnalysis.prototype, "volumeDistribution", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Array)
], ProgressAnalysis.prototype, "strengthProgress", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Object)
], ProgressAnalysis.prototype, "weeklyAdherence", void 0);
__decorate([
    (0, typeorm_1.Column)('json'),
    __metadata("design:type", Object)
], ProgressAnalysis.prototype, "caloricBalance", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ProgressAnalysis.prototype, "date", void 0);
exports.ProgressAnalysis = ProgressAnalysis = __decorate([
    (0, typeorm_1.Entity)('progress_analysis')
], ProgressAnalysis);


/***/ }),
/* 23 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.WaterLog = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let WaterLog = class WaterLog {
};
exports.WaterLog = WaterLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], WaterLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.waterLogs),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], WaterLog.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WaterLog.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], WaterLog.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], WaterLog.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], WaterLog.prototype, "createdAt", void 0);
exports.WaterLog = WaterLog = __decorate([
    (0, typeorm_1.Entity)('water_logs')
], WaterLog);


/***/ }),
/* 24 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Activity = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let Activity = class Activity {
};
exports.Activity = Activity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Activity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.activities),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], Activity.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Activity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Activity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Activity.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Activity.prototype, "caloriesBurned", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Activity.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Activity.prototype, "createdAt", void 0);
exports.Activity = Activity = __decorate([
    (0, typeorm_1.Entity)('activities')
], Activity);


/***/ }),
/* 25 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DailyNutrition = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let DailyNutrition = class DailyNutrition {
};
exports.DailyNutrition = DailyNutrition;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], DailyNutrition.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.dailyNutrition),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], DailyNutrition.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], DailyNutrition.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], DailyNutrition.prototype, "date", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], DailyNutrition.prototype, "totals", void 0);
exports.DailyNutrition = DailyNutrition = __decorate([
    (0, typeorm_1.Entity)('daily_nutrition')
], DailyNutrition);


/***/ }),
/* 26 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MailService = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(6);
let MailService = class MailService {
    constructor(configService) {
        this.configService = configService;
    }
    async sendPasswordResetEmail(email, tokenId) {
        const resetLink = `${this.configService.get('APP_URL')}/reset-password?token=${tokenId}`;
        console.log(`Password reset link sent to ${email}: ${resetLink}`);
    }
    async sendVerificationEmail(email, codeId) {
        console.log(`Verification code sent to ${email}: ${codeId}`);
    }
};
exports.MailService = MailService;
exports.MailService = MailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], MailService);


/***/ }),
/* 27 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SmsService = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(6);
let SmsService = class SmsService {
    constructor(configService) {
        this.configService = configService;
    }
    async sendVerificationSms(phone, code) {
        console.log(`SMS verification code sent to ${phone}: ${code}`);
    }
};
exports.SmsService = SmsService;
exports.SmsService = SmsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], SmsService);


/***/ }),
/* 28 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const auth_service_1 = __webpack_require__(11);
const register_user_dto_1 = __webpack_require__(29);
const login_dto_1 = __webpack_require__(31);
const reset_password_dto_1 = __webpack_require__(32);
const verify_code_dto_1 = __webpack_require__(33);
const refresh_token_dto_1 = __webpack_require__(34);
const jwt_auth_guard_1 = __webpack_require__(35);
const local_auth_guard_1 = __webpack_require__(36);
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async register(registerDto) {
        try {
            const user = await this.authService.register(registerDto);
            return {
                message: 'Registration successful',
                user: {
                    id: user.id,
                    email: user.email,
                    name: user.name
                }
            };
        }
        catch (error) {
            if (error.code === '23505') {
                throw new common_1.HttpException('Email already registered', common_1.HttpStatus.CONFLICT);
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async login(loginDto, req) {
        try {
            const deviceInfo = {
                ip: req.ip,
                userAgent: req.headers['user-agent'],
                ...req.body.deviceInfo
            };
            const result = await this.authService.login(req.user, deviceInfo);
            return {
                message: 'Login successful',
                ...result
            };
        }
        catch (error) {
            throw new common_1.HttpException('Invalid credentials', common_1.HttpStatus.UNAUTHORIZED);
        }
    }
    async refreshToken(refreshTokenDto, req) {
        try {
            const deviceInfo = {
                ip: req.ip,
                userAgent: req.headers['user-agent']
            };
            const result = await this.authService.refreshToken(refreshTokenDto.refreshToken, deviceInfo);
            return {
                message: 'Token refreshed successfully',
                ...result
            };
        }
        catch (error) {
            throw new common_1.HttpException('Invalid refresh token', common_1.HttpStatus.UNAUTHORIZED);
        }
    }
    async logout(req, allDevices) {
        try {
            await this.authService.logout(req.user.sessionId, allDevices);
            return { message: 'Logout successful' };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async requestPasswordReset(email) {
        try {
            await this.authService.requestPasswordReset(email);
            return { message: 'Password reset instructions sent to your email' };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async resetPassword(resetPasswordDto) {
        try {
            await this.authService.resetPassword(resetPasswordDto.token, resetPasswordDto.newPassword);
            return { message: 'Password successfully reset' };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async requestVerification(req, type) {
        try {
            await this.authService.requestVerification(req.user.id, type);
            return { message: `Verification code sent to your ${type}` };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async verify(req, verifyCodeDto) {
        try {
            await this.authService.verifyCode(req.user.id, verifyCodeDto.code);
            return { message: 'Successfully verified' };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getProfile(req) {
        return req.user;
    }
    async getSessions(req) {
        try {
            const sessions = await this.authService.getUserSessions(req.user.id);
            return sessions;
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'Register new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'User successfully registered' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof register_user_dto_1.RegisterUserDto !== "undefined" && register_user_dto_1.RegisterUserDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.UseGuards)(local_auth_guard_1.LocalAuthGuard),
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({ summary: 'User login' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Login successful' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid credentials' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof login_dto_1.LoginDto !== "undefined" && login_dto_1.LoginDto) === "function" ? _c : Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, swagger_1.ApiOperation)({ summary: 'Refresh access token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Token refreshed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid refresh token' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof refresh_token_dto_1.RefreshTokenDto !== "undefined" && refresh_token_dto_1.RefreshTokenDto) === "function" ? _d : Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('logout'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'User logout' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Logout successful' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('all_devices')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Boolean]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Post)('request-password-reset'),
    (0, swagger_1.ApiOperation)({ summary: 'Request password reset' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Password reset email sent' }),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "requestPasswordReset", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, swagger_1.ApiOperation)({ summary: 'Reset password using token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Password successfully reset' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_e = typeof reset_password_dto_1.ResetPasswordDto !== "undefined" && reset_password_dto_1.ResetPasswordDto) === "function" ? _e : Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Post)('request-verification'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Request email/phone verification' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Verification code sent' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "requestVerification", null);
__decorate([
    (0, common_1.Post)('verify'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Verify email/phone' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Successfully verified' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_f = typeof verify_code_dto_1.VerifyCodeDto !== "undefined" && verify_code_dto_1.VerifyCodeDto) === "function" ? _f : Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verify", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user profile' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User profile retrieved' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Get)('sessions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get user sessions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User sessions retrieved' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getSessions", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('auth'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], AuthController);


/***/ }),
/* 29 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RegisterUserDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
const user_role_enum_1 = __webpack_require__(14);
class RegisterUserDto {
}
exports.RegisterUserDto = RegisterUserDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    (0, class_validator_1.Matches)(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, {
        message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character'
    }),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: user_role_enum_1.UserRole }),
    (0, class_validator_1.IsEnum)(user_role_enum_1.UserRole),
    __metadata("design:type", typeof (_a = typeof user_role_enum_1.UserRole !== "undefined" && user_role_enum_1.UserRole) === "function" ? _a : Object)
], RegisterUserDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "photo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], RegisterUserDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], RegisterUserDto.prototype, "activityLevel", void 0);


/***/ }),
/* 30 */
/***/ ((module) => {

module.exports = require("class-validator");

/***/ }),
/* 31 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LoginDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class LoginDto {
}
exports.LoginDto = LoginDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], LoginDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LoginDto.prototype, "password", void 0);


/***/ }),
/* 32 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ResetPasswordDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class ResetPasswordDto {
}
exports.ResetPasswordDto = ResetPasswordDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResetPasswordDto.prototype, "token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    (0, class_validator_1.Matches)(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, {
        message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 number and 1 special character'
    }),
    __metadata("design:type", String)
], ResetPasswordDto.prototype, "newPassword", void 0);


/***/ }),
/* 33 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.VerifyCodeDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class VerifyCodeDto {
}
exports.VerifyCodeDto = VerifyCodeDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(6, 6),
    __metadata("design:type", String)
], VerifyCodeDto.prototype, "code", void 0);


/***/ }),
/* 34 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RefreshTokenDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class RefreshTokenDto {
}
exports.RefreshTokenDto = RefreshTokenDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RefreshTokenDto.prototype, "refreshToken", void 0);


/***/ }),
/* 35 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtAuthGuard = void 0;
const common_1 = __webpack_require__(2);
const passport_1 = __webpack_require__(10);
let JwtAuthGuard = class JwtAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)()
], JwtAuthGuard);


/***/ }),
/* 36 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LocalAuthGuard = void 0;
const common_1 = __webpack_require__(2);
const passport_1 = __webpack_require__(10);
let LocalAuthGuard = class LocalAuthGuard extends (0, passport_1.AuthGuard)('local') {
};
exports.LocalAuthGuard = LocalAuthGuard;
exports.LocalAuthGuard = LocalAuthGuard = __decorate([
    (0, common_1.Injectable)()
], LocalAuthGuard);


/***/ }),
/* 37 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UsersModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const users_service_1 = __webpack_require__(38);
const users_controller_1 = __webpack_require__(41);
const user_entity_1 = __webpack_require__(13);
const ai_module_1 = __webpack_require__(46);
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([user_entity_1.User]), ai_module_1.AIModule],
        providers: [users_service_1.UsersService],
        controllers: [users_controller_1.UsersController],
        exports: [users_service_1.UsersService],
    })
], UsersModule);


/***/ }),
/* 38 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UsersService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const user_role_enum_1 = __webpack_require__(14);
const ai_service_1 = __webpack_require__(39);
const bcrypt = __webpack_require__(40);
let UsersService = class UsersService {
    constructor(usersRepository, aiService) {
        this.usersRepository = usersRepository;
        this.aiService = aiService;
    }
    async create(createUserDto) {
        const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
        const user = this.usersRepository.create({
            ...createUserDto,
            password: hashedPassword
        });
        return this.usersRepository.save(user);
    }
    async findAll() {
        return this.usersRepository.find({
            select: ['id', 'name', 'email', 'role', 'photo', 'code', 'createdAt']
        });
    }
    async findOne(id) {
        const user = await this.usersRepository.findOne({
            where: { id },
            select: ['id', 'name', 'email', 'role', 'photo', 'code', 'height', 'activityLevel', 'createdAt']
        });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return user;
    }
    async update(id, updateUserDto) {
        const user = await this.findOne(id);
        if (updateUserDto.password) {
            updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
        }
        Object.assign(user, updateUserDto);
        return this.usersRepository.save(user);
    }
    async remove(id) {
        const user = await this.findOne(id);
        await this.usersRepository.remove(user);
    }
    async generateSyncCode(userId) {
        const user = await this.findOne(userId);
        let code = '';
        let isUnique = false;
        while (!isUnique) {
            code = Math.floor(100000 + Math.random() * 900000).toString();
            const existingUser = await this.usersRepository.findOne({ where: { code } });
            if (!existingUser) {
                isUnique = true;
            }
        }
        user.code = code;
        await this.usersRepository.save(user);
        return code;
    }
    async syncWithProfessional(clientId, professionalCode) {
        const client = await this.findOne(clientId);
        const professional = await this.usersRepository.findOne({
            where: { code: professionalCode },
            select: ['id', 'name', 'email', 'role', 'photo']
        });
        if (!professional) {
            return { success: false, message: 'Código não encontrado' };
        }
        if (professional.role !== user_role_enum_1.UserRole.COACH && professional.role !== user_role_enum_1.UserRole.NUTRITIONIST) {
            return { success: false, message: 'Código inválido - não pertence a um profissional' };
        }
        if (professional.role === user_role_enum_1.UserRole.COACH && client.coach?.id === professional.id) {
            return { success: false, message: 'Você já está sincronizado com este coach' };
        }
        if (professional.role === user_role_enum_1.UserRole.NUTRITIONIST && client.nutritionist?.id === professional.id) {
            return { success: false, message: 'Você já está sincronizado com este nutricionista' };
        }
        if (professional.role === user_role_enum_1.UserRole.COACH) {
            client.coach = professional;
        }
        else {
            client.nutritionist = professional;
        }
        await this.usersRepository.save(client);
        return {
            success: true,
            message: `Sincronizado com sucesso com ${professional.role === user_role_enum_1.UserRole.COACH ? 'coach' : 'nutricionista'} ${professional.name}`,
            professional: {
                id: professional.id,
                name: professional.name,
                role: professional.role,
                photo: professional.photo
            }
        };
    }
    async getSyncedClients(professionalId) {
        const professional = await this.findOne(professionalId);
        if (professional.role === user_role_enum_1.UserRole.COACH) {
            return this.usersRepository.find({
                where: { coach: { id: professionalId } },
                select: ['id', 'name', 'email', 'photo', 'createdAt'],
                relations: ['currentDietProtocol']
            });
        }
        else if (professional.role === user_role_enum_1.UserRole.NUTRITIONIST) {
            return this.usersRepository.find({
                where: { nutritionist: { id: professionalId } },
                select: ['id', 'name', 'email', 'photo', 'createdAt'],
                relations: ['currentDietProtocol']
            });
        }
        return [];
    }
    async findByEmail(email) {
        return this.usersRepository.findOne({ where: { email } });
    }
    async getAIFoodSuggestions(userId, request, mealId, mealType) {
        const user = await this.findOne(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return this.aiService.generateFoodSuggestions(userId, request, mealId, mealType);
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof ai_service_1.AIService !== "undefined" && ai_service_1.AIService) === "function" ? _b : Object])
], UsersService);


/***/ }),
/* 39 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AIService = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(6);
let AIService = class AIService {
    constructor(configService) {
        this.configService = configService;
    }
    async generateFoodSuggestions(userId, request, mealId, mealType) {
        try {
            if (request.type === 'text' && request.content) {
                return this.generateTextBasedSuggestions(request.content, mealType);
            }
            else if (request.type === 'image') {
                return this.generateImageBasedSuggestions();
            }
            else if (request.type === 'audio') {
                return this.generateAudioBasedSuggestions();
            }
            else {
                return this.generateDefaultSuggestions(mealType);
            }
        }
        catch (error) {
            console.error('Error generating AI food suggestions:', error);
            return this.generateDefaultSuggestions(mealType);
        }
    }
    generateTextBasedSuggestions(content, mealType) {
        const lowerContent = content.toLowerCase();
        if (lowerContent.includes('proteína') || lowerContent.includes('protein')) {
            return this.getHighProteinSuggestions();
        }
        else if (lowerContent.includes('carboidrato') || lowerContent.includes('carbs')) {
            return this.getHighCarbSuggestions();
        }
        else if (lowerContent.includes('vegetariano') || lowerContent.includes('vegano')) {
            return this.getVegetarianSuggestions();
        }
        else if (lowerContent.includes('low carb') || lowerContent.includes('cetogênica')) {
            return this.getLowCarbSuggestions();
        }
        else {
            return this.generateDefaultSuggestions(mealType);
        }
    }
    generateImageBasedSuggestions() {
        return [
            {
                name: 'Prato identificado na imagem',
                quantity: '1 porção',
                calories: 350,
                protein: 25,
                carbs: 30,
                fat: 15,
                description: 'Baseado na análise da imagem enviada'
            },
            {
                name: 'Alternativa similar',
                quantity: '1 porção',
                calories: 320,
                protein: 22,
                carbs: 28,
                fat: 12,
                description: 'Opção similar com menos calorias'
            }
        ];
    }
    generateAudioBasedSuggestions() {
        return [
            {
                name: 'Alimento mencionado no áudio',
                quantity: '1 porção',
                calories: 280,
                protein: 20,
                carbs: 25,
                fat: 10,
                description: 'Baseado na transcrição do áudio'
            }
        ];
    }
    getHighProteinSuggestions() {
        return [
            {
                name: 'Peito de frango grelhado',
                quantity: '150g',
                calories: 248,
                protein: 46,
                carbs: 0,
                fat: 5,
                description: 'Excelente fonte de proteína magra'
            },
            {
                name: 'Ovo mexido',
                quantity: '2 unidades',
                calories: 155,
                protein: 13,
                carbs: 1,
                fat: 11,
                description: 'Proteína completa e versátil'
            },
            {
                name: 'Iogurte grego natural',
                quantity: '200g',
                calories: 130,
                protein: 20,
                carbs: 9,
                fat: 0,
                description: 'Rico em proteína e probióticos'
            }
        ];
    }
    getHighCarbSuggestions() {
        return [
            {
                name: 'Arroz integral',
                quantity: '100g cozido',
                calories: 111,
                protein: 3,
                carbs: 23,
                fat: 1,
                description: 'Carboidrato complexo e nutritivo'
            },
            {
                name: 'Batata doce assada',
                quantity: '150g',
                calories: 129,
                protein: 2,
                carbs: 30,
                fat: 0,
                description: 'Rica em vitaminas e fibras'
            },
            {
                name: 'Aveia',
                quantity: '50g',
                calories: 190,
                protein: 7,
                carbs: 32,
                fat: 4,
                description: 'Carboidrato de liberação lenta'
            }
        ];
    }
    getVegetarianSuggestions() {
        return [
            {
                name: 'Quinoa cozida',
                quantity: '100g',
                calories: 120,
                protein: 4,
                carbs: 22,
                fat: 2,
                description: 'Proteína vegetal completa'
            },
            {
                name: 'Tofu grelhado',
                quantity: '100g',
                calories: 76,
                protein: 8,
                carbs: 2,
                fat: 5,
                description: 'Fonte de proteína vegetal'
            },
            {
                name: 'Lentilha cozida',
                quantity: '100g',
                calories: 116,
                protein: 9,
                carbs: 20,
                fat: 0,
                description: 'Rica em proteína e fibras'
            }
        ];
    }
    getLowCarbSuggestions() {
        return [
            {
                name: 'Salmão grelhado',
                quantity: '120g',
                calories: 231,
                protein: 25,
                carbs: 0,
                fat: 14,
                description: 'Rico em ômega-3 e proteína'
            },
            {
                name: 'Abacate',
                quantity: '1/2 unidade',
                calories: 160,
                protein: 2,
                carbs: 4,
                fat: 15,
                description: 'Gordura saudável e fibras'
            },
            {
                name: 'Brócolis refogado',
                quantity: '150g',
                calories: 51,
                protein: 4,
                carbs: 6,
                fat: 1,
                description: 'Baixo em carboidratos e nutritivo'
            }
        ];
    }
    generateDefaultSuggestions(mealType) {
        const timeOfDay = new Date().getHours();
        if (mealType === 'replacement' || timeOfDay < 10) {
            return [
                {
                    name: 'Tapioca com queijo',
                    quantity: '1 unidade',
                    calories: 180,
                    protein: 8,
                    carbs: 25,
                    fat: 6,
                    description: 'Opção leve e nutritiva para o café da manhã'
                },
                {
                    name: 'Vitamina de banana',
                    quantity: '300ml',
                    calories: 150,
                    protein: 6,
                    carbs: 30,
                    fat: 2,
                    description: 'Rica em potássio e energia'
                }
            ];
        }
        else if (timeOfDay >= 12 && timeOfDay < 15) {
            return [
                {
                    name: 'Prato balanceado',
                    quantity: '1 porção',
                    calories: 450,
                    protein: 30,
                    carbs: 45,
                    fat: 15,
                    description: 'Arroz, feijão, carne e salada'
                },
                {
                    name: 'Salada completa',
                    quantity: '1 porção',
                    calories: 320,
                    protein: 25,
                    carbs: 20,
                    fat: 18,
                    description: 'Mix de folhas, proteína e azeite'
                }
            ];
        }
        else {
            return [
                {
                    name: 'Sanduíche natural',
                    quantity: '1 unidade',
                    calories: 280,
                    protein: 15,
                    carbs: 35,
                    fat: 8,
                    description: 'Pão integral com recheio saudável'
                },
                {
                    name: 'Sopa de legumes',
                    quantity: '300ml',
                    calories: 120,
                    protein: 5,
                    carbs: 20,
                    fat: 3,
                    description: 'Leve e nutritiva'
                }
            ];
        }
    }
    async generateMealSuggestion(macros, preferences) {
        return `Sugestão baseada em ${macros.calories}kcal, ${macros.protein}g proteína`;
    }
};
exports.AIService = AIService;
exports.AIService = AIService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], AIService);


/***/ }),
/* 40 */
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),
/* 41 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UsersController = void 0;
const common_1 = __webpack_require__(2);
const jwt_auth_guard_1 = __webpack_require__(35);
const roles_guard_1 = __webpack_require__(42);
const roles_decorator_1 = __webpack_require__(43);
const users_service_1 = __webpack_require__(38);
const create_user_dto_1 = __webpack_require__(44);
const update_user_dto_1 = __webpack_require__(45);
const swagger_1 = __webpack_require__(3);
const user_role_enum_1 = __webpack_require__(14);
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    create(createUserDto) {
        return this.usersService.create(createUserDto);
    }
    findAll() {
        return this.usersService.findAll();
    }
    findOne(id) {
        return this.usersService.findOne(id);
    }
    update(id, updateUserDto) {
        return this.usersService.update(id, updateUserDto);
    }
    remove(id) {
        return this.usersService.remove(id);
    }
    findClients(req) {
        return this.usersService.getSyncedClients(req.user.id);
    }
    generateSyncCode(req) {
        return this.usersService.generateSyncCode(req.user.id);
    }
    syncWithProfessional(req, body) {
        return this.usersService.syncWithProfessional(req.user.id, body.code);
    }
    async getAIFoodSuggestions(req, body, mealId, mealType) {
        return this.usersService.getAIFoodSuggestions(req.user.id, body, mealId, mealType);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create user' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof create_user_dto_1.CreateUserDto !== "undefined" && create_user_dto_1.CreateUserDto) === "function" ? _b : Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user by id' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update user' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_c = typeof update_user_dto_1.UpdateUserDto !== "undefined" && update_user_dto_1.UpdateUserDto) === "function" ? _c : Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete user' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('professional/clients'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get professional\'s clients' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "findClients", null);
__decorate([
    (0, common_1.Post)('generate-sync-code'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Generate sync code for professional' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "generateSyncCode", null);
__decorate([
    (0, common_1.Post)('sync-with-professional'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.USER),
    (0, swagger_1.ApiOperation)({ summary: 'Sync user with professional using code' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "syncWithProfessional", null);
__decorate([
    (0, common_1.Post)('ai/foods-suggestions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get AI food suggestions' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Query)('meal_id')),
    __param(3, (0, common_1.Query)('meal_type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAIFoodSuggestions", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof users_service_1.UsersService !== "undefined" && users_service_1.UsersService) === "function" ? _a : Object])
], UsersController);


/***/ }),
/* 42 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RolesGuard = void 0;
const common_1 = __webpack_require__(2);
const core_1 = __webpack_require__(1);
let RolesGuard = class RolesGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.getAllAndOverride('roles', [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredRoles) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        return requiredRoles.includes(user.role);
    }
};
exports.RolesGuard = RolesGuard;
exports.RolesGuard = RolesGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], RolesGuard);


/***/ }),
/* 43 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Roles = void 0;
const common_1 = __webpack_require__(2);
const Roles = (...roles) => (0, common_1.SetMetadata)('roles', roles);
exports.Roles = Roles;


/***/ }),
/* 44 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateUserDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
const user_role_enum_1 = __webpack_require__(14);
class CreateUserDto {
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    __metadata("design:type", String)
], CreateUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: user_role_enum_1.UserRole }),
    (0, class_validator_1.IsEnum)(user_role_enum_1.UserRole),
    __metadata("design:type", typeof (_a = typeof user_role_enum_1.UserRole !== "undefined" && user_role_enum_1.UserRole) === "function" ? _a : Object)
], CreateUserDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "photo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], CreateUserDto.prototype, "birthDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateUserDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateUserDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDto.prototype, "activityLevel", void 0);


/***/ }),
/* 45 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateUserDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_user_dto_1 = __webpack_require__(44);
class UpdateUserDto extends (0, swagger_1.PartialType)(create_user_dto_1.CreateUserDto) {
}
exports.UpdateUserDto = UpdateUserDto;


/***/ }),
/* 46 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AIModule = void 0;
const common_1 = __webpack_require__(2);
const config_1 = __webpack_require__(6);
const ai_service_1 = __webpack_require__(39);
let AIModule = class AIModule {
};
exports.AIModule = AIModule;
exports.AIModule = AIModule = __decorate([
    (0, common_1.Module)({
        imports: [config_1.ConfigModule],
        providers: [ai_service_1.AIService],
        exports: [ai_service_1.AIService],
    })
], AIModule);


/***/ }),
/* 47 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtStrategy = void 0;
const passport_jwt_1 = __webpack_require__(48);
const passport_1 = __webpack_require__(10);
const common_1 = __webpack_require__(2);
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor() {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: process.env.JWT_SECRET || 'your-secret-key',
        });
    }
    async validate(payload) {
        return { id: payload.sub, email: payload.email, role: payload.role };
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], JwtStrategy);


/***/ }),
/* 48 */
/***/ ((module) => {

module.exports = require("passport-jwt");

/***/ }),
/* 49 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LocalStrategy = void 0;
const passport_local_1 = __webpack_require__(50);
const passport_1 = __webpack_require__(10);
const common_1 = __webpack_require__(2);
const auth_service_1 = __webpack_require__(11);
let LocalStrategy = class LocalStrategy extends (0, passport_1.PassportStrategy)(passport_local_1.Strategy) {
    constructor(authService) {
        super({
            usernameField: 'email',
            passwordField: 'password'
        });
        this.authService = authService;
    }
    async validate(email, password) {
        try {
            const user = await this.authService.validateUser(email, password);
            if (!user) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            return user;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
};
exports.LocalStrategy = LocalStrategy;
exports.LocalStrategy = LocalStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], LocalStrategy);


/***/ }),
/* 50 */
/***/ ((module) => {

module.exports = require("passport-local");

/***/ }),
/* 51 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MailModule = void 0;
const common_1 = __webpack_require__(2);
const mail_service_1 = __webpack_require__(26);
let MailModule = class MailModule {
};
exports.MailModule = MailModule;
exports.MailModule = MailModule = __decorate([
    (0, common_1.Module)({
        providers: [mail_service_1.MailService],
        exports: [mail_service_1.MailService],
    })
], MailModule);


/***/ }),
/* 52 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SmsModule = void 0;
const common_1 = __webpack_require__(2);
const sms_service_1 = __webpack_require__(27);
let SmsModule = class SmsModule {
};
exports.SmsModule = SmsModule;
exports.SmsModule = SmsModule = __decorate([
    (0, common_1.Module)({
        providers: [sms_service_1.SmsService],
        exports: [sms_service_1.SmsService],
    })
], SmsModule);


/***/ }),
/* 53 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProtocolsModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const protocols_service_1 = __webpack_require__(54);
const protocols_controller_1 = __webpack_require__(55);
const professional_protocols_service_1 = __webpack_require__(62);
const professional_protocols_controller_1 = __webpack_require__(63);
const protocol_entity_1 = __webpack_require__(20);
const user_entity_1 = __webpack_require__(13);
let ProtocolsModule = class ProtocolsModule {
};
exports.ProtocolsModule = ProtocolsModule;
exports.ProtocolsModule = ProtocolsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([protocol_entity_1.Protocol, user_entity_1.User])
        ],
        providers: [protocols_service_1.ProtocolsService, professional_protocols_service_1.ProfessionalProtocolsService],
        controllers: [protocols_controller_1.ProtocolsController, professional_protocols_controller_1.ProfessionalProtocolsController],
        exports: [protocols_service_1.ProtocolsService, professional_protocols_service_1.ProfessionalProtocolsService],
    })
], ProtocolsModule);


/***/ }),
/* 54 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProtocolsService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const protocol_entity_1 = __webpack_require__(20);
const user_role_enum_1 = __webpack_require__(14);
const protocol_type_enum_1 = __webpack_require__(21);
let ProtocolsService = class ProtocolsService {
    constructor(protocolsRepository) {
        this.protocolsRepository = protocolsRepository;
    }
    async create(createProtocolDto, creatorId) {
        const protocol = this.protocolsRepository.create({
            ...createProtocolDto,
            createdBy: { id: creatorId },
            user: createProtocolDto.userId ? { id: createProtocolDto.userId } : undefined,
        });
        return this.protocolsRepository.save(protocol);
    }
    async createWorkoutProtocol(createWorkoutDto, creatorId) {
        const protocolData = {
            name: createWorkoutDto.name,
            type: protocol_type_enum_1.ProtocolType.WORKOUT,
            objective: createWorkoutDto.objective,
            startDate: createWorkoutDto.startDate,
            endDate: createWorkoutDto.endDate,
            notes: createWorkoutDto.notes || '',
            content: {
                protocolType: createWorkoutDto.protocolType,
                split: createWorkoutDto.split,
                frequency: createWorkoutDto.frequency,
                workouts: createWorkoutDto.workouts
            }
        };
        const protocol = this.protocolsRepository.create({
            ...protocolData,
            createdBy: { id: creatorId },
            user: createWorkoutDto.userId ? { id: createWorkoutDto.userId } : { id: creatorId },
        });
        return this.protocolsRepository.save(protocol);
    }
    async createDietProtocol(createDietDto, creatorId) {
        const protocolData = {
            name: createDietDto.name,
            type: protocol_type_enum_1.ProtocolType.DIET,
            objective: createDietDto.objective,
            startDate: createDietDto.startDate,
            endDate: createDietDto.endDate,
            notes: createDietDto.notes || '',
            content: {
                goals: createDietDto.goals,
                waterCalculationMethod: createDietDto.waterCalculationMethod || 'weight',
                weeklyMeals: createDietDto.weeklyMeals,
                supplements: createDietDto.supplements
            }
        };
        const protocol = this.protocolsRepository.create({
            ...protocolData,
            createdBy: { id: creatorId },
            user: createDietDto.userId ? { id: createDietDto.userId } : { id: creatorId },
        });
        return this.protocolsRepository.save(protocol);
    }
    async findAll(userId, role) {
        const query = this.protocolsRepository.createQueryBuilder('protocol')
            .leftJoinAndSelect('protocol.user', 'user')
            .leftJoinAndSelect('protocol.createdBy', 'createdBy');
        if (role === user_role_enum_1.UserRole.USER) {
            query.where('protocol.user.id = :userId', { userId });
        }
        else if (role === user_role_enum_1.UserRole.COACH) {
            query.where('protocol.createdBy.id = :userId AND protocol.type = :type', {
                userId,
                type: protocol_type_enum_1.ProtocolType.WORKOUT,
            });
        }
        else if (role === user_role_enum_1.UserRole.NUTRITIONIST) {
            query.where('protocol.createdBy.id = :userId AND protocol.type = :type', {
                userId,
                type: protocol_type_enum_1.ProtocolType.DIET,
            });
        }
        return query.getMany();
    }
    async findOne(id, userId, role) {
        const protocol = await this.protocolsRepository.findOne({
            where: { id },
            relations: ['user', 'createdBy'],
        });
        if (!protocol) {
            throw new common_1.NotFoundException(`Protocol with ID ${id} not found`);
        }
        if (role === user_role_enum_1.UserRole.USER && protocol.user.id !== userId) {
            throw new common_1.ForbiddenException('You do not have access to this protocol');
        }
        if ((role === user_role_enum_1.UserRole.COACH || role === user_role_enum_1.UserRole.NUTRITIONIST) &&
            protocol.createdBy.id !== userId) {
            throw new common_1.ForbiddenException('You do not have access to this protocol');
        }
        return protocol;
    }
    async update(id, updateProtocolDto, userId, role) {
        const protocol = await this.findOne(id, userId, role);
        Object.assign(protocol, updateProtocolDto);
        return this.protocolsRepository.save(protocol);
    }
    async remove(id, userId, role) {
        const protocol = await this.findOne(id, userId, role);
        await this.protocolsRepository.remove(protocol);
    }
    async completeProtocol(id, userId, role) {
        const protocol = await this.findOne(id, userId, role);
        protocol.status = 'completed';
        protocol.endDate = new Date();
        return this.protocolsRepository.save(protocol);
    }
    async archiveProtocol(id, userId, role) {
        const protocol = await this.findOne(id, userId, role);
        protocol.status = 'archived';
        return this.protocolsRepository.save(protocol);
    }
    async getProtocolHistory(userId, role, filters) {
        const query = this.protocolsRepository.createQueryBuilder('protocol')
            .leftJoinAndSelect('protocol.user', 'user')
            .leftJoinAndSelect('protocol.createdBy', 'createdBy')
            .orderBy('protocol.createdAt', 'DESC');
        if (role === user_role_enum_1.UserRole.USER) {
            query.where('protocol.user.id = :userId', { userId });
        }
        else if (role === user_role_enum_1.UserRole.COACH) {
            query.where('protocol.createdBy.id = :userId AND protocol.type = :type', {
                userId,
                type: protocol_type_enum_1.ProtocolType.WORKOUT,
            });
        }
        else if (role === user_role_enum_1.UserRole.NUTRITIONIST) {
            query.where('protocol.createdBy.id = :userId AND protocol.type = :type', {
                userId,
                type: protocol_type_enum_1.ProtocolType.DIET,
            });
        }
        if (filters.type) {
            query.andWhere('protocol.type = :filterType', { filterType: filters.type });
        }
        if (filters.status) {
            query.andWhere('protocol.status = :status', { status: filters.status });
        }
        const offset = (filters.page - 1) * filters.limit;
        query.skip(offset).take(filters.limit + 1);
        const protocols = await query.getMany();
        const hasMore = protocols.length > filters.limit;
        if (hasMore) {
            protocols.pop();
        }
        const total = await query.getCount();
        return {
            protocols,
            total,
            hasMore
        };
    }
    async duplicateProtocol(id, userId, role, newName) {
        const originalProtocol = await this.findOne(id, userId, role);
        const duplicatedProtocol = this.protocolsRepository.create({
            name: newName || `${originalProtocol.name} (Cópia)`,
            type: originalProtocol.type,
            objective: originalProtocol.objective,
            content: originalProtocol.content,
            notes: originalProtocol.notes,
            startDate: new Date(),
            endDate: undefined,
            status: 'active',
            user: originalProtocol.user,
            userId: originalProtocol.userId,
            createdBy: originalProtocol.createdBy,
            createdById: originalProtocol.createdById,
        });
        return await this.protocolsRepository.save(duplicatedProtocol);
    }
};
exports.ProtocolsService = ProtocolsService;
exports.ProtocolsService = ProtocolsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(protocol_entity_1.Protocol)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object])
], ProtocolsService);


/***/ }),
/* 55 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProtocolsController = void 0;
const common_1 = __webpack_require__(2);
const jwt_auth_guard_1 = __webpack_require__(35);
const roles_guard_1 = __webpack_require__(42);
const roles_decorator_1 = __webpack_require__(43);
const protocols_service_1 = __webpack_require__(54);
const create_protocol_dto_1 = __webpack_require__(56);
const create_workout_protocol_dto_1 = __webpack_require__(58);
const create_diet_protocol_dto_1 = __webpack_require__(59);
const update_protocol_dto_1 = __webpack_require__(60);
const duplicate_protocol_dto_1 = __webpack_require__(61);
const swagger_1 = __webpack_require__(3);
const user_role_enum_1 = __webpack_require__(14);
const protocol_type_enum_1 = __webpack_require__(21);
let ProtocolsController = class ProtocolsController {
    constructor(protocolsService) {
        this.protocolsService = protocolsService;
    }
    create(req, createProtocolDto) {
        return this.protocolsService.create(createProtocolDto, req.user.id);
    }
    createWorkoutProtocol(req, createWorkoutDto) {
        return this.protocolsService.createWorkoutProtocol(createWorkoutDto, req.user.id);
    }
    createDietProtocol(req, createDietDto) {
        return this.protocolsService.createDietProtocol(createDietDto, req.user.id);
    }
    findAll(req) {
        return this.protocolsService.findAll(req.user.id, req.user.role);
    }
    findOne(req, id) {
        return this.protocolsService.findOne(id, req.user.id, req.user.role);
    }
    update(req, id, updateProtocolDto) {
        return this.protocolsService.update(id, updateProtocolDto, req.user.id, req.user.role);
    }
    remove(req, id) {
        return this.protocolsService.remove(id, req.user.id, req.user.role);
    }
    complete(req, id) {
        return this.protocolsService.completeProtocol(id, req.user.id, req.user.role);
    }
    archive(req, id) {
        return this.protocolsService.archiveProtocol(id, req.user.id, req.user.role);
    }
    getHistory(req, type, status, page = 1, limit = 10) {
        return this.protocolsService.getProtocolHistory(req.user.id, req.user.role, {
            type,
            status,
            page,
            limit
        });
    }
    duplicate(req, id, duplicateDto) {
        return this.protocolsService.duplicateProtocol(id, req.user.id, req.user.role, duplicateDto.name);
    }
};
exports.ProtocolsController = ProtocolsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_b = typeof create_protocol_dto_1.CreateProtocolDto !== "undefined" && create_protocol_dto_1.CreateProtocolDto) === "function" ? _b : Object]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('workout'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create workout protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_c = typeof create_workout_protocol_dto_1.CreateWorkoutProtocolDto !== "undefined" && create_workout_protocol_dto_1.CreateWorkoutProtocolDto) === "function" ? _c : Object]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "createWorkoutProtocol", null);
__decorate([
    (0, common_1.Post)('diet'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create diet protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_d = typeof create_diet_protocol_dto_1.CreateDietProtocolDto !== "undefined" && create_diet_protocol_dto_1.CreateDietProtocolDto) === "function" ? _d : Object]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "createDietProtocol", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all protocols' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get protocol by id' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Update protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_e = typeof update_protocol_dto_1.UpdateProtocolDto !== "undefined" && update_protocol_dto_1.UpdateProtocolDto) === "function" ? _e : Object]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Delete protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/complete'),
    (0, swagger_1.ApiOperation)({ summary: 'Complete protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "complete", null);
__decorate([
    (0, common_1.Post)(':id/archive'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Archive protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "archive", null);
__decorate([
    (0, common_1.Get)('history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get protocol history' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('status')),
    __param(3, (0, common_1.Query)('page')),
    __param(4, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_f = typeof protocol_type_enum_1.ProtocolType !== "undefined" && protocol_type_enum_1.ProtocolType) === "function" ? _f : Object, String, Number, Number]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "getHistory", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_g = typeof duplicate_protocol_dto_1.DuplicateProtocolDto !== "undefined" && duplicate_protocol_dto_1.DuplicateProtocolDto) === "function" ? _g : Object]),
    __metadata("design:returntype", void 0)
], ProtocolsController.prototype, "duplicate", null);
exports.ProtocolsController = ProtocolsController = __decorate([
    (0, swagger_1.ApiTags)('protocols'),
    (0, common_1.Controller)('protocols'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof protocols_service_1.ProtocolsService !== "undefined" && protocols_service_1.ProtocolsService) === "function" ? _a : Object])
], ProtocolsController);


/***/ }),
/* 56 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateWorkoutProtocolDto = exports.WorkoutDayDto = exports.WorkoutExerciseDto = exports.CreateProtocolDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
const class_transformer_1 = __webpack_require__(57);
const protocol_type_enum_1 = __webpack_require__(21);
class CreateProtocolDto {
}
exports.CreateProtocolDto = CreateProtocolDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: protocol_type_enum_1.ProtocolType, description: 'Protocol type: workout or diet' }),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            return value === 'workout' ? protocol_type_enum_1.ProtocolType.WORKOUT :
                value === 'diet' ? protocol_type_enum_1.ProtocolType.DIET : value;
        }
        return value;
    }),
    (0, class_validator_1.IsEnum)(protocol_type_enum_1.ProtocolType),
    __metadata("design:type", typeof (_a = typeof protocol_type_enum_1.ProtocolType !== "undefined" && protocol_type_enum_1.ProtocolType) === "function" ? _a : Object)
], CreateProtocolDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "objective", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateProtocolDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "userId", void 0);
class WorkoutExerciseDto {
}
exports.WorkoutExerciseDto = WorkoutExerciseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkoutExerciseDto.prototype, "exerciseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkoutExerciseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "sets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "reps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "rpe", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "restTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WorkoutExerciseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "orderIndex", void 0);
class WorkoutDayDto {
}
exports.WorkoutDayDto = WorkoutDayDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkoutDayDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [WorkoutExerciseDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => WorkoutExerciseDto),
    __metadata("design:type", Array)
], WorkoutDayDto.prototype, "exercises", void 0);
class CreateWorkoutProtocolDto extends CreateProtocolDto {
}
exports.CreateWorkoutProtocolDto = CreateWorkoutProtocolDto;
__decorate([
    (0, swagger_1.ApiProperty)({ enum: protocol_type_enum_1.ProtocolType, default: protocol_type_enum_1.ProtocolType.WORKOUT }),
    __metadata("design:type", typeof (_b = typeof protocol_type_enum_1.ProtocolType !== "undefined" && protocol_type_enum_1.ProtocolType.WORKOUT) === "function" ? _b : Object)
], CreateWorkoutProtocolDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "protocolType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "split", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], CreateWorkoutProtocolDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [WorkoutDayDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => WorkoutDayDto),
    __metadata("design:type", Array)
], CreateWorkoutProtocolDto.prototype, "workouts", void 0);


/***/ }),
/* 57 */
/***/ ((module) => {

module.exports = require("class-transformer");

/***/ }),
/* 58 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateWorkoutProtocolDto = exports.WorkoutDayDto = exports.WorkoutExerciseDto = void 0;
const class_validator_1 = __webpack_require__(30);
const class_transformer_1 = __webpack_require__(57);
const swagger_1 = __webpack_require__(3);
class WorkoutExerciseDto {
}
exports.WorkoutExerciseDto = WorkoutExerciseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkoutExerciseDto.prototype, "exerciseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkoutExerciseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "sets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "reps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "rpe", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "restTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WorkoutExerciseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkoutExerciseDto.prototype, "orderIndex", void 0);
class WorkoutDayDto {
}
exports.WorkoutDayDto = WorkoutDayDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkoutDayDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [WorkoutExerciseDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => WorkoutExerciseDto),
    __metadata("design:type", Array)
], WorkoutDayDto.prototype, "exercises", void 0);
class CreateWorkoutProtocolDto {
}
exports.CreateWorkoutProtocolDto = CreateWorkoutProtocolDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['workout'], description: 'Must be "workout"' }),
    (0, class_validator_1.IsIn)(['workout']),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "protocolType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "objective", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "split", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Can be string or number' }),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'number')
            return value.toString();
        return value;
    }),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [WorkoutDayDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => WorkoutDayDto),
    __metadata("design:type", Array)
], CreateWorkoutProtocolDto.prototype, "workouts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateWorkoutProtocolDto.prototype, "userId", void 0);


/***/ }),
/* 59 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateDietProtocolDto = exports.GoalsDto = exports.SupplementDto = exports.MealDto = exports.FoodDto = void 0;
const class_validator_1 = __webpack_require__(30);
const class_transformer_1 = __webpack_require__(57);
const swagger_1 = __webpack_require__(3);
class FoodDto {
}
exports.FoodDto = FoodDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FoodDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FoodDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FoodDto.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FoodDto.prototype, "calories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FoodDto.prototype, "protein", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FoodDto.prototype, "carbs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FoodDto.prototype, "fat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], FoodDto.prototype, "fiber", void 0);
class MealDto {
}
exports.MealDto = MealDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MealDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MealDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [FoodDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => FoodDto),
    __metadata("design:type", Array)
], MealDto.prototype, "foods", void 0);
class SupplementDto {
}
exports.SupplementDto = SupplementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SupplementDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SupplementDto.prototype, "dosage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SupplementDto.prototype, "timing", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SupplementDto.prototype, "notes", void 0);
class GoalsDto {
}
exports.GoalsDto = GoalsDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GoalsDto.prototype, "calories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GoalsDto.prototype, "protein", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GoalsDto.prototype, "carbs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GoalsDto.prototype, "fat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GoalsDto.prototype, "water", void 0);
class CreateDietProtocolDto {
}
exports.CreateDietProtocolDto = CreateDietProtocolDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['diet'], description: 'Must be "diet"' }),
    (0, class_validator_1.IsIn)(['diet']),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "objective", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: GoalsDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => GoalsDto),
    __metadata("design:type", GoalsDto)
], CreateDietProtocolDto.prototype, "goals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ['weight', 'manual'] }),
    (0, class_validator_1.IsIn)(['weight', 'manual']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "waterCalculationMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Weekly meals organized by day',
        example: {
            monday: [{ name: 'Breakfast', time: '08:00', foods: [] }],
            tuesday: [{ name: 'Breakfast', time: '08:00', foods: [] }]
        }
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateDietProtocolDto.prototype, "weeklyMeals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [SupplementDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => SupplementDto),
    __metadata("design:type", Array)
], CreateDietProtocolDto.prototype, "supplements", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateDietProtocolDto.prototype, "userId", void 0);


/***/ }),
/* 60 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateProtocolDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_protocol_dto_1 = __webpack_require__(56);
class UpdateProtocolDto extends (0, swagger_1.PartialType)(create_protocol_dto_1.CreateProtocolDto) {
}
exports.UpdateProtocolDto = UpdateProtocolDto;


/***/ }),
/* 61 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DuplicateProtocolDto = void 0;
const swagger_1 = __webpack_require__(3);
const class_validator_1 = __webpack_require__(30);
class DuplicateProtocolDto {
}
exports.DuplicateProtocolDto = DuplicateProtocolDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New name for the duplicated protocol',
        required: false,
        example: 'My Protocol (Copy)'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DuplicateProtocolDto.prototype, "name", void 0);


/***/ }),
/* 62 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProfessionalProtocolsService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const protocol_entity_1 = __webpack_require__(20);
const user_entity_1 = __webpack_require__(13);
const user_role_enum_1 = __webpack_require__(14);
const protocol_type_enum_1 = __webpack_require__(21);
let ProfessionalProtocolsService = class ProfessionalProtocolsService {
    constructor(protocolRepository, userRepository) {
        this.protocolRepository = protocolRepository;
        this.userRepository = userRepository;
    }
    async createProtocolForClient(professionalId, createProtocolDto) {
        const professional = await this.userRepository.findOne({
            where: { id: professionalId },
            select: ['id', 'role', 'name']
        });
        if (!professional) {
            throw new common_1.NotFoundException('Professional not found');
        }
        const client = await this.userRepository.findOne({
            where: { id: createProtocolDto.clientId },
            relations: ['coach', 'nutritionist']
        });
        if (!client) {
            throw new common_1.NotFoundException('Client not found');
        }
        const hasAccess = (professional.role === user_role_enum_1.UserRole.COACH && client.coach?.id === professionalId) ||
            (professional.role === user_role_enum_1.UserRole.NUTRITIONIST && client.nutritionist?.id === professionalId);
        if (!hasAccess) {
            throw new common_1.ForbiddenException('You do not have access to this client');
        }
        if (professional.role === user_role_enum_1.UserRole.COACH && createProtocolDto.type !== protocol_type_enum_1.ProtocolType.WORKOUT) {
            throw new common_1.ForbiddenException('Coaches can only create workout protocols');
        }
        if (professional.role === user_role_enum_1.UserRole.NUTRITIONIST && createProtocolDto.type !== protocol_type_enum_1.ProtocolType.DIET) {
            throw new common_1.ForbiddenException('Nutritionists can only create diet protocols');
        }
        const protocol = this.protocolRepository.create({
            name: createProtocolDto.name,
            type: createProtocolDto.type,
            objective: createProtocolDto.objective,
            content: createProtocolDto.content,
            notes: createProtocolDto.notes,
            startDate: createProtocolDto.startDate,
            endDate: createProtocolDto.endDate,
            user: client,
            createdBy: professional,
            status: 'pending_import'
        });
        const savedProtocol = await this.protocolRepository.save(protocol);
        return savedProtocol;
    }
    async getAvailableProtocolsForClient(clientId) {
        return this.protocolRepository.find({
            where: {
                user: { id: clientId },
                status: 'pending_import'
            },
            relations: ['createdBy'],
            order: {
                createdAt: 'DESC'
            }
        });
    }
    async importProtocol(clientId, protocolId) {
        const protocol = await this.protocolRepository.findOne({
            where: {
                id: protocolId,
                user: { id: clientId },
                status: 'pending_import'
            },
            relations: ['user', 'createdBy']
        });
        if (!protocol) {
            throw new common_1.NotFoundException('Protocol not found or already imported');
        }
        await this.protocolRepository.update({
            user: { id: clientId },
            type: protocol.type,
            status: 'active'
        }, { status: 'archived' });
        protocol.status = 'active';
        const savedProtocol = await this.protocolRepository.save(protocol);
        if (protocol.type === protocol_type_enum_1.ProtocolType.DIET) {
            await this.userRepository.update({ id: clientId }, { currentDietProtocol: protocol });
        }
        return savedProtocol;
    }
    async getProtocolHistory(clientId, type) {
        const whereCondition = {
            user: { id: clientId },
            status: 'archived'
        };
        if (type) {
            whereCondition.type = type;
        }
        return this.protocolRepository.find({
            where: whereCondition,
            relations: ['createdBy'],
            order: {
                updatedAt: 'DESC'
            }
        });
    }
    async generateWhatsAppMessage(protocolId) {
        const protocol = await this.protocolRepository.findOne({
            where: { id: protocolId },
            relations: ['user', 'createdBy']
        });
        if (!protocol) {
            throw new common_1.NotFoundException('Protocol not found');
        }
        const protocolTypeText = protocol.type === protocol_type_enum_1.ProtocolType.WORKOUT ? 'treino' : 'dieta';
        const message = `Olá ${protocol.user.name}! 👋

Criei um novo protocolo de ${protocolTypeText} para você no SnapFit:

📋 *${protocol.name}*
🎯 Objetivo: ${protocol.objective}

Para acessar e importar seu protocolo:
1. Abra o SnapFit
2. Vá em ${protocol.type === protocol_type_enum_1.ProtocolType.WORKOUT ? 'Treino' : 'Dieta'} > Importar Protocolo
3. Você verá o novo protocolo disponível

Qualquer dúvida, estou aqui para ajudar! 💪`;
        return encodeURIComponent(message);
    }
};
exports.ProfessionalProtocolsService = ProfessionalProtocolsService;
exports.ProfessionalProtocolsService = ProfessionalProtocolsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(protocol_entity_1.Protocol)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object])
], ProfessionalProtocolsService);


/***/ }),
/* 63 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ProfessionalProtocolsController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const jwt_auth_guard_1 = __webpack_require__(35);
const roles_guard_1 = __webpack_require__(42);
const roles_decorator_1 = __webpack_require__(43);
const user_role_enum_1 = __webpack_require__(14);
const professional_protocols_service_1 = __webpack_require__(62);
const protocol_type_enum_1 = __webpack_require__(21);
let ProfessionalProtocolsController = class ProfessionalProtocolsController {
    constructor(professionalProtocolsService) {
        this.professionalProtocolsService = professionalProtocolsService;
    }
    createProtocolForClient(req, createProtocolDto) {
        return this.professionalProtocolsService.createProtocolForClient(req.user.id, createProtocolDto);
    }
    getAvailableProtocols(req) {
        return this.professionalProtocolsService.getAvailableProtocolsForClient(req.user.id);
    }
    importProtocol(req, protocolId) {
        return this.professionalProtocolsService.importProtocol(req.user.id, protocolId);
    }
    getProtocolHistory(req, type) {
        return this.professionalProtocolsService.getProtocolHistory(req.user.id, type);
    }
    generateWhatsAppMessage(protocolId) {
        return this.professionalProtocolsService.generateWhatsAppMessage(protocolId);
    }
};
exports.ProfessionalProtocolsController = ProfessionalProtocolsController;
__decorate([
    (0, common_1.Post)('create-for-client'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create protocol for client' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_b = typeof professional_protocols_service_1.CreateProtocolForClientDto !== "undefined" && professional_protocols_service_1.CreateProtocolForClientDto) === "function" ? _b : Object]),
    __metadata("design:returntype", void 0)
], ProfessionalProtocolsController.prototype, "createProtocolForClient", null);
__decorate([
    (0, common_1.Get)('available'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.USER),
    (0, swagger_1.ApiOperation)({ summary: 'Get available protocols for import' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProfessionalProtocolsController.prototype, "getAvailableProtocols", null);
__decorate([
    (0, common_1.Post)('import/:protocolId'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.USER),
    (0, swagger_1.ApiOperation)({ summary: 'Import protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('protocolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ProfessionalProtocolsController.prototype, "importProtocol", null);
__decorate([
    (0, common_1.Get)('history'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.USER),
    (0, swagger_1.ApiOperation)({ summary: 'Get protocol history' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_c = typeof protocol_type_enum_1.ProtocolType !== "undefined" && protocol_type_enum_1.ProtocolType) === "function" ? _c : Object]),
    __metadata("design:returntype", void 0)
], ProfessionalProtocolsController.prototype, "getProtocolHistory", null);
__decorate([
    (0, common_1.Get)('whatsapp-message/:protocolId'),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.COACH, user_role_enum_1.UserRole.NUTRITIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Generate WhatsApp message for protocol' }),
    __param(0, (0, common_1.Param)('protocolId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProfessionalProtocolsController.prototype, "generateWhatsAppMessage", null);
exports.ProfessionalProtocolsController = ProfessionalProtocolsController = __decorate([
    (0, swagger_1.ApiTags)('professional-protocols'),
    (0, common_1.Controller)('professional-protocols'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof professional_protocols_service_1.ProfessionalProtocolsService !== "undefined" && professional_protocols_service_1.ProfessionalProtocolsService) === "function" ? _a : Object])
], ProfessionalProtocolsController);


/***/ }),
/* 64 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DietModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const diet_controller_1 = __webpack_require__(65);
const diet_service_1 = __webpack_require__(66);
const protocol_entity_1 = __webpack_require__(67);
const meal_entity_1 = __webpack_require__(68);
const food_entity_1 = __webpack_require__(69);
const supplement_entity_1 = __webpack_require__(70);
const shopping_list_entity_1 = __webpack_require__(71);
let DietModule = class DietModule {
};
exports.DietModule = DietModule;
exports.DietModule = DietModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                protocol_entity_1.Protocol,
                meal_entity_1.Meal,
                food_entity_1.Food,
                supplement_entity_1.Supplement,
                shopping_list_entity_1.ShoppingList
            ])
        ],
        controllers: [diet_controller_1.DietController],
        providers: [diet_service_1.DietService],
        exports: [diet_service_1.DietService]
    })
], DietModule);


/***/ }),
/* 65 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DietController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const jwt_auth_guard_1 = __webpack_require__(35);
const diet_service_1 = __webpack_require__(66);
const create_protocol_dto_1 = __webpack_require__(72);
const update_protocol_dto_1 = __webpack_require__(74);
const create_supplement_dto_1 = __webpack_require__(73);
const create_shopping_list_dto_1 = __webpack_require__(75);
const duplicate_protocol_dto_1 = __webpack_require__(61);
let DietController = class DietController {
    constructor(dietService) {
        this.dietService = dietService;
    }
    async getCurrentProtocol(req) {
        try {
            console.log('Getting current protocol for user:', req.user?.id);
            const result = await this.dietService.getCurrentProtocol(req.user.id);
            console.log('Current protocol found:', result ? 'Yes' : 'No', result?.id);
            return result;
        }
        catch (error) {
            console.error('Error getting current protocol:', error.message);
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async createProtocol(req, createProtocolDto) {
        try {
            return await this.dietService.createProtocol(req.user.id, createProtocolDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getProtocol(req, id) {
        try {
            console.log('Getting protocol with ID:', id, 'for user:', req.user?.id);
            const result = await this.dietService.getProtocol(id, req.user.id);
            console.log('Protocol found:', result ? 'Yes' : 'No');
            return result;
        }
        catch (error) {
            console.error('Error getting protocol:', error.message);
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateProtocol(req, id, updateProtocolDto) {
        try {
            return await this.dietService.updateProtocol(id, req.user.id, updateProtocolDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async addSupplement(id, createSupplementDto) {
        try {
            return await this.dietService.addSupplement(id, createSupplementDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateSupplement(id, updateData) {
        try {
            return await this.dietService.updateSupplement(id, updateData);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async removeSupplement(id) {
        try {
            return await this.dietService.removeSupplement(id);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async generateShoppingList(req, createShoppingListDto) {
        try {
            return await this.dietService.generateShoppingList(req.user.id, createShoppingListDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getShoppingLists(req) {
        try {
            return await this.dietService.getShoppingLists(req.user.id);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getShoppingList(req, id) {
        try {
            return await this.dietService.getShoppingList(id, req.user.id);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getProtocolsHistory(req, page = 1, limit = 10, status) {
        try {
            return await this.dietService.getProtocolsHistory(req.user.id, {
                page,
                limit,
                status
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async duplicateProtocol(req, id, duplicateDto) {
        try {
            return await this.dietService.duplicateProtocol(id, req.user.id, duplicateDto.name);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.DietController = DietController;
__decorate([
    (0, common_1.Get)('protocol/current'),
    (0, swagger_1.ApiOperation)({ summary: 'Get current diet protocol' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "getCurrentProtocol", null);
__decorate([
    (0, common_1.Post)('protocols'),
    (0, swagger_1.ApiOperation)({ summary: 'Create diet protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_b = typeof create_protocol_dto_1.CreateProtocolDto !== "undefined" && create_protocol_dto_1.CreateProtocolDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "createProtocol", null);
__decorate([
    (0, common_1.Get)('protocols/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get diet protocol by id' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "getProtocol", null);
__decorate([
    (0, common_1.Put)('protocols/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update diet protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_c = typeof update_protocol_dto_1.UpdateProtocolDto !== "undefined" && update_protocol_dto_1.UpdateProtocolDto) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "updateProtocol", null);
__decorate([
    (0, common_1.Post)('protocols/:id/supplements'),
    (0, swagger_1.ApiOperation)({ summary: 'Add supplement to protocol' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_d = typeof create_supplement_dto_1.CreateSupplementDto !== "undefined" && create_supplement_dto_1.CreateSupplementDto) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "addSupplement", null);
__decorate([
    (0, common_1.Put)('supplements/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update supplement' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "updateSupplement", null);
__decorate([
    (0, common_1.Delete)('supplements/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Remove supplement' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "removeSupplement", null);
__decorate([
    (0, common_1.Post)('shopping-lists'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate shopping list' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_e = typeof create_shopping_list_dto_1.CreateShoppingListDto !== "undefined" && create_shopping_list_dto_1.CreateShoppingListDto) === "function" ? _e : Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "generateShoppingList", null);
__decorate([
    (0, common_1.Get)('shopping-lists'),
    (0, swagger_1.ApiOperation)({ summary: 'Get shopping lists' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "getShoppingLists", null);
__decorate([
    (0, common_1.Get)('shopping-lists/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get shopping list by id' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "getShoppingList", null);
__decorate([
    (0, common_1.Get)('protocols/history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get diet protocols history' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, String]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "getProtocolsHistory", null);
__decorate([
    (0, common_1.Post)('protocols/:id/duplicate'),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate diet protocol' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_f = typeof duplicate_protocol_dto_1.DuplicateProtocolDto !== "undefined" && duplicate_protocol_dto_1.DuplicateProtocolDto) === "function" ? _f : Object]),
    __metadata("design:returntype", Promise)
], DietController.prototype, "duplicateProtocol", null);
exports.DietController = DietController = __decorate([
    (0, swagger_1.ApiTags)('diet'),
    (0, common_1.Controller)('diet'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof diet_service_1.DietService !== "undefined" && diet_service_1.DietService) === "function" ? _a : Object])
], DietController);


/***/ }),
/* 66 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DietService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const protocol_entity_1 = __webpack_require__(67);
const meal_entity_1 = __webpack_require__(68);
const food_entity_1 = __webpack_require__(69);
const supplement_entity_1 = __webpack_require__(70);
const shopping_list_entity_1 = __webpack_require__(71);
let DietService = class DietService {
    constructor(dataSource, protocolRepository, mealRepository, foodRepository, supplementRepository, shoppingListRepository) {
        this.dataSource = dataSource;
        this.protocolRepository = protocolRepository;
        this.mealRepository = mealRepository;
        this.foodRepository = foodRepository;
        this.supplementRepository = supplementRepository;
        this.shoppingListRepository = shoppingListRepository;
    }
    async getCurrentProtocol(userId) {
        const protocol = await this.protocolRepository.findOne({
            where: {
                userId,
                status: 'active',
                type: 'diet'
            },
            order: { startDate: 'DESC' },
            relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
        });
        if (!protocol) {
            throw new common_1.NotFoundException('No active diet protocol found');
        }
        return protocol;
    }
    async getProtocol(id, userId) {
        const protocol = await this.protocolRepository.findOne({
            where: { id, userId },
            relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
        });
        if (!protocol) {
            throw new common_1.NotFoundException('Diet protocol not found');
        }
        return protocol;
    }
    async createProtocol(userId, createProtocolDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const { weeklyMeals, supplements, ...protocolData } = createProtocolDto;
            const protocol = this.protocolRepository.create({
                ...protocolData,
                userId,
                type: 'diet',
                dietType: createProtocolDto.dietType
            });
            await queryRunner.manager.save(protocol);
            for (const [day, meals] of Object.entries(createProtocolDto.weeklyMeals)) {
                for (const meal of meals) {
                    const newMeal = this.mealRepository.create({
                        ...meal,
                        protocolId: protocol.id,
                        weekDay: day
                    });
                    await queryRunner.manager.save(newMeal);
                    for (const food of meal.foods) {
                        const newFood = this.foodRepository.create({
                            ...food,
                            mealId: newMeal.id
                        });
                        await queryRunner.manager.save(newFood);
                    }
                }
            }
            for (const supplement of createProtocolDto.supplements) {
                const newSupplement = this.supplementRepository.create({
                    ...supplement,
                    protocolId: protocol.id
                });
                await queryRunner.manager.save(newSupplement);
            }
            await queryRunner.commitTransaction();
            return protocol;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async updateProtocol(id, userId, updateProtocolDto) {
        const protocol = await this.protocolRepository.findOne({
            where: { id, userId }
        });
        if (!protocol) {
            throw new common_1.NotFoundException('Protocol not found');
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            Object.assign(protocol, updateProtocolDto);
            await queryRunner.manager.save(protocol);
            if (updateProtocolDto.weeklyMeals) {
                await queryRunner.manager.delete(meal_entity_1.Meal, { protocolId: protocol.id });
                for (const [day, meals] of Object.entries(updateProtocolDto.weeklyMeals)) {
                    for (const meal of meals) {
                        const newMeal = this.mealRepository.create({
                            ...meal,
                            protocolId: protocol.id,
                            weekDay: day
                        });
                        await queryRunner.manager.save(newMeal);
                        for (const food of meal.foods) {
                            const newFood = this.foodRepository.create({
                                ...food,
                                mealId: newMeal.id
                            });
                            await queryRunner.manager.save(newFood);
                        }
                    }
                }
            }
            if (updateProtocolDto.supplements) {
                await queryRunner.manager.delete(supplement_entity_1.Supplement, { protocolId: protocol.id });
                for (const supplement of updateProtocolDto.supplements) {
                    const newSupplement = this.supplementRepository.create({
                        ...supplement,
                        protocolId: protocol.id
                    });
                    await queryRunner.manager.save(newSupplement);
                }
            }
            await queryRunner.commitTransaction();
            return protocol;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async addSupplement(protocolId, createSupplementDto) {
        const protocol = await this.protocolRepository.findOne({
            where: { id: protocolId }
        });
        if (!protocol) {
            throw new common_1.NotFoundException('Protocol not found');
        }
        const supplement = this.supplementRepository.create({
            ...createSupplementDto,
            protocolId
        });
        return this.supplementRepository.save(supplement);
    }
    async updateSupplement(id, updateData) {
        const supplement = await this.supplementRepository.findOne({
            where: { id }
        });
        if (!supplement) {
            throw new common_1.NotFoundException('Supplement not found');
        }
        Object.assign(supplement, updateData);
        return this.supplementRepository.save(supplement);
    }
    async removeSupplement(id) {
        const supplement = await this.supplementRepository.findOne({
            where: { id }
        });
        if (!supplement) {
            throw new common_1.NotFoundException('Supplement not found');
        }
        await this.supplementRepository.remove(supplement);
    }
    async generateShoppingList(userId, createShoppingListDto) {
        const protocol = await this.getCurrentProtocol(userId);
        const foods = await this.foodRepository
            .createQueryBuilder('food')
            .innerJoin('food.meal', 'meal')
            .where('meal.protocolId = :protocolId', { protocolId: protocol.id })
            .getMany();
        const multiplier = {
            weekly: 7,
            biweekly: 14,
            monthly: 30
        }[createShoppingListDto.period];
        const items = new Map();
        foods.forEach(food => {
            const key = `${food.name}-${food.unit}`;
            const existing = items.get(key);
            if (existing) {
                existing.quantity += food.quantity * multiplier;
            }
            else {
                items.set(key, {
                    name: food.name,
                    quantity: food.quantity * multiplier,
                    unit: food.unit
                });
            }
        });
        const shoppingList = this.shoppingListRepository.create({
            userId,
            protocolId: protocol.id,
            period: createShoppingListDto.period,
            items: Array.from(items.values()),
            generatedAt: new Date()
        });
        return this.shoppingListRepository.save(shoppingList);
    }
    async getShoppingLists(userId) {
        return this.shoppingListRepository.find({
            where: { userId },
            order: { generatedAt: 'DESC' }
        });
    }
    async getShoppingList(id, userId) {
        const list = await this.shoppingListRepository.findOne({
            where: { id, userId }
        });
        if (!list) {
            throw new common_1.NotFoundException('Shopping list not found');
        }
        return list;
    }
    async getProtocolsHistory(userId, filters) {
        const query = this.protocolRepository.createQueryBuilder('protocol')
            .where('protocol.userId = :userId AND protocol.type = :type', {
            userId,
            type: 'diet'
        })
            .orderBy('protocol.createdAt', 'DESC');
        if (filters.status) {
            query.andWhere('protocol.status = :status', { status: filters.status });
        }
        const offset = (filters.page - 1) * filters.limit;
        query.skip(offset).take(filters.limit + 1);
        const protocols = await query.getMany();
        const hasMore = protocols.length > filters.limit;
        if (hasMore) {
            protocols.pop();
        }
        const total = await query.getCount();
        return {
            protocols,
            total,
            hasMore
        };
    }
    async duplicateProtocol(id, userId, newName) {
        const originalProtocol = await this.protocolRepository.findOne({
            where: { id, userId },
            relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
        });
        if (!originalProtocol) {
            throw new common_1.NotFoundException('Diet protocol not found');
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const newProtocol = this.protocolRepository.create({
                name: newName || `${originalProtocol.name} (Cópia)`,
                type: 'diet',
                objective: originalProtocol.objective,
                startDate: new Date(),
                goals: originalProtocol.goals,
                waterCalculationMethod: originalProtocol.waterCalculationMethod,
                notes: originalProtocol.notes,
                status: 'active',
                userId,
                createdById: originalProtocol.createdById
            });
            await queryRunner.manager.save(newProtocol);
            for (const meal of originalProtocol.weeklyMeals) {
                const newMeal = this.mealRepository.create({
                    name: meal.name,
                    time: meal.time,
                    weekDay: meal.weekDay,
                    protocolId: newProtocol.id
                });
                await queryRunner.manager.save(newMeal);
                for (const food of meal.foods) {
                    const newFood = this.foodRepository.create({
                        name: food.name,
                        quantity: food.quantity,
                        unit: food.unit,
                        calories: food.calories,
                        protein: food.protein,
                        carbs: food.carbs,
                        fat: food.fat,
                        mealId: newMeal.id
                    });
                    await queryRunner.manager.save(newFood);
                }
            }
            for (const supplement of originalProtocol.supplements) {
                const newSupplement = this.supplementRepository.create({
                    name: supplement.name,
                    dosage: supplement.dosage,
                    timing: supplement.timing,
                    notes: supplement.notes,
                    protocolId: newProtocol.id
                });
                await queryRunner.manager.save(newSupplement);
            }
            await queryRunner.commitTransaction();
            const result = await this.protocolRepository.findOne({
                where: { id: newProtocol.id },
                relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
            });
            if (!result) {
                throw new Error('Failed to retrieve duplicated protocol');
            }
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.DietService = DietService;
exports.DietService = DietService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(protocol_entity_1.Protocol)),
    __param(2, (0, typeorm_1.InjectRepository)(meal_entity_1.Meal)),
    __param(3, (0, typeorm_1.InjectRepository)(food_entity_1.Food)),
    __param(4, (0, typeorm_1.InjectRepository)(supplement_entity_1.Supplement)),
    __param(5, (0, typeorm_1.InjectRepository)(shopping_list_entity_1.ShoppingList)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.DataSource !== "undefined" && typeorm_2.DataSource) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, typeof (_e = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _e : Object, typeof (_f = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _f : Object])
], DietService);


/***/ }),
/* 67 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Protocol = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const meal_entity_1 = __webpack_require__(68);
const supplement_entity_1 = __webpack_require__(70);
let Protocol = class Protocol {
};
exports.Protocol = Protocol;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Protocol.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Protocol.prototype, "dietType", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "objective", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], Protocol.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Protocol.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Object)
], Protocol.prototype, "goals", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "waterCalculationMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Protocol.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'active' }),
    __metadata("design:type", String)
], Protocol.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_c = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _c : Object)
], Protocol.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_d = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _d : Object)
], Protocol.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Protocol.prototype, "createdById", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => meal_entity_1.Meal, meal => meal.protocol),
    __metadata("design:type", Array)
], Protocol.prototype, "weeklyMeals", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => supplement_entity_1.Supplement, supplement => supplement.protocol),
    __metadata("design:type", Array)
], Protocol.prototype, "supplements", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Protocol.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], Protocol.prototype, "updatedAt", void 0);
exports.Protocol = Protocol = __decorate([
    (0, typeorm_1.Entity)('protocols')
], Protocol);


/***/ }),
/* 68 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Meal = void 0;
const typeorm_1 = __webpack_require__(12);
const protocol_entity_1 = __webpack_require__(67);
const food_entity_1 = __webpack_require__(69);
let Meal = class Meal {
};
exports.Meal = Meal;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Meal.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "time", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "weekDay", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Meal.prototype, "completed", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => protocol_entity_1.Protocol, protocol => protocol.weeklyMeals),
    __metadata("design:type", typeof (_a = typeof protocol_entity_1.Protocol !== "undefined" && protocol_entity_1.Protocol) === "function" ? _a : Object)
], Meal.prototype, "protocol", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Meal.prototype, "protocolId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => food_entity_1.Food, food => food.meal),
    __metadata("design:type", Array)
], Meal.prototype, "foods", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Meal.prototype, "createdAt", void 0);
exports.Meal = Meal = __decorate([
    (0, typeorm_1.Entity)('meals')
], Meal);


/***/ }),
/* 69 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Food = void 0;
const typeorm_1 = __webpack_require__(12);
const meal_entity_1 = __webpack_require__(68);
let Food = class Food {
};
exports.Food = Food;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Food.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Food.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Food.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "calories", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "protein", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "carbs", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Food.prototype, "fat", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Food.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Food.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => meal_entity_1.Meal, meal => meal.foods),
    __metadata("design:type", typeof (_a = typeof meal_entity_1.Meal !== "undefined" && meal_entity_1.Meal) === "function" ? _a : Object)
], Food.prototype, "meal", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Food.prototype, "mealId", void 0);
exports.Food = Food = __decorate([
    (0, typeorm_1.Entity)('foods')
], Food);


/***/ }),
/* 70 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Supplement = void 0;
const typeorm_1 = __webpack_require__(12);
const protocol_entity_1 = __webpack_require__(67);
let Supplement = class Supplement {
};
exports.Supplement = Supplement;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Supplement.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Supplement.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Supplement.prototype, "dosage", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Supplement.prototype, "timing", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Supplement.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => protocol_entity_1.Protocol, protocol => protocol.supplements),
    __metadata("design:type", typeof (_a = typeof protocol_entity_1.Protocol !== "undefined" && protocol_entity_1.Protocol) === "function" ? _a : Object)
], Supplement.prototype, "protocol", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Supplement.prototype, "protocolId", void 0);
exports.Supplement = Supplement = __decorate([
    (0, typeorm_1.Entity)('supplements')
], Supplement);


/***/ }),
/* 71 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ShoppingList = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const protocol_entity_1 = __webpack_require__(67);
let ShoppingList = class ShoppingList {
};
exports.ShoppingList = ShoppingList;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ShoppingList.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], ShoppingList.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ShoppingList.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => protocol_entity_1.Protocol),
    __metadata("design:type", typeof (_b = typeof protocol_entity_1.Protocol !== "undefined" && protocol_entity_1.Protocol) === "function" ? _b : Object)
], ShoppingList.prototype, "protocol", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ShoppingList.prototype, "protocolId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ShoppingList.prototype, "period", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb'),
    __metadata("design:type", Array)
], ShoppingList.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ShoppingList.prototype, "generatedAt", void 0);
exports.ShoppingList = ShoppingList = __decorate([
    (0, typeorm_1.Entity)('shopping_lists')
], ShoppingList);


/***/ }),
/* 72 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateProtocolDto = void 0;
const class_validator_1 = __webpack_require__(30);
const class_transformer_1 = __webpack_require__(57);
const swagger_1 = __webpack_require__(3);
const create_supplement_dto_1 = __webpack_require__(73);
class CreateProtocolDto {
}
exports.CreateProtocolDto = CreateProtocolDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "dietType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "objective", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateProtocolDto.prototype, "goals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsEnum)(['weight', 'manual']),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "waterCalculationMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateProtocolDto.prototype, "weeklyMeals", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [create_supplement_dto_1.CreateSupplementDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => create_supplement_dto_1.CreateSupplementDto),
    __metadata("design:type", Array)
], CreateProtocolDto.prototype, "supplements", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProtocolDto.prototype, "notes", void 0);


/***/ }),
/* 73 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateSupplementDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class CreateSupplementDto {
}
exports.CreateSupplementDto = CreateSupplementDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSupplementDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSupplementDto.prototype, "dosage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSupplementDto.prototype, "timing", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateSupplementDto.prototype, "notes", void 0);


/***/ }),
/* 74 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateProtocolDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_protocol_dto_1 = __webpack_require__(72);
class UpdateProtocolDto extends (0, swagger_1.PartialType)(create_protocol_dto_1.CreateProtocolDto) {
}
exports.UpdateProtocolDto = UpdateProtocolDto;


/***/ }),
/* 75 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateShoppingListDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class CreateShoppingListDto {
}
exports.CreateShoppingListDto = CreateShoppingListDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsEnum)(['weekly', 'biweekly', 'monthly']),
    __metadata("design:type", String)
], CreateShoppingListDto.prototype, "period", void 0);


/***/ }),
/* 76 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MicronutrientsModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const micronutrients_controller_1 = __webpack_require__(77);
const micronutrients_service_1 = __webpack_require__(79);
const blood_test_entity_1 = __webpack_require__(80);
const micronutrient_analysis_entity_1 = __webpack_require__(81);
const supplement_recommendation_entity_1 = __webpack_require__(82);
const user_entity_1 = __webpack_require__(13);
let MicronutrientsModule = class MicronutrientsModule {
};
exports.MicronutrientsModule = MicronutrientsModule;
exports.MicronutrientsModule = MicronutrientsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                blood_test_entity_1.BloodTest,
                micronutrient_analysis_entity_1.MicronutrientAnalysis,
                supplement_recommendation_entity_1.SupplementRecommendation,
                user_entity_1.User
            ])
        ],
        controllers: [micronutrients_controller_1.MicronutrientsController, micronutrients_controller_1.AssessmentsController],
        providers: [micronutrients_service_1.MicronutrientsService],
        exports: [micronutrients_service_1.MicronutrientsService]
    })
], MicronutrientsModule);


/***/ }),
/* 77 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AssessmentsController = exports.MicronutrientsController = void 0;
const common_1 = __webpack_require__(2);
const platform_express_1 = __webpack_require__(78);
const swagger_1 = __webpack_require__(3);
const jwt_auth_guard_1 = __webpack_require__(35);
const micronutrients_service_1 = __webpack_require__(79);
const create_supplement_plan_dto_1 = __webpack_require__(83);
const multer_1 = __webpack_require__(84);
const path_1 = __webpack_require__(85);
let MicronutrientsController = class MicronutrientsController {
    constructor(micronutrientsService) {
        this.micronutrientsService = micronutrientsService;
    }
    async getNutritionalAnalysis(req) {
        return this.micronutrientsService.getNutritionalAnalysis(req.user.id);
    }
    async getWeeklyNutritionData(req) {
        return this.micronutrientsService.getWeeklyNutritionData(req.user.id);
    }
    async getNutritionalSummary(req) {
        return this.micronutrientsService.getNutritionalSummary(req.user.id);
    }
    async getAssessments(req) {
        return this.micronutrientsService.getAssessments(req.user.id);
    }
    async getBloodTests(req) {
        return this.micronutrientsService.getBloodTests(req.user.id);
    }
    async uploadBloodTest(req, file) {
        return this.micronutrientsService.uploadBloodTest(req.user.id, file);
    }
    async getSupplementRecommendations(req) {
        return this.micronutrientsService.getSupplementRecommendations(req.user.id);
    }
    async createSupplementPlan(req, createPlanDto) {
        return this.micronutrientsService.createSupplementPlan(req.user.id, createPlanDto);
    }
    async getMicronutrientEvolution(req, period) {
        return this.micronutrientsService.getMicronutrientEvolution(req.user.id, period);
    }
};
exports.MicronutrientsController = MicronutrientsController;
__decorate([
    (0, common_1.Get)('progress/nutritional_analysis'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user nutritional analysis' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getNutritionalAnalysis", null);
__decorate([
    (0, common_1.Get)('analytics/nutrition/weekly'),
    (0, swagger_1.ApiOperation)({ summary: 'Get weekly nutrition analytics' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getWeeklyNutritionData", null);
__decorate([
    (0, common_1.Get)('diary/nutritional_summary'),
    (0, swagger_1.ApiOperation)({ summary: 'Get nutritional summary from diary' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getNutritionalSummary", null);
__decorate([
    (0, common_1.Get)('assessments'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user assessments including blood tests' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getAssessments", null);
__decorate([
    (0, common_1.Get)('blood-tests'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user blood tests' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getBloodTests", null);
__decorate([
    (0, common_1.Post)('blood-tests/upload'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload blood test file' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        storage: (0, multer_1.diskStorage)({
            destination: './uploads/blood-tests',
            filename: (req, file, cb) => {
                const randomName = Array(32).fill(null).map(() => (Math.round(Math.random() * 16)).toString(16)).join('');
                cb(null, `${randomName}${(0, path_1.extname)(file.originalname)}`);
            },
        }),
    })),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }),
            new common_1.FileTypeValidator({ fileType: /(jpg|jpeg|png|pdf)$/ }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_c = typeof Express !== "undefined" && (_b = Express.Multer) !== void 0 && _b.File) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "uploadBloodTest", null);
__decorate([
    (0, common_1.Get)('supplements/recommendations'),
    (0, swagger_1.ApiOperation)({ summary: 'Get supplement recommendations for user' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getSupplementRecommendations", null);
__decorate([
    (0, common_1.Post)('supplements/plan'),
    (0, swagger_1.ApiOperation)({ summary: 'Create supplement plan from recommendations' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_d = typeof create_supplement_plan_dto_1.CreateSupplementPlanDto !== "undefined" && create_supplement_plan_dto_1.CreateSupplementPlanDto) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "createSupplementPlan", null);
__decorate([
    (0, common_1.Get)('micronutrients/evolution'),
    (0, swagger_1.ApiOperation)({ summary: 'Get micronutrient evolution data' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('period')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], MicronutrientsController.prototype, "getMicronutrientEvolution", null);
exports.MicronutrientsController = MicronutrientsController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof micronutrients_service_1.MicronutrientsService !== "undefined" && micronutrients_service_1.MicronutrientsService) === "function" ? _a : Object])
], MicronutrientsController);
let AssessmentsController = class AssessmentsController {
    constructor(micronutrientsService) {
        this.micronutrientsService = micronutrientsService;
    }
    async getBloodTestsFromAssessments(req) {
        return this.micronutrientsService.getBloodTests(req.user.id);
    }
};
exports.AssessmentsController = AssessmentsController;
__decorate([
    (0, common_1.Get)('blood-tests'),
    (0, swagger_1.ApiOperation)({ summary: 'Get blood tests from assessments' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AssessmentsController.prototype, "getBloodTestsFromAssessments", null);
exports.AssessmentsController = AssessmentsController = __decorate([
    (0, swagger_1.ApiTags)('assessments'),
    (0, common_1.Controller)('assessments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_e = typeof micronutrients_service_1.MicronutrientsService !== "undefined" && micronutrients_service_1.MicronutrientsService) === "function" ? _e : Object])
], AssessmentsController);


/***/ }),
/* 78 */
/***/ ((module) => {

module.exports = require("@nestjs/platform-express");

/***/ }),
/* 79 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MicronutrientsService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const blood_test_entity_1 = __webpack_require__(80);
const micronutrient_analysis_entity_1 = __webpack_require__(81);
const supplement_recommendation_entity_1 = __webpack_require__(82);
let MicronutrientsService = class MicronutrientsService {
    constructor(bloodTestRepository, analysisRepository, recommendationRepository) {
        this.bloodTestRepository = bloodTestRepository;
        this.analysisRepository = analysisRepository;
        this.recommendationRepository = recommendationRepository;
    }
    async getNutritionalAnalysis(userId) {
        let analysis = await this.analysisRepository.findOne({
            where: { userId },
            order: { createdAt: 'DESC' }
        });
        if (!analysis) {
            analysis = await this.createInitialAnalysis(userId);
        }
        return {
            status: 'success',
            data: {
                date: analysis.analysisDate.toISOString(),
                totalIntake: analysis.totalIntake,
                deficiencies: analysis.deficiencies,
                excesses: analysis.excesses,
                recommendations: await this.getRecommendationsForUser(userId),
                overallScore: analysis.overallScore,
                improvementAreas: analysis.improvementAreas
            }
        };
    }
    async getWeeklyNutritionData(userId) {
        const analysis = await this.analysisRepository.findOne({
            where: { userId },
            order: { createdAt: 'DESC' }
        });
        if (!analysis) {
            return this.getNutritionalAnalysis(userId);
        }
        return {
            status: 'success',
            data: {
                weekly_averages: analysis.weeklyData?.weekly_averages || this.generateWeeklyAverages(),
                macronutrient_distribution: analysis.weeklyData?.macronutrient_distribution || this.generateMacroDistribution(),
                micronutrient_summary: {
                    totalIntake: analysis.totalIntake,
                    overallScore: analysis.overallScore
                }
            }
        };
    }
    async getNutritionalSummary(userId) {
        return this.getNutritionalAnalysis(userId);
    }
    async getBloodTests(userId) {
        const bloodTests = await this.bloodTestRepository.find({
            where: { userId },
            order: { testDate: 'DESC' }
        });
        return {
            status: 'success',
            data: bloodTests
        };
    }
    async getAssessments(userId) {
        return this.getBloodTests(userId);
    }
    async uploadBloodTest(userId, file, createBloodTestDto) {
        const mockResults = [
            {
                id: `result_${Date.now()}`,
                date: new Date().toISOString(),
                testName: 'Vitamina D (25-OH)',
                value: 22.3,
                unit: 'ng/mL',
                referenceRange: { min: 30, max: 100 },
                status: 'low',
                relatedMicronutrients: ['vitamin_d']
            },
            {
                id: `result_${Date.now() + 1}`,
                date: new Date().toISOString(),
                testName: 'Vitamina B12',
                value: 180,
                unit: 'pg/mL',
                referenceRange: { min: 200, max: 900 },
                status: 'low',
                relatedMicronutrients: ['vitamin_b12']
            }
        ];
        const bloodTest = this.bloodTestRepository.create({
            userId,
            testDate: new Date(),
            uploadedFileName: file.originalname,
            uploadedFilePath: file.path,
            results: mockResults,
            analyzedByAI: true,
            notes: 'Analisado automaticamente por IA'
        });
        const savedTest = await this.bloodTestRepository.save(bloodTest);
        await this.updateAnalysisFromBloodTest(userId, savedTest);
        return {
            status: 'success',
            data: savedTest
        };
    }
    async getSupplementRecommendations(userId) {
        const recommendations = await this.recommendationRepository.find({
            where: { userId, isActive: true },
            order: { priority: 'ASC', createdAt: 'DESC' }
        });
        return {
            status: 'success',
            data: recommendations
        };
    }
    async createSupplementPlan(userId, createPlanDto) {
        const recommendations = await this.recommendationRepository.findByIds(createPlanDto.recommendations);
        const totalCost = recommendations.reduce((sum, rec) => sum + (rec.cost || 0), 0);
        return {
            status: 'success',
            data: {
                id: `plan_${Date.now()}`,
                recommendations,
                totalCost,
                duration: '3 meses',
                expectedImprovements: [
                    'Melhora nos níveis de energia',
                    'Fortalecimento do sistema imunológico',
                    'Melhora do humor e disposição'
                ]
            }
        };
    }
    async getMicronutrientEvolution(userId, period = '3months') {
        const endDate = new Date();
        const startDate = new Date();
        switch (period) {
            case '1month':
                startDate.setMonth(endDate.getMonth() - 1);
                break;
            case '6months':
                startDate.setMonth(endDate.getMonth() - 6);
                break;
            case '1year':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
            default:
                startDate.setMonth(endDate.getMonth() - 3);
        }
        const analyses = await this.analysisRepository.find({
            where: {
                userId,
                analysisDate: (0, typeorm_2.Between)(startDate, endDate)
            },
            order: { analysisDate: 'ASC' }
        });
        const evolutionData = this.generateEvolutionData(analyses, period);
        return {
            status: 'success',
            data: evolutionData
        };
    }
    async createInitialAnalysis(userId) {
        const mockAnalysis = this.analysisRepository.create({
            userId,
            analysisDate: new Date(),
            totalIntake: [
                {
                    micronutrientId: 'vitamin_d',
                    amount: 8.5,
                    percentage: 57,
                    status: 'low',
                    source: 'food'
                },
                {
                    micronutrientId: 'vitamin_b12',
                    amount: 1.2,
                    percentage: 50,
                    status: 'deficient',
                    source: 'food'
                },
                {
                    micronutrientId: 'iron',
                    amount: 12.8,
                    percentage: 91,
                    status: 'adequate',
                    source: 'food'
                },
                {
                    micronutrientId: 'magnesium',
                    amount: 280,
                    percentage: 70,
                    status: 'low',
                    source: 'food'
                },
                {
                    micronutrientId: 'zinc',
                    amount: 9.2,
                    percentage: 84,
                    status: 'adequate',
                    source: 'food'
                },
                {
                    micronutrientId: 'vitamin_c',
                    amount: 75,
                    percentage: 83,
                    status: 'adequate',
                    source: 'food'
                }
            ],
            deficiencies: ['vitamin_b12', 'vitamin_d'],
            excesses: [],
            overallScore: 72,
            improvementAreas: ['Aumentar exposição solar', 'Incluir mais alimentos ricos em B12', 'Considerar suplementação'],
            weeklyData: {
                weekly_averages: this.generateWeeklyAverages(),
                macronutrient_distribution: this.generateMacroDistribution()
            }
        });
        const savedAnalysis = await this.analysisRepository.save(mockAnalysis);
        await this.createInitialRecommendations(userId);
        return savedAnalysis;
    }
    async createInitialRecommendations(userId) {
        const recommendations = [
            {
                userId,
                micronutrientId: 'vitamin_d',
                name: 'Vitamina D3',
                dosage: 2000,
                unit: 'UI',
                timing: 'morning',
                duration: '3 meses',
                reason: 'Deficiência detectada nos exames e baixa exposição solar',
                priority: 'high',
                interactions: ['Pode aumentar absorção de cálcio'],
                sideEffects: ['Raros em doses adequadas'],
                cost: 25.90
            },
            {
                userId,
                micronutrientId: 'vitamin_b12',
                name: 'Metilcobalamina',
                dosage: 1000,
                unit: 'μg',
                timing: 'morning',
                duration: '2 meses',
                reason: 'Deficiência severa detectada',
                priority: 'high',
                interactions: [],
                sideEffects: ['Muito raros'],
                cost: 18.50
            }
        ];
        for (const rec of recommendations) {
            const recommendation = this.recommendationRepository.create(rec);
            await this.recommendationRepository.save(recommendation);
        }
    }
    async getRecommendationsForUser(userId) {
        const recommendations = await this.recommendationRepository.find({
            where: { userId, isActive: true }
        });
        return recommendations.map(rec => ({
            id: rec.id,
            micronutrientId: rec.micronutrientId,
            name: rec.name,
            dosage: rec.dosage,
            unit: rec.unit,
            timing: rec.timing,
            duration: rec.duration,
            reason: rec.reason,
            priority: rec.priority,
            interactions: rec.interactions,
            sideEffects: rec.sideEffects,
            cost: rec.cost
        }));
    }
    generateWeeklyAverages() {
        return {
            calories: 2100,
            protein: 150,
            carbs: 220,
            fat: 70,
            fiber: 25,
            water: 2.2
        };
    }
    generateMacroDistribution() {
        return {
            protein: 28.6,
            carbs: 41.9,
            fat: 29.5
        };
    }
    generateEvolutionData(analyses, period) {
        return {
            vitamin_d: {
                timeline: [
                    { date: '2024-01-01', value: 8.5, percentage: 57 },
                    { date: '2024-01-15', value: 12.2, percentage: 81 },
                    { date: '2024-02-01', value: 15.8, percentage: 105 },
                    { date: '2024-02-15', value: 18.3, percentage: 122 },
                ],
                trend: 'improving',
                targetReached: true
            },
            vitamin_b12: {
                timeline: [
                    { date: '2024-01-01', value: 1.2, percentage: 50 },
                    { date: '2024-01-15', value: 1.8, percentage: 75 },
                    { date: '2024-02-01', value: 2.1, percentage: 88 },
                    { date: '2024-02-15', value: 2.4, percentage: 100 },
                ],
                trend: 'improving',
                targetReached: true
            },
            iron: {
                timeline: [
                    { date: '2024-01-01', value: 12.8, percentage: 91 },
                    { date: '2024-01-15', value: 13.2, percentage: 94 },
                    { date: '2024-02-01', value: 12.5, percentage: 89 },
                    { date: '2024-02-15', value: 13.8, percentage: 99 },
                ],
                trend: 'stable',
                targetReached: false
            }
        };
    }
    async updateAnalysisFromBloodTest(userId, bloodTest) {
        await this.getNutritionalAnalysis(userId);
    }
};
exports.MicronutrientsService = MicronutrientsService;
exports.MicronutrientsService = MicronutrientsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(blood_test_entity_1.BloodTest)),
    __param(1, (0, typeorm_1.InjectRepository)(micronutrient_analysis_entity_1.MicronutrientAnalysis)),
    __param(2, (0, typeorm_1.InjectRepository)(supplement_recommendation_entity_1.SupplementRecommendation)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object])
], MicronutrientsService);


/***/ }),
/* 80 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.BloodTest = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let BloodTest = class BloodTest {
};
exports.BloodTest = BloodTest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], BloodTest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], BloodTest.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], BloodTest.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], BloodTest.prototype, "testDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BloodTest.prototype, "uploadedFileName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], BloodTest.prototype, "uploadedFilePath", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Array)
], BloodTest.prototype, "results", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], BloodTest.prototype, "analyzedByAI", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], BloodTest.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], BloodTest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], BloodTest.prototype, "updatedAt", void 0);
exports.BloodTest = BloodTest = __decorate([
    (0, typeorm_1.Entity)('blood_tests')
], BloodTest);


/***/ }),
/* 81 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.MicronutrientAnalysis = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let MicronutrientAnalysis = class MicronutrientAnalysis {
};
exports.MicronutrientAnalysis = MicronutrientAnalysis;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], MicronutrientAnalysis.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], MicronutrientAnalysis.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], MicronutrientAnalysis.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], MicronutrientAnalysis.prototype, "analysisDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Array)
], MicronutrientAnalysis.prototype, "totalIntake", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array' }),
    __metadata("design:type", Array)
], MicronutrientAnalysis.prototype, "deficiencies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array' }),
    __metadata("design:type", Array)
], MicronutrientAnalysis.prototype, "excesses", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MicronutrientAnalysis.prototype, "overallScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array' }),
    __metadata("design:type", Array)
], MicronutrientAnalysis.prototype, "improvementAreas", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], MicronutrientAnalysis.prototype, "weeklyData", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], MicronutrientAnalysis.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], MicronutrientAnalysis.prototype, "updatedAt", void 0);
exports.MicronutrientAnalysis = MicronutrientAnalysis = __decorate([
    (0, typeorm_1.Entity)('micronutrient_analyses')
], MicronutrientAnalysis);


/***/ }),
/* 82 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SupplementRecommendation = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
let SupplementRecommendation = class SupplementRecommendation {
};
exports.SupplementRecommendation = SupplementRecommendation;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], SupplementRecommendation.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "micronutrientId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], SupplementRecommendation.prototype, "dosage", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "timing", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SupplementRecommendation.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], SupplementRecommendation.prototype, "interactions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], SupplementRecommendation.prototype, "sideEffects", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], SupplementRecommendation.prototype, "cost", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], SupplementRecommendation.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], SupplementRecommendation.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], SupplementRecommendation.prototype, "updatedAt", void 0);
exports.SupplementRecommendation = SupplementRecommendation = __decorate([
    (0, typeorm_1.Entity)('supplement_recommendations')
], SupplementRecommendation);


/***/ }),
/* 83 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateSupplementPlanDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class CreateSupplementPlanDto {
}
exports.CreateSupplementPlanDto = CreateSupplementPlanDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], description: 'Array of recommendation IDs to include in the plan' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateSupplementPlanDto.prototype, "recommendations", void 0);


/***/ }),
/* 84 */
/***/ ((module) => {

module.exports = require("multer");

/***/ }),
/* 85 */
/***/ ((module) => {

module.exports = require("path");

/***/ }),
/* 86 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RecipesModule = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const recipes_controller_1 = __webpack_require__(87);
const recipes_service_1 = __webpack_require__(88);
const recipe_entity_1 = __webpack_require__(89);
const user_favorite_recipe_entity_1 = __webpack_require__(90);
const recipe_rating_entity_1 = __webpack_require__(91);
const user_entity_1 = __webpack_require__(13);
let RecipesModule = class RecipesModule {
};
exports.RecipesModule = RecipesModule;
exports.RecipesModule = RecipesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                recipe_entity_1.Recipe,
                user_favorite_recipe_entity_1.UserFavoriteRecipe,
                recipe_rating_entity_1.RecipeRating,
                user_entity_1.User
            ])
        ],
        controllers: [recipes_controller_1.RecipesController],
        providers: [recipes_service_1.RecipesService],
        exports: [recipes_service_1.RecipesService]
    })
], RecipesModule);


/***/ }),
/* 87 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RecipesController = void 0;
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const jwt_auth_guard_1 = __webpack_require__(35);
const recipes_service_1 = __webpack_require__(88);
const create_recipe_dto_1 = __webpack_require__(92);
const update_recipe_dto_1 = __webpack_require__(93);
const add_favorite_dto_1 = __webpack_require__(94);
const rate_recipe_dto_1 = __webpack_require__(95);
let RecipesController = class RecipesController {
    constructor(recipesService) {
        this.recipesService = recipesService;
    }
    async createRecipe(req, createRecipeDto) {
        return this.recipesService.createRecipe(req.user.id, createRecipeDto);
    }
    async searchRecipes(req, query) {
        if (query.tags && typeof query.tags === 'string') {
            query.tags = query.tags.split(',').map((tag) => tag.trim());
        }
        return this.recipesService.findAllRecipes(req.user.id, query);
    }
    async getRecipeById(req, id) {
        return this.recipesService.findRecipeById(id, req.user.id);
    }
    async updateRecipe(req, id, updateRecipeDto) {
        return this.recipesService.updateRecipe(id, req.user.id, updateRecipeDto);
    }
    async deleteRecipe(req, id) {
        return this.recipesService.deleteRecipe(id, req.user.id);
    }
    async getFavoriteRecipes(req) {
        return this.recipesService.getUserFavorites(req.user.id);
    }
    async addToFavorites(req, addFavoriteDto) {
        return this.recipesService.addToFavorites(req.user.id, addFavoriteDto);
    }
    async removeFromFavorites(req, favoriteId) {
        return this.recipesService.removeFromFavorites(req.user.id, favoriteId);
    }
    async rateRecipe(req, rateRecipeDto) {
        return this.recipesService.rateRecipe(req.user.id, rateRecipeDto);
    }
};
exports.RecipesController = RecipesController;
__decorate([
    (0, common_1.Post)('recipes'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new recipe' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_b = typeof create_recipe_dto_1.CreateRecipeDto !== "undefined" && create_recipe_dto_1.CreateRecipeDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "createRecipe", null);
__decorate([
    (0, common_1.Get)('recipes/search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search recipes with filters' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term for recipe name or description' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, enum: ['breakfast', 'lunch', 'dinner', 'snack', 'dessert', 'drink'] }),
    (0, swagger_1.ApiQuery)({ name: 'difficulty', required: false, enum: ['easy', 'medium', 'hard'] }),
    (0, swagger_1.ApiQuery)({ name: 'maxPrepTime', required: false, type: Number, description: 'Maximum preparation time in minutes' }),
    (0, swagger_1.ApiQuery)({ name: 'minRating', required: false, type: Number, description: 'Minimum average rating' }),
    (0, swagger_1.ApiQuery)({ name: 'tags', required: false, description: 'Comma-separated tags' }),
    (0, swagger_1.ApiQuery)({ name: 'isPublic', required: false, type: Boolean, description: 'Filter public recipes' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "searchRecipes", null);
__decorate([
    (0, common_1.Get)('recipes/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get recipe by ID' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "getRecipeById", null);
__decorate([
    (0, common_1.Put)('recipes/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update recipe' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, typeof (_c = typeof update_recipe_dto_1.UpdateRecipeDto !== "undefined" && update_recipe_dto_1.UpdateRecipeDto) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "updateRecipe", null);
__decorate([
    (0, common_1.Delete)('recipes/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete recipe' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "deleteRecipe", null);
__decorate([
    (0, common_1.Get)('recipes/favorites'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user favorite recipes' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "getFavoriteRecipes", null);
__decorate([
    (0, common_1.Post)('recipes/favorites'),
    (0, swagger_1.ApiOperation)({ summary: 'Add recipe to favorites' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_d = typeof add_favorite_dto_1.AddFavoriteDto !== "undefined" && add_favorite_dto_1.AddFavoriteDto) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "addToFavorites", null);
__decorate([
    (0, common_1.Delete)('recipes/favorites/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Remove recipe from favorites' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "removeFromFavorites", null);
__decorate([
    (0, common_1.Post)('recipes/rate'),
    (0, swagger_1.ApiOperation)({ summary: 'Rate a recipe' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, typeof (_e = typeof rate_recipe_dto_1.RateRecipeDto !== "undefined" && rate_recipe_dto_1.RateRecipeDto) === "function" ? _e : Object]),
    __metadata("design:returntype", Promise)
], RecipesController.prototype, "rateRecipe", null);
exports.RecipesController = RecipesController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof recipes_service_1.RecipesService !== "undefined" && recipes_service_1.RecipesService) === "function" ? _a : Object])
], RecipesController);


/***/ }),
/* 88 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RecipesService = void 0;
const common_1 = __webpack_require__(2);
const typeorm_1 = __webpack_require__(5);
const typeorm_2 = __webpack_require__(12);
const recipe_entity_1 = __webpack_require__(89);
const user_favorite_recipe_entity_1 = __webpack_require__(90);
const recipe_rating_entity_1 = __webpack_require__(91);
let RecipesService = class RecipesService {
    constructor(recipeRepository, favoriteRepository, ratingRepository) {
        this.recipeRepository = recipeRepository;
        this.favoriteRepository = favoriteRepository;
        this.ratingRepository = ratingRepository;
    }
    async createRecipe(userId, createRecipeDto) {
        const nutritionalTotals = this.calculateNutritionalTotals(createRecipeDto.ingredients);
        const recipe = this.recipeRepository.create({
            ...createRecipeDto,
            ...nutritionalTotals,
            createdBy: userId,
        });
        const savedRecipe = await this.recipeRepository.save(recipe);
        return {
            status: 'success',
            data: savedRecipe
        };
    }
    async findAllRecipes(userId, query = {}) {
        const { search, category, difficulty, maxPrepTime, minRating, tags, isPublic = true, page = 1, limit = 20 } = query;
        const queryBuilder = this.recipeRepository.createQueryBuilder('recipe')
            .leftJoinAndSelect('recipe.creator', 'creator')
            .leftJoinAndSelect('recipe.favorites', 'favorites', 'favorites.userId = :userId', { userId })
            .where('recipe.isActive = :isActive', { isActive: true });
        if (isPublic) {
            queryBuilder.andWhere('(recipe.isPublic = :isPublic OR recipe.createdBy = :userId)', { isPublic: true, userId });
        }
        else {
            queryBuilder.andWhere('recipe.createdBy = :userId', { userId });
        }
        if (search) {
            queryBuilder.andWhere('(recipe.name LIKE :search OR recipe.description LIKE :search)', { search: `%${search}%` });
        }
        if (category) {
            queryBuilder.andWhere('recipe.category = :category', { category });
        }
        if (difficulty) {
            queryBuilder.andWhere('recipe.difficulty = :difficulty', { difficulty });
        }
        if (maxPrepTime) {
            queryBuilder.andWhere('recipe.prepTime <= :maxPrepTime', { maxPrepTime });
        }
        if (minRating) {
            queryBuilder.andWhere('recipe.averageRating >= :minRating', { minRating });
        }
        if (tags && tags.length > 0) {
            const tagArray = Array.isArray(tags) ? tags : [tags];
            queryBuilder.andWhere('recipe.tags && :tags', { tags: tagArray });
        }
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        queryBuilder.orderBy('recipe.averageRating', 'DESC')
            .addOrderBy('recipe.createdAt', 'DESC');
        const [recipes, total] = await queryBuilder.getManyAndCount();
        return {
            status: 'success',
            data: {
                recipes,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        };
    }
    async findRecipeById(id, userId) {
        const queryBuilder = this.recipeRepository.createQueryBuilder('recipe')
            .leftJoinAndSelect('recipe.creator', 'creator')
            .leftJoinAndSelect('recipe.ratings', 'ratings')
            .leftJoinAndSelect('ratings.user', 'ratingUser')
            .where('recipe.id = :id', { id })
            .andWhere('recipe.isActive = :isActive', { isActive: true });
        if (userId) {
            queryBuilder.leftJoinAndSelect('recipe.favorites', 'favorites', 'favorites.userId = :userId', { userId });
        }
        const recipe = await queryBuilder.getOne();
        if (!recipe) {
            throw new common_1.NotFoundException('Recipe not found');
        }
        if (!recipe.isPublic && recipe.createdBy !== userId) {
            throw new common_1.ForbiddenException('You do not have access to this recipe');
        }
        return {
            status: 'success',
            data: recipe
        };
    }
    async updateRecipe(id, userId, updateRecipeDto) {
        const recipe = await this.recipeRepository.findOne({ where: { id, isActive: true } });
        if (!recipe) {
            throw new common_1.NotFoundException('Recipe not found');
        }
        if (recipe.createdBy !== userId) {
            throw new common_1.ForbiddenException('You can only update your own recipes');
        }
        let nutritionalTotals = {};
        if (updateRecipeDto.ingredients) {
            nutritionalTotals = this.calculateNutritionalTotals(updateRecipeDto.ingredients);
        }
        Object.assign(recipe, updateRecipeDto, nutritionalTotals);
        const updatedRecipe = await this.recipeRepository.save(recipe);
        return {
            status: 'success',
            data: updatedRecipe
        };
    }
    async deleteRecipe(id, userId) {
        const recipe = await this.recipeRepository.findOne({ where: { id, isActive: true } });
        if (!recipe) {
            throw new common_1.NotFoundException('Recipe not found');
        }
        if (recipe.createdBy !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own recipes');
        }
        recipe.isActive = false;
        await this.recipeRepository.save(recipe);
        return {
            status: 'success',
            message: 'Recipe deleted successfully'
        };
    }
    async getUserFavorites(userId) {
        const favorites = await this.favoriteRepository.find({
            where: { userId },
            relations: ['recipe', 'recipe.creator'],
            order: { createdAt: 'DESC' }
        });
        const recipes = favorites.map(fav => ({
            ...fav.recipe,
            favoriteNotes: fav.notes,
            favoritedAt: fav.createdAt
        }));
        return {
            status: 'success',
            data: recipes
        };
    }
    async addToFavorites(userId, addFavoriteDto) {
        const { recipeId, notes } = addFavoriteDto;
        const recipe = await this.recipeRepository.findOne({
            where: { id: recipeId, isActive: true }
        });
        if (!recipe) {
            throw new common_1.NotFoundException('Recipe not found');
        }
        if (!recipe.isPublic && recipe.createdBy !== userId) {
            throw new common_1.ForbiddenException('You cannot favorite this recipe');
        }
        const existingFavorite = await this.favoriteRepository.findOne({
            where: { userId, recipeId }
        });
        if (existingFavorite) {
            throw new common_1.ConflictException('Recipe is already in your favorites');
        }
        const favorite = this.favoriteRepository.create({
            userId,
            recipeId,
            notes
        });
        await this.favoriteRepository.save(favorite);
        await this.updateRecipeFavoritesCount(recipeId);
        return {
            status: 'success',
            message: 'Recipe added to favorites'
        };
    }
    async removeFromFavorites(userId, favoriteId) {
        const favorite = await this.favoriteRepository.findOne({
            where: { id: favoriteId, userId }
        });
        if (!favorite) {
            throw new common_1.NotFoundException('Favorite not found');
        }
        await this.favoriteRepository.remove(favorite);
        await this.updateRecipeFavoritesCount(favorite.recipeId);
        return {
            status: 'success',
            message: 'Recipe removed from favorites'
        };
    }
    async rateRecipe(userId, rateRecipeDto) {
        const { recipeId, rating, comment } = rateRecipeDto;
        const recipe = await this.recipeRepository.findOne({
            where: { id: recipeId, isActive: true }
        });
        if (!recipe) {
            throw new common_1.NotFoundException('Recipe not found');
        }
        let existingRating = await this.ratingRepository.findOne({
            where: { userId, recipeId }
        });
        if (existingRating) {
            existingRating.rating = rating;
            existingRating.comment = comment || null;
            await this.ratingRepository.save(existingRating);
        }
        else {
            const newRating = this.ratingRepository.create({
                userId,
                recipeId,
                rating,
                comment
            });
            await this.ratingRepository.save(newRating);
        }
        await this.updateRecipeRating(recipeId);
        return {
            status: 'success',
            message: 'Recipe rated successfully'
        };
    }
    calculateNutritionalTotals(ingredients) {
        const totals = ingredients.reduce((acc, ingredient) => {
            return {
                totalCalories: acc.totalCalories + (ingredient.calories || 0),
                totalProtein: acc.totalProtein + (ingredient.protein || 0),
                totalCarbs: acc.totalCarbs + (ingredient.carbs || 0),
                totalFat: acc.totalFat + (ingredient.fat || 0),
                totalFiber: acc.totalFiber + (ingredient.fiber || 0),
                totalSugar: acc.totalSugar + (ingredient.sugar || 0),
                totalSodium: acc.totalSodium + (ingredient.sodium || 0),
            };
        }, {
            totalCalories: 0,
            totalProtein: 0,
            totalCarbs: 0,
            totalFat: 0,
            totalFiber: 0,
            totalSugar: 0,
            totalSodium: 0,
        });
        Object.keys(totals).forEach(key => {
            totals[key] = Math.round(totals[key] * 100) / 100;
        });
        return totals;
    }
    async updateRecipeFavoritesCount(recipeId) {
        const count = await this.favoriteRepository.count({ where: { recipeId } });
        await this.recipeRepository.update(recipeId, { favoritesCount: count });
    }
    async updateRecipeRating(recipeId) {
        const ratings = await this.ratingRepository.find({ where: { recipeId } });
        if (ratings.length === 0) {
            await this.recipeRepository.update(recipeId, {
                averageRating: 0,
                ratingsCount: 0
            });
            return;
        }
        const averageRating = ratings.reduce((sum, rating) => sum + rating.rating, 0) / ratings.length;
        const roundedAverage = Math.round(averageRating * 100) / 100;
        await this.recipeRepository.update(recipeId, {
            averageRating: roundedAverage,
            ratingsCount: ratings.length
        });
    }
};
exports.RecipesService = RecipesService;
exports.RecipesService = RecipesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(recipe_entity_1.Recipe)),
    __param(1, (0, typeorm_1.InjectRepository)(user_favorite_recipe_entity_1.UserFavoriteRecipe)),
    __param(2, (0, typeorm_1.InjectRepository)(recipe_rating_entity_1.RecipeRating)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object])
], RecipesService);


/***/ }),
/* 89 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Recipe = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const user_favorite_recipe_entity_1 = __webpack_require__(90);
const recipe_rating_entity_1 = __webpack_require__(91);
let Recipe = class Recipe {
};
exports.Recipe = Recipe;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Recipe.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Recipe.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Recipe.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Array)
], Recipe.prototype, "ingredients", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Recipe.prototype, "instructions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], Recipe.prototype, "servings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], Recipe.prototype, "prepTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], Recipe.prototype, "cookTime", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Recipe.prototype, "difficulty", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Recipe.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2 }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalCalories", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2 }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalProtein", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2 }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalCarbs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2 }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalFat", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalFiber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalSugar", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 8, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Recipe.prototype, "totalSodium", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Recipe.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Recipe.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Recipe.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Recipe.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], Recipe.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_favorite_recipe_entity_1.UserFavoriteRecipe, favorite => favorite.recipe),
    __metadata("design:type", Array)
], Recipe.prototype, "favorites", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => recipe_rating_entity_1.RecipeRating, rating => rating.recipe),
    __metadata("design:type", Array)
], Recipe.prototype, "ratings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Recipe.prototype, "averageRating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Recipe.prototype, "ratingsCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Recipe.prototype, "favoritesCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], Recipe.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], Recipe.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], Recipe.prototype, "updatedAt", void 0);
exports.Recipe = Recipe = __decorate([
    (0, typeorm_1.Entity)('recipes')
], Recipe);


/***/ }),
/* 90 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UserFavoriteRecipe = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const recipe_entity_1 = __webpack_require__(89);
let UserFavoriteRecipe = class UserFavoriteRecipe {
};
exports.UserFavoriteRecipe = UserFavoriteRecipe;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserFavoriteRecipe.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], UserFavoriteRecipe.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'CASCADE' }),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], UserFavoriteRecipe.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], UserFavoriteRecipe.prototype, "recipeId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => recipe_entity_1.Recipe, recipe => recipe.favorites, { onDelete: 'CASCADE' }),
    __metadata("design:type", typeof (_b = typeof recipe_entity_1.Recipe !== "undefined" && recipe_entity_1.Recipe) === "function" ? _b : Object)
], UserFavoriteRecipe.prototype, "recipe", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], UserFavoriteRecipe.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], UserFavoriteRecipe.prototype, "createdAt", void 0);
exports.UserFavoriteRecipe = UserFavoriteRecipe = __decorate([
    (0, typeorm_1.Entity)('user_favorite_recipes'),
    (0, typeorm_1.Unique)(['userId', 'recipeId'])
], UserFavoriteRecipe);


/***/ }),
/* 91 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RecipeRating = void 0;
const typeorm_1 = __webpack_require__(12);
const user_entity_1 = __webpack_require__(13);
const recipe_entity_1 = __webpack_require__(89);
let RecipeRating = class RecipeRating {
};
exports.RecipeRating = RecipeRating;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RecipeRating.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RecipeRating.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'CASCADE' }),
    __metadata("design:type", typeof (_a = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _a : Object)
], RecipeRating.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RecipeRating.prototype, "recipeId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => recipe_entity_1.Recipe, recipe => recipe.ratings, { onDelete: 'CASCADE' }),
    __metadata("design:type", typeof (_b = typeof recipe_entity_1.Recipe !== "undefined" && recipe_entity_1.Recipe) === "function" ? _b : Object)
], RecipeRating.prototype, "recipe", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], RecipeRating.prototype, "rating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", Object)
], RecipeRating.prototype, "comment", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], RecipeRating.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], RecipeRating.prototype, "updatedAt", void 0);
exports.RecipeRating = RecipeRating = __decorate([
    (0, typeorm_1.Entity)('recipe_ratings'),
    (0, typeorm_1.Unique)(['userId', 'recipeId'])
], RecipeRating);


/***/ }),
/* 92 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CreateRecipeDto = exports.CreateRecipeIngredientDto = void 0;
const class_validator_1 = __webpack_require__(30);
const class_transformer_1 = __webpack_require__(57);
const swagger_1 = __webpack_require__(3);
class CreateRecipeIngredientDto {
}
exports.CreateRecipeIngredientDto = CreateRecipeIngredientDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeIngredientDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeIngredientDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeIngredientDto.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "protein", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "carbs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "fat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "calories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "fiber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "sugar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeIngredientDto.prototype, "sodium", void 0);
class CreateRecipeDto {
}
exports.CreateRecipeDto = CreateRecipeDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [CreateRecipeIngredientDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateRecipeIngredientDto),
    __metadata("design:type", Array)
], CreateRecipeDto.prototype, "ingredients", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeDto.prototype, "instructions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateRecipeDto.prototype, "servings", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeDto.prototype, "prepTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateRecipeDto.prototype, "cookTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsIn)(['easy', 'medium', 'hard']),
    __metadata("design:type", String)
], CreateRecipeDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsIn)(['breakfast', 'lunch', 'dinner', 'snack', 'dessert', 'drink']),
    __metadata("design:type", String)
], CreateRecipeDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecipeDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateRecipeDto.prototype, "isPublic", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateRecipeDto.prototype, "tags", void 0);


/***/ }),
/* 93 */
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.UpdateRecipeDto = void 0;
const swagger_1 = __webpack_require__(3);
const create_recipe_dto_1 = __webpack_require__(92);
class UpdateRecipeDto extends (0, swagger_1.PartialType)(create_recipe_dto_1.CreateRecipeDto) {
}
exports.UpdateRecipeDto = UpdateRecipeDto;


/***/ }),
/* 94 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AddFavoriteDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class AddFavoriteDto {
}
exports.AddFavoriteDto = AddFavoriteDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AddFavoriteDto.prototype, "recipeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AddFavoriteDto.prototype, "notes", void 0);


/***/ }),
/* 95 */
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RateRecipeDto = void 0;
const class_validator_1 = __webpack_require__(30);
const swagger_1 = __webpack_require__(3);
class RateRecipeDto {
}
exports.RateRecipeDto = RateRecipeDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RateRecipeDto.prototype, "recipeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ minimum: 1, maximum: 5 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], RateRecipeDto.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RateRecipeDto.prototype, "comment", void 0);


/***/ })
/******/ 	]);
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(1);
const common_1 = __webpack_require__(2);
const swagger_1 = __webpack_require__(3);
const app_module_1 = __webpack_require__(4);
async function bootstrap() {
    try {
        console.log('🚀 Starting SnapFit Backend...');
        const app = await core_1.NestFactory.create(app_module_1.AppModule);
        console.log('✅ NestJS application created');
        app.enableCors();
        console.log('✅ CORS enabled');
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            transform: true,
        }));
        console.log('✅ Validation pipes configured');
        const config = new swagger_1.DocumentBuilder()
            .setTitle('SnapFit API')
            .setDescription('The SnapFit API documentation')
            .setVersion('1.0')
            .addBearerAuth()
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api', app, document);
        console.log('✅ Swagger documentation configured');
        await app.listen(3000);
        console.log('🎉 SnapFit Backend is running on http://localhost:3000');
        console.log('📚 API Documentation available at http://localhost:3000/api');
    }
    catch (error) {
        console.error('❌ Error starting SnapFit Backend:', error);
        process.exit(1);
    }
}
bootstrap().catch((error) => {
    console.error('❌ Fatal error during bootstrap:', error);
    process.exit(1);
});

})();

/******/ })()
;