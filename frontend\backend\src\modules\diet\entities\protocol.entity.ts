import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Meal } from './meal.entity';
import { Supplement } from './supplement.entity';

@Entity('protocols')
export class Protocol {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  type: 'diet' | 'workout';

  @Column({ nullable: true })
  dietType: string; // 'cutting', 'bulking', 'maintenance', 'recomposition', 'performance'

  @Column()
  objective: string;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date', nullable: true })
  endDate: Date;

  @Column('jsonb')
  goals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    water: number;
  };

  @Column()
  waterCalculationMethod: 'weight' | 'manual';

  @Column({ nullable: true })
  notes: string;

  @Column({ default: 'active' })
  status: 'active' | 'completed' | 'archived';

  @ManyToOne(() => User)
  user: User;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  createdBy: User;

  @Column()
  createdById: string;

  @OneToMany(() => Meal, meal => meal.protocol)
  weeklyMeals: Meal[];

  @OneToMany(() => Supplement, supplement => supplement.protocol)
  supplements: Supplement[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}