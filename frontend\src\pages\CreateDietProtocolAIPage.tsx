import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, Brain, Sparkles } from 'lucide-react';
import { DietProtocolAIForm } from '../components/DietProtocolAIForm';
import { LoadingScreen } from '../components/LoadingScreen';
import { useCreateDietProtocolAI } from '../hooks/useProtocols';
import { useCheckActiveProtocol, useFinalizeProtocol } from '../hooks/useProtocolManagement';
import { apiService } from '../services/api';
import { toast } from 'react-toastify';
import { DietProtocolAIData } from '../types/protocol';

export function CreateDietProtocolAIPage() {
  const navigate = useNavigate();
  const createDietProtocolAI = useCreateDietProtocolAI();
  const checkActiveProtocol = useCheckActiveProtocol();
  const finalizeProtocol = useFinalizeProtocol();

  const handleGenerate = async (formData: DietProtocolAIData) => {
    try {
      console.log('🚀 Enviando dados para IA:', formData);

      // First, check if there's an active protocol and finalize it if needed
      try {
        const activeProtocol = await checkActiveProtocol.mutateAsync('diet');

        if (activeProtocol && activeProtocol.id) {
          console.log('🏁 Finalizing active diet protocol before creating new one');
          await finalizeProtocol.mutateAsync({
            protocolId: activeProtocol.id.toString(),
            protocolType: 'diet'
          });
          console.log('✅ Active diet protocol finalized successfully');
        }
      } catch (error) {
        console.log('ℹ️ No active diet protocol to finalize or error finalizing:', error);
      }

      // Now create the new protocol using the AI endpoint
      createDietProtocolAI.mutate(formData, {
        onSuccess: (createdProtocol) => {
          console.log('✅ Diet protocol created successfully:', createdProtocol);

          if (createdProtocol?.id) {
            // Verificar disponibilidade antes de redirecionar
            let attempts = 0;
            const maxAttempts = 10;
            const interval = setInterval(async () => {
              attempts++;
              try {
                const protocol = await apiService.get(`users/protocols/diet/${createdProtocol.id}`);
                if (protocol?.data?.has_protocol) {
                  clearInterval(interval);
                  navigate('/dashboard/diet');
                } else if (attempts >= maxAttempts) {
                  clearInterval(interval);
                  toast.error('Protocolo não encontrado após geração. Tente novamente.');
                }
              } catch (error) {
                console.error('Error checking protocol availability:', error);
              }
            }, 5000); // Check every 5 seconds
          } else {
            toast.error('Protocolo de dieta não foi criado corretamente');
          }
        },
        onError: (error) => {
          console.error('Error generating AI diet protocol:', error);
          // Error is handled by the hook
        }
      });
    } catch (error) {
      console.error('Error generating AI diet protocol:', error);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/diet');
  };

  if (createDietProtocolAI.isPending || checkActiveProtocol.isPending || finalizeProtocol.isPending) {
    return (
      <div className="min-h-screen bg-snapfit-black">
        <LoadingScreen
          type="diet"
          message="Gerando seu protocolo de dieta personalizado..."
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-snapfit-black">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <button
              onClick={handleCancel}
              className="p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                <Brain className="w-4 h-4 text-snapfit-green" />
              </div>
              <div>
                <h1 className="text-lg font-medium text-white">
                  Gerar Protocolo de Dieta com IA
                </h1>
                <div className="flex items-center gap-1.5 text-xs text-gray-400">
                  <Sparkles className="w-3 h-3 text-snapfit-green" />
                  <span>Protocolo nutricional personalizado baseado em seus dados</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 pb-20">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Formulário da IA */}
          <DietProtocolAIForm
            onGenerate={handleGenerate}
            onCancel={handleCancel}
          />

          {/* Informações sobre a IA */}
          <div className="bg-gradient-to-r from-snapfit-green/10 to-blue-500/10 rounded-xl p-4 border border-snapfit-green/20">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5">
                <Brain className="w-4 h-4 text-snapfit-green" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-white mb-2">Como funciona a IA Nutricional do SnapFit</h3>
                <div className="text-xs text-gray-400 space-y-1">
                  <p>• Analisa seus dados corporais, objetivos e preferências alimentares</p>
                  <p>• Calcula necessidades calóricas e distribuição de macronutrientes</p>
                  <p>• Considera restrições alimentares e alergias</p>
                  <p>• Gera cardápio personalizado com alimentos que você gosta</p>
                  <p>• Ajusta horários das refeições ao seu estilo de vida</p>
                  <p>• Inclui suplementação quando necessário</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
