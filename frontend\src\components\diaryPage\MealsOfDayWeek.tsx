import { Check, Edit2, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useEffect, useState } from "react";
import { apiService } from "../../services/api";
import { toast } from "react-toastify";
import LoadingOverlay from "../LoadingOverlay";
import { FoodSuggestionsAi } from "../FoodSuggestionsAi";
import { Dropdown, Loader, Modal } from "rsuite";
import 'rsuite/Dropdown/styles/index.css';
import { EllipsisVerticalIcon } from "@heroicons/react/20/solid";
import MealCheckModal from "../user/MealCheckModal";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import 'rsuite/Loader/styles/index.css';

interface MealsOfDayWeekProps {
    day: string;
    reloadMeals: any;
}

const MealsOfDayWeek = ({ day }: MealsOfDayWeekProps) => {
    const queryClient = useQueryClient();
    const completed = true;
    // const [meals, setMeals] = useState([]);
    const [loadingPage, setLoadingPage] = useState(false);
    const [selectedMeal, setSelectedMeal] = useState(null);
    const [refreshMeals, setRefreshMeals] = useState(false);
    const [isToday, setIsToday] = useState(false);
    const [uncheckedMealId, setUncheckedMealId] = useState(null);
    const [openUncheck, setOpenUncheck] = useState(false);

    const handleCloseUncheck = () => {
        setOpenUncheck(false);
    };

    // mutation uncheck meal
    const { mutate: uncheckMeal, isPending: isLoadingMutationUncheck } = useMutation({
      mutationFn: async (mealData: {
        id: number;
      }) => {
        setOpenUncheck(false);
        const response = await apiService.post(`users/protocols/diet/uncheck`, mealData);
        return response;
      },
      onSuccess: (data, variables: any) => {
        const dateString = day;

        // 1. Atualiza as queries relacionadas
        queryClient.invalidateQueries({ queryKey: ['meals', dateString] });
        queryClient.invalidateQueries({ queryKey: ['dailyDataNutritionalSummary', dateString] });
        // Invalidate dashboard meals query
        queryClient.invalidateQueries({ queryKey: ['dashboard', 'meals', dateString] });

        toast.success('Refeição desmarcada', {
          position: 'bottom-right',
        });
      },
      onError: (error) => {
        setOpenUncheck(true);
        console.error('Erro ao marcar refeição como consumida:', error);
        toast.error('Erro ao desmarcar refeição', {
          position: 'bottom-right',
        });
      }
    });



    const handleUncheckMeal = async () => {
      if (uncheckedMealId) {
        const mealData = {
          id: uncheckedMealId
        };
        uncheckMeal(mealData);
      }
    }

   const { data: meals, isLoading: isLoadingMeals, error: errorMeals } = useQuery({
    queryKey: ['meals', day],
    queryFn: async () => {
      console.log('🔄 MealsOfDayWeek: Fetching meals for day:', day);
      console.log('📅 MealsOfDayWeek: Date being sent:', `${day} 00:00:00`);
      console.log('🔑 MealsOfDayWeek: Access token:', localStorage.getItem('accessToken') ? 'Present' : 'Missing');

      try {
        const response: any = await apiService.get('users/protocols/diet/meals/active',
          { searchParams: { date: `${day} 00:00:00` } }
        );
        console.log('🍽️ MealsOfDayWeek: Full response:', response);
        console.log('📋 MealsOfDayWeek: Has protocol:', response?.data?.has_protocol);
        console.log('🥘 MealsOfDayWeek: Meals count:', response?.data?.meals?.length || 0);

        if (!response?.data?.has_protocol) {
          console.warn('⚠️ MealsOfDayWeek: User does not have an active diet protocol');
        }

        return response?.data?.has_protocol ? response?.data?.meals : [];
      } catch (error) {
        console.error('❌ MealsOfDayWeek: API error:', error);
        return [];
      }
    }
   });


        // mutation
        const { mutate: checkMeal, isPending: isLoadingMutationCheck } = useMutation({
          mutationFn: async (mealData: {
            meal_id: number;
          }) => {
            const response = await apiService.post(`users/protocols/diet/check`, mealData);
            return response;
          },
          onSuccess: async (data, variables: any) => {
            const dateString = day;

            // 1. Invalidar todas as queries relacionadas a refeições
            await Promise.all([
              queryClient.invalidateQueries({ queryKey: ['dashboard'] }),
              queryClient.invalidateQueries({ queryKey: ['dashboard', 'nutrition'] }),
              queryClient.invalidateQueries({ queryKey: ['dashboard', 'nutrition', 'meals', dateString] }),
              queryClient.invalidateQueries({ queryKey: ['dashboard', 'nutrition', 'summary', dateString] }),
              queryClient.invalidateQueries({ queryKey: ['dashboard', 'meals', dateString] }),
              queryClient.invalidateQueries({ queryKey: ['meals', dateString] }),
              queryClient.invalidateQueries({ queryKey: ['dailyDataNutritionalSummary', dateString] }),
              queryClient.invalidateQueries({ queryKey: ['daily-meals'] }),
              queryClient.invalidateQueries({ queryKey: ['diary'] }),
              queryClient.invalidateQueries({ queryKey: ['diary', 'meals', dateString] }),
              queryClient.invalidateQueries({ queryKey: ['diet-protocol'] }),
              queryClient.invalidateQueries({ queryKey: ['mealofdayweek'] })
            ]);

            // 2. Executa callback e fecha popover
            setSelectedMeal(null);

            // 3. Mostrar feedback de sucesso
            console.log('✅ Refeição marcada como consumida e cache invalidado');
          },
          onError: (error) => {
            console.error('Erro ao marcar refeição como consumida:', error);
          }
        });


        useEffect(() => {
            if (selectedMeal) {
              checkMeal({ meal_id: selectedMeal });
            }
        }, [selectedMeal]);

    useEffect(() => {
        const today = new Date().toISOString().split('T')[0];
        setIsToday(day === today);
    }, [day]);

    const handleMealSuggestionsAi = async (mealId: string) => {
      queryClient.setQueryData(['meals', day], (oldData: any) => {
        return oldData.map((m: any) =>
          m.id === mealId ? { ...m, suggestionsAiLoading: true } : m
        );
      });

      const apiFoodsSuggestionAi = 'users/ai/foods-suggestions';
      try {
        const query = { meal_id: mealId, meal_type: 'replacement' };
        const payload = {
          type: 'text',
          content: null,
        }
        const response: any = await apiService.post(apiFoodsSuggestionAi,
              payload,
              {
                searchParams: query,
                timeout: 15000 } // Otimizado para 15 segundos
            );

      // Processamento detalhado baseado no que vemos no Network tab
      console.log('🔄 Raw API response object:', response);
      console.log('🔄 Response.data:', response.data);
      console.log('🔄 Response.data type:', typeof response.data);
      console.log('🔄 Response.data keys:', Object.keys(response.data || {}));

      let suggestionsAiData = response.data;

      // Verificar se a resposta tem a estrutura esperada
      if (suggestionsAiData) {
        // Se é string, fazer parse
        if (typeof suggestionsAiData === 'string') {
          try {
            suggestionsAiData = JSON.parse(suggestionsAiData);
            console.log('🔄 Parsed string data:', suggestionsAiData);
          } catch (parseError) {
            console.error('Error parsing JSON string:', parseError);
            suggestionsAiData = [];
          }
        }

        // Verificar diferentes estruturas possíveis baseadas no Network tab
        if (suggestionsAiData.data && Array.isArray(suggestionsAiData.data)) {
          console.log('🔄 Found data array:', suggestionsAiData.data);
          suggestionsAiData = suggestionsAiData.data;
        } else if (suggestionsAiData.recommendations && Array.isArray(suggestionsAiData.recommendations)) {
          console.log('🔄 Found recommendations array:', suggestionsAiData.recommendations);
          suggestionsAiData = suggestionsAiData.recommendations;
        } else if (Array.isArray(suggestionsAiData)) {
          console.log('🔄 Data is already array:', suggestionsAiData);
        } else {
          console.warn('🔄 Unexpected data structure:', suggestionsAiData);
          // Tentar extrair arrays de qualquer propriedade
          const keys = Object.keys(suggestionsAiData);
          for (const key of keys) {
            if (Array.isArray(suggestionsAiData[key]) && suggestionsAiData[key].length > 0) {
              console.log(`🔄 Found array in key "${key}":`, suggestionsAiData[key]);
              suggestionsAiData = suggestionsAiData[key];
              break;
            }
          }

          // Se ainda não é array, criar array vazio
          if (!Array.isArray(suggestionsAiData)) {
            console.warn('🔄 Could not find array data, using empty array');
            suggestionsAiData = [];
          }
        }
      } else {
        console.warn('🔄 No data in response');
        suggestionsAiData = [];
      }

      console.log('🔄 Final processed suggestions:', suggestionsAiData);
      console.log('🔄 Final suggestions length:', suggestionsAiData.length);

      queryClient.setQueryData(['meals', day], (oldData: any) => {
        const updatedData = oldData.map((m: any) =>
          m.id === mealId ? { ...m, suggestionsAi: suggestionsAiData, suggestionsAiLoading: false } : m
        );
        console.log('🔄 Updated meal with suggestions:', updatedData.find(m => m.id === mealId));
        return updatedData;
      });
      } catch (error) {
        console.error('Error getting meal suggestions:', error);
      } finally {
        queryClient.setQueryData(['meals', day], (oldData: any) => {
          return oldData.map((m: any) =>
            m.id === mealId ? { ...m, suggestionsAiLoading: false, suggestionsAi: [] } : m
          );
        });

        // Show error message to user
        toast.error('Erro ao obter sugestões da IA. Tente novamente.');
      }
    };

    useEffect(() => {
      // fetchMeals();
    }, [refreshMeals]);

    return (
        <>
        {(isLoadingMeals || isLoadingMutationCheck || isLoadingMutationUncheck) && <Loader backdrop vertical className="z-[99999]" />}
        {loadingPage && <LoadingOverlay />}
        <div className="space-y-4">

            {meals?.map((meal: any) => (
            <div
            key={meal.id}
            className={`bg-snapfit-gray rounded-lg shadow-sm p-3 sm:p-4 border ${
                meal.completed ? 'border-l-4 border-snapfit-green border-snapfit-green/30' : 'border-l-4 border-snapfit-green/10 border-snapfit-green/10'
              }`}
            >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex flex-col items-left md:flex-row md:items-center gap-3">
                  <h3 className="text-base sm:text-lg font-medium text-white">{meal?.name}</h3>
                  <span className="text-xs text-gray-400">{meal?.nutrients?.calories} kcal • {meal?.nutrients?.protein}g P • {meal?.nutrients?.carbs}g C • {meal?.nutrients?.fat}g G</span>
                  {!meal.completed && (
                  <button
                    onClick={() => {
                      handleMealSuggestionsAi(meal.id);
                      /*
                      setMeals((prev: any) => prev.map((m: any) =>
                        m.id === meal.id ? { ...m, suggestions: true } : m
                      ));
                      */
                    }}
                    className="flex items-center gap-1.5 text-xs text-gray-400 hover:text-snapfit-green transition-colors"
                  >
                    <Sparkles className="w-3.5 h-3.5" />
                    <span className="font-light">gerar tabela de substituição</span>
                  </button>
                  )}
                </div>
                <div className="mt-1 text-xs sm:text-sm text-gray-400">
                    {meal?.foods.map((food: any, index: number) => (
                    <span
                    key={index}
                    className="mr-2">
                      {food.name} <small>({food.quantity} {food.unit})</small> {index < meal.foods.length - 1 && <span style={{ margin: '0 4px' }}>•</span>}
                    </span>
                    ))}
                </div>
              </div>
              <div className="flex items-center gap-1 sm:gap-2 ml-4">
                <span className="text-xs sm:text-sm text-snapfit-green">{meal?.meal_time.split(":").slice(0, 2).join(":")}</span>
                <div>
                <button
                  className="
                  hidden
                  p-1.5 sm:p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors"
                >
                  <Edit2 className="w-4 h-4 sm:w-5 sm:h-5" />
                </button>
                {(
                <>
                {meal.completed && (
                <button
                  onClick={() =>
                  {
                    if(meal.completed) {
                      setUncheckedMealId(meal.id);
                      setOpenUncheck(true);
                      return;
                    }
                    setSelectedMeal(meal.id)
                  }
                }
                  className={`p-1.5 sm:p-2 rounded-full transition-colors
                    ${
                      meal.completed
                      ? 'bg-snapfit-green/20 text-snapfit-green hover:text-red-400 border border-snapfit-green/30'
                      : 'bg-snapfit-dark-gray text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 border border-snapfit-green/10'
                  }`}
                >
                  <Check className="w-4 h-4 sm:w-5 sm:h-5" />
                </button>
                )}
                {!meal.completed && (
    <MealCheckModal
    id={meal.id}
    dateInit={day}
    timeInit={meal?.meal_time}
    onConfirm={(date, time) => {
      console.log('Confirmado:', date, time);
      queryClient.invalidateQueries({ queryKey: ['meals', day] });
      queryClient.invalidateQueries({ queryKey: ['dailyDataNutritionalSummary', day] });
    }}
    onCancel={() => console.log('Cancelado')}
    />
    )}
                </>
                )}
                <button
                  className="
                  hidden
                  p-1.5 sm:p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <X className="w-4 h-4 sm:w-5 sm:h-5" />
                </button>
                </div>
              </div>
            </div>
            {(meal?.suggestionsAiLoading || meal?.suggestionsAi) && (
            <FoodSuggestionsAi
              mealId={meal.id}
              dateInit={day}
              timeInit={meal?.meal_time}
              isLoading={meal?.suggestionsAiLoading}
              suggestions={meal?.suggestionsAi}
              />
            )}
            </div>
            ))}
        </div>



        <Modal backdrop="static" role="alertdialog" open={openUncheck} onClose={handleCloseUncheck} size="xs">
        {(isLoadingMutationUncheck || isLoadingMeals) && <Loader backdrop vertical className="z-[99999]" />}
        <Modal.Body className="bg-snapfit-gray text-white border border-snapfit-green/20 rounded-lg">
          Deseja desmarcar esta refeição?
        </Modal.Body>
        <Modal.Footer className="bg-snapfit-gray border-t border-snapfit-green/20">
          <div className="flex gap-2 justify-center">
          <button className="bg-snapfit-green text-black px-4 py-2 rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95" onClick={handleUncheckMeal}>
            Sim
          </button>
          <button className="text-gray-400 hover:text-snapfit-green px-4 py-2" onClick={handleCloseUncheck}>
            Cancelar
          </button>
          </div>
        </Modal.Footer>
      </Modal>
        </>
    )
}

export default MealsOfDayWeek;