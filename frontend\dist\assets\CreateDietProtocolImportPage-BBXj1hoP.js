import{t as E,r,j as e,aD as R,aO as G,U as P,ae as D,ag as H,l as L,aj as T}from"./index-BwF2e626.js";import{D as d}from"./download-DOD2aajn.js";function Q(){const b=E(),[A,C]=r.useState(!0),[I,f]=r.useState(!1),[h,j]=r.useState([]),[y,v]=r.useState([]),[l,N]=r.useState("available"),[a,x]=r.useState(null),[V,p]=r.useState(null),[U,B]=r.useState(null);r.useEffect(()=>{q(),M()},[]);const q=async()=>{try{const t=await fetch("https://api.mysnapfit.com.br/professional-protocols/available",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(t.ok){const i=await t.json();j(i.filter(o=>o.type==="diet"))}}catch(t){console.error("Error fetching available protocols:",t),j([{id:"1",name:"Protocolo de Emagrecimento Avançado",objective:"Perda de peso saudável com déficit calórico controlado",createdBy:{id:"nutri-1",name:"Dra. Ana Silva",photo:"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-1*24*60*60*1e3).toISOString(),notes:"Protocolo de 12 semanas com déficit calórico controlado. Inclui refeições balanceadas e suplementação.",duration:12,targetCalories:1800,weeklyMeals:{monday:[{id:"m1",name:"Café da Manhã",time:"07:00",foods:[{id:"f1",name:"Aveia",quantity:"50g",calories:190,protein:7,carbs:32,fat:4},{id:"f2",name:"Banana",quantity:"1 unidade",calories:90,protein:1,carbs:23,fat:0},{id:"f3",name:"Whey Protein",quantity:"30g",calories:120,protein:24,carbs:2,fat:1}]},{id:"m2",name:"Almoço",time:"12:00",foods:[{id:"f4",name:"Peito de Frango",quantity:"150g",calories:250,protein:47,carbs:0,fat:5},{id:"f5",name:"Arroz Integral",quantity:"80g",calories:280,protein:6,carbs:58,fat:2},{id:"f6",name:"Brócolis",quantity:"100g",calories:25,protein:3,carbs:5,fat:0}]},{id:"m3",name:"Jantar",time:"19:00",foods:[{id:"f7",name:"Salmão",quantity:"120g",calories:220,protein:25,carbs:0,fat:13},{id:"f8",name:"Batata Doce",quantity:"100g",calories:86,protein:2,carbs:20,fat:0},{id:"f9",name:"Aspargos",quantity:"100g",calories:20,protein:2,carbs:4,fat:0}]}],tuesday:[{id:"m4",name:"Café da Manhã",time:"07:00",foods:[{id:"f10",name:"Ovo",quantity:"2 unidades",calories:140,protein:12,carbs:1,fat:10},{id:"f11",name:"Pão Integral",quantity:"2 fatias",calories:160,protein:6,carbs:30,fat:2},{id:"f12",name:"Abacate",quantity:"50g",calories:80,protein:1,carbs:4,fat:7}]}],wednesday:[],thursday:[],friday:[],saturday:[],sunday:[]}},{id:"2",name:"Dieta para Ganho de Massa",objective:"Aumento de massa muscular com controle de gordura",createdBy:{id:"nutri-1",name:"Dra. Ana Silva",photo:"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=50&h=50&fit=crop"},createdAt:new Date().toISOString(),notes:"Protocolo hipercalórico com foco em proteínas de alta qualidade e carboidratos complexos."},{id:"3",name:"Protocolo Anti-Inflamatório",objective:"Redução de inflamação e melhoria da saúde geral",createdBy:{id:"nutri-2",name:"Dr. João Oliveira",photo:"https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-2*24*60*60*1e3).toISOString(),notes:"Dieta rica em antioxidantes e ômega-3. Ideal para recuperação e bem-estar geral."}])}finally{C(!1)}},M=async()=>{try{const t=await fetch("https://api.mysnapfit.com.br/professional-protocols/history?type=diet",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(t.ok){const i=await t.json();v(i)}}catch(t){console.error("Error fetching protocol history:",t),v([{id:"hist-1",name:"Protocolo de Ganho de Massa - Concluído",objective:"Aumento de massa muscular com superávit calórico",createdBy:{id:"nutri-1",name:"Dra. Ana Silva",photo:"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-45*24*60*60*1e3).toISOString(),notes:"Protocolo de 10 semanas para ganho de massa. Resultados excelentes: +3kg de massa magra!"},{id:"hist-2",name:"Dieta de Manutenção",objective:"Manutenção do peso e composição corporal",createdBy:{id:"nutri-1",name:"Dra. Ana Silva",photo:"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-90*24*60*60*1e3).toISOString(),notes:"Primeiro protocolo personalizado. Base para todas as progressões futuras."}])}},O=()=>{b("/dashboard/diet")},z=t=>{x(t),w("monday")},[m,w]=r.useState("monday"),k=async t=>{p(t),f(!0);try{if((await fetch(`https://api.mysnapfit.com.br/professional-protocols/import/${t}`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok){const o=h.find(c=>c.id===t);B((o==null?void 0:o.name)||"Protocolo"),setTimeout(()=>{b("/dashboard/diet")},2e3)}else p(null)}catch{p(null)}finally{f(!1)}},$=t=>new Date(t).toLocaleDateString("pt-BR",{day:"numeric",month:"short",year:"numeric"});if(I)return e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(R,{type:"diet",message:"Importando protocolo de dieta..."})});const S=l==="available"?h:y;return e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-between p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:O,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(G,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(d,{className:"w-4 h-4 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-medium text-white",children:"Importar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(P,{className:"w-3 h-3 text-blue-400"}),e.jsx("span",{children:"Protocolos do seu nutricionista"})]})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-1 border border-snapfit-green/20",children:e.jsxs("div",{className:"grid grid-cols-2 gap-1",children:[e.jsxs("button",{onClick:()=>N("available"),className:`flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${l==="available"?"bg-snapfit-green text-black":"text-gray-400 hover:text-white"}`,children:[e.jsx(d,{className:"w-4 h-4"}),"Disponíveis (",h.length,")"]}),e.jsxs("button",{onClick:()=>N("history"),className:`flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${l==="history"?"bg-snapfit-green text-black":"text-gray-400 hover:text-white"}`,children:[e.jsx(D,{className:"w-4 h-4"}),"Histórico (",y.length,")"]})]})}),A?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-8 border border-snapfit-green/20 text-center",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-500/30",children:e.jsx(d,{className:"w-6 h-6 text-blue-400 animate-pulse"})}),e.jsx("p",{className:"text-white font-medium mb-2",children:"Carregando protocolos..."}),e.jsx("p",{className:"text-sm text-gray-400",children:"Buscando protocolos do seu nutricionista"})]}):S.length===0?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-8 border border-snapfit-green/20 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4",children:l==="available"?e.jsx(d,{className:"w-8 h-8 text-gray-400"}):e.jsx(D,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:l==="available"?"Nenhum protocolo disponível":"Nenhum protocolo no histórico"}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:l==="available"?"Seu nutricionista ainda não criou protocolos para você":"Você ainda não importou nenhum protocolo"}),l==="available"&&e.jsx("div",{className:"bg-blue-500/10 rounded-lg p-4 border border-blue-500/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(H,{className:"w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-1",children:"Como receber protocolos?"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"1. Certifique-se de estar sincronizado com seu nutricionista"}),e.jsx("p",{children:"2. Seu nutricionista criará protocolos personalizados"}),e.jsx("p",{children:"3. Você receberá uma notificação quando estiver disponível"})]})]})]})})]}):e.jsx("div",{className:"space-y-4",children:S.map(t=>e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-1",children:t.name}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:t.objective}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(P,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:t.createdBy.name})]}),e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(L,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:$(t.createdAt)})]})]})]}),e.jsx("div",{className:"flex items-center gap-2 ml-4",children:e.jsx("img",{src:t.createdBy.photo||"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=40&h=40&fit=crop",alt:t.createdBy.name,className:"w-10 h-10 rounded-full object-cover border border-snapfit-green/30"})})]}),t.notes&&e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 mb-4 border border-snapfit-green/10",children:e.jsx("p",{className:"text-sm text-gray-300",children:t.notes})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>z(t),className:"flex-1 px-4 py-2.5 text-sm font-medium text-gray-400 border border-gray-600 rounded-lg hover:text-white hover:border-gray-500 transition-colors",children:"Visualizar"}),e.jsxs("button",{onClick:()=>k(t.id),className:"flex-1 flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(d,{className:"w-4 h-4"}),l==="available"?"Importar":"Reimportar"]})]})]},t.id))})]})}),a&&e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:a.name}),e.jsx("p",{className:"text-sm text-gray-400 mt-1",children:a.objective})]}),e.jsx("button",{onClick:()=>x(null),className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(T,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Duração"}),e.jsxs("div",{className:"text-lg font-medium text-white",children:[a.duration," semanas"]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Calorias Alvo"}),e.jsxs("div",{className:"text-lg font-medium text-white",children:[a.targetCalories," kcal"]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Criado por"}),e.jsx("div",{className:"text-lg font-medium text-white",children:a.createdBy.name})]})]}),a.notes&&e.jsxs("div",{className:"bg-blue-500/10 rounded-lg p-4 mb-6 border border-blue-500/20",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-400 mb-2",children:"Observações do Nutricionista"}),e.jsx("p",{className:"text-sm text-gray-300",children:a.notes})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Plano Alimentar"}),e.jsx("div",{className:"flex overflow-x-auto scrollbar-hide gap-1 sm:gap-2 border-b border-gray-700 pb-2",children:Object.keys(a.weeklyMeals||{}).map((t,i)=>{const o={monday:"SEG",tuesday:"TER",wednesday:"QUA",thursday:"QUI",friday:"SEX",saturday:"SAB",sunday:"DOM"},c=["text-snapfit-green border-snapfit-green","text-blue-400 border-blue-400","text-purple-400 border-purple-400","text-orange-400 border-orange-400","text-pink-400 border-pink-400","text-cyan-400 border-cyan-400","text-yellow-400 border-yellow-400"],u=c[i%c.length],g=m===t;return e.jsx("button",{onClick:()=>w(t),className:`px-3 py-2 text-sm font-medium border-b-2 transition-colors whitespace-nowrap flex-shrink-0 ${g?u:"text-gray-400 border-transparent hover:text-gray-300"}`,children:o[t]},t)})}),m&&a.weeklyMeals&&a.weeklyMeals[m]&&e.jsx("div",{className:"space-y-4",children:a.weeklyMeals[m].length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:e.jsx("p",{children:"Nenhuma refeição programada para este dia"})}):a.weeklyMeals[m].map((t,i)=>{const o=t.foods.reduce((s,n)=>s+n.calories,0),c=t.foods.reduce((s,n)=>s+n.protein,0),u=t.foods.reduce((s,n)=>s+n.carbs,0),g=t.foods.reduce((s,n)=>s+n.fat,0);return e.jsxs("div",{className:"border border-snapfit-green/30 bg-snapfit-green/5 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-snapfit-green text-base",children:t.name}),e.jsx("div",{className:"text-sm text-gray-400",children:t.time})]}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-white font-medium",children:o}),e.jsx("div",{children:"kcal"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-white font-medium",children:[Math.round(c),"g"]}),e.jsx("div",{children:"P"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-white font-medium",children:[Math.round(u),"g"]}),e.jsx("div",{children:"C"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-white font-medium",children:[Math.round(g),"g"]}),e.jsx("div",{children:"G"})]})]})]}),e.jsx("div",{className:"space-y-2",children:t.foods.map((s,n)=>e.jsxs("div",{className:"bg-snapfit-black/30 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-white font-medium",children:s.name}),e.jsx("div",{className:"text-sm text-gray-400",children:s.quantity})]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-400 mt-1",children:[e.jsxs("span",{children:[s.calories,"kcal"]}),e.jsxs("span",{children:[s.protein,"g P"]}),e.jsxs("span",{children:[s.carbs,"g C"]}),e.jsxs("span",{children:[s.fat,"g G"]})]})]},n))})]},i)})})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-6 border-t border-snapfit-green/20",children:[e.jsx("button",{onClick:()=>x(null),className:"px-4 py-2.5 text-sm font-medium text-gray-400 border border-gray-600 rounded-lg hover:text-white hover:border-gray-500 transition-colors",children:"Fechar"}),e.jsxs("button",{onClick:()=>{k(a.id),x(null)},className:"flex items-center gap-2 px-4 py-2.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(d,{className:"w-4 h-4"}),"Importar Protocolo"]})]})]})})]})}export{Q as CreateDietProtocolImportPage};
