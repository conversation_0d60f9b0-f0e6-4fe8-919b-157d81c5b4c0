import{t as M,aR as E,r as p,b as g,y as x,j as e,aO as j,H as m,a as P,bi as C}from"./index-DU5i5Dy8.js";function S(){const u=M(),{protocolId:n}=E(),[f,y]=p.useState(null),[w,h]=p.useState(!0),[v,b]=p.useState(null);p.useEffect(()=>{n&&N()},[n]);const N=async()=>{var t,c,r,l;try{h(!0),b(null),console.log("Fetching protocol data for ID:",n);try{console.log("🔍 Tentando buscar protocolo ativo...");const o=await g.get("users/protocols/diet/active");console.log("🔍 Resposta da API:",o);let a=null;if(((o==null?void 0:o.status)==="success"&&((t=o==null?void 0:o.data)!=null&&t.has_protocol)||o!=null&&o.data&&o.data.id)&&(a=o.data),a&&a.id.toString()===n){console.log("✅ Protocolo ativo encontrado:",a);const i={name:a.name,objective:a.objective,type:a.type||"cutting",goals:a.nutritional_goals||a.goals||{calories:0,protein:0,carbs:0,fat:0,water:0},weeklyMeals:a.weeklyMeals||a.meals||{},supplements:a.supplements||[],notes:a.notes||a.general_notes||"",waterCalculationMethod:a.waterCalculationMethod||"weight"};y(i);return}console.log("⚠️ Protocolo ativo não corresponde ao ID solicitado")}catch(o){console.log("❌ Backend não disponível, usando dados mock para teste:",o.message)}if(console.log("💾 Carregando dados mock para protocolo:",n),localStorage.getItem("mock-protocol-deleted")==="true"&&(n==="mock-protocol-id-123"||n==="78")){console.log("🗑️ Protocolo mock foi deletado, redirecionando..."),b("Protocolo não encontrado ou foi removido"),h(!1);return}if(n==="mock-protocol-id-123"||n==="78"){const o=localStorage.getItem("mock-protocol-data");let a;if(o)try{const i=JSON.parse(o);a={name:i.name,objective:i.objective,type:i.type||"cutting",goals:i.goals,weeklyMeals:i.weeklyMeals,supplements:i.supplements,notes:i.notes,waterCalculationMethod:i.waterCalculationMethod},console.log("Loaded saved mock data:",a)}catch{console.log("Error parsing saved data, using default"),a=null}a||(a={name:"Protocolo de Teste para Edição",objective:"Testar funcionalidade de edição de protocolos",type:"cutting",goals:{calories:2e3,protein:150,carbs:200,fat:70,water:2500},weeklyMeals:{monday:[{id:"meal-1",name:"Café da Manhã",time:"08:00",foods:[{id:"food-1",name:"Aveia",quantity:50,unit:"g",calories:190,protein:6.5,carbs:32,fat:3.5},{id:"food-2",name:"Banana",quantity:1,unit:"unidade",calories:105,protein:1.3,carbs:27,fat:.3}]},{id:"meal-2",name:"Almoço",time:"12:00",foods:[{id:"food-3",name:"Peito de Frango",quantity:150,unit:"g",calories:248,protein:46.2,carbs:0,fat:5.4},{id:"food-4",name:"Arroz Integral",quantity:100,unit:"g",calories:123,protein:2.6,carbs:25,fat:.9}]}],tuesday:[{id:"meal-3",name:"Café da Manhã",time:"08:00",foods:[{id:"food-5",name:"Pão Integral",quantity:2,unit:"fatias",calories:160,protein:6,carbs:30,fat:2}]}],wednesday:[{id:"meal-4",name:"Café da Manhã",time:"08:00",foods:[{id:"food-6",name:"Iogurte Grego",quantity:200,unit:"g",calories:130,protein:20,carbs:9,fat:0}]}],thursday:[],friday:[],saturday:[],sunday:[]},supplements:[{name:"Whey Protein",dosage:"30g",supplement_time:"Pós-treino",notes:"Misturar com água"}],notes:"Protocolo de teste para verificar funcionalidade de edição",waterCalculationMethod:"manual"}),console.log("✅ Dados do protocolo carregados:",a),console.log("🔍 Verificando estrutura das refeições:",a.weeklyMeals),console.log("📋 Tipo do protocolo:",a.type),y(a)}else throw new Error("Protocol not found")}catch(s){console.error("Error fetching protocol data:",s);let o="Erro ao carregar dados do protocolo. Verifique se o protocolo existe.";((c=s==null?void 0:s.response)==null?void 0:c.status)===404?o="Protocolo não encontrado. Verifique se o ID está correto.":((r=s==null?void 0:s.response)==null?void 0:r.status)===401?o="Não autorizado. Faça login novamente.":((l=s==null?void 0:s.response)==null?void 0:l.status)===403&&(o="Acesso negado. Você não tem permissão para acessar este protocolo."),b(o),x.error("Erro ao carregar protocolo",{position:"bottom-right"})}finally{h(!1)}},k=async t=>{var c;try{console.log("Saving protocol with data:",t);try{const r={name:t.name,objective:t.objective,notes:t.notes||"",nutritional_goals:t.nutritional_goals||t.goals,weeklyMeals:t.weeklyMeals,supplements:((c=t.supplements)==null?void 0:c.map(s=>({name:s.name,dosage:s.dosage,supplement_time:s.supplement_time||s.time,notes:s.notes})))||[],waterCalculationMethod:t.waterCalculationMethod||"weight",type:t.type||"cutting",startDate:t.startDate||new Date().toISOString()};console.log("🔄 Enviando dados para atualização:",r);let l=null;try{l=await g.put(`diet/protocols/${n}`,r)}catch{console.log("First endpoint failed, trying alternative...");try{l=await g.put(`users/protocols/diet/${n}`,r)}catch{console.log("Second endpoint failed, trying create as fallback..."),l=await g.post("users/protocols/diet",{...r,type_id:1})}}if(l){x.success("Protocolo de dieta atualizado com sucesso!",{position:"bottom-right"}),setTimeout(()=>{u("/dashboard/diet")},2e3);return}}catch(r){console.error("❌ Erro ao atualizar protocolo no backend:",r),console.log("💾 Tentando salvar localmente como fallback")}if(n==="mock-protocol-id-123"){const r={id:n,name:t.name,objective:t.objective,goals:t.goals,weeklyMeals:t.weeklyMeals,supplements:t.supplements||[],notes:t.notes||"",waterCalculationMethod:t.waterCalculationMethod||"weight",lastUpdated:new Date().toISOString()};localStorage.setItem("mock-protocol-data",JSON.stringify(r)),console.log("Mock save successful:",r),x.success("Protocolo de dieta atualizado com sucesso! (Modo de teste)",{position:"bottom-right"}),setTimeout(()=>{u("/dashboard/diet")},2e3)}else throw new Error("Failed to update diet protocol")}catch(r){console.error("Error updating diet protocol:",r),x.error("Erro ao atualizar protocolo de dieta",{position:"bottom-right"})}},d=()=>{u("/dashboard/diet")};return w?e.jsxs("div",{className:"min-h-screen bg-snapfit-black text-white",children:[e.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:d,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors",children:e.jsx(j,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(m,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Carregando dados do protocolo..."})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20 flex items-center justify-center min-h-[400px]",children:e.jsxs("div",{className:"text-center",children:[e.jsx(P,{className:"w-8 h-8 text-snapfit-green animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Carregando dados do protocolo..."})]})})})})]}):v?e.jsxs("div",{className:"min-h-screen bg-snapfit-black text-white",children:[e.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:d,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors",children:e.jsx(j,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(m,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Erro ao carregar protocolo"})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-red-500/10 rounded-xl p-6 border border-red-500/20 text-center",children:[e.jsxs("div",{className:"text-red-400 mb-4",children:[e.jsx(m,{className:"w-12 h-12 mx-auto mb-2"}),e.jsx("h3",{className:"text-lg font-medium",children:"Erro ao Carregar Protocolo"})]}),e.jsx("p",{className:"text-gray-400 mb-6",children:v}),e.jsx("button",{onClick:d,className:"px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:"Voltar para Dieta"})]})})})]}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black text-white",children:[e.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:d,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors",children:e.jsx(j,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-blue-400 font-medium",children:"Modo Edição"})]})]}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400 mt-1",children:[e.jsx(m,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Modificar protocolo existente"})]})]})]})})}),e.jsx("div",{className:"p-4 pb-32",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"bg-amber-500/10 rounded-xl p-4 border border-amber-500/20 mb-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-amber-500/20 rounded-full flex items-center justify-center border border-amber-500/30 flex-shrink-0 mt-0.5",children:e.jsx(m,{className:"w-4 h-4 text-amber-500"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Editando Protocolo Existente"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"• Modifique objetivos nutricionais conforme necessário"}),e.jsx("p",{children:"• Ajuste refeições e alimentos do protocolo"}),e.jsx("p",{children:"• Atualize suplementos e horários"}),e.jsx("p",{children:"• Salve as alterações para aplicar ao protocolo ativo"})]})]})]})}),e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20",children:f?e.jsxs(e.Fragment,{children:[console.log("🔄 Renderizando DietProtocolManualCreator com dados:",f),e.jsx(C,{onSave:k,onCancel:d,initialData:f})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Carregando dados do protocolo..."})]})})]})})]})}export{S as EditDietProtocolPage,S as default};
