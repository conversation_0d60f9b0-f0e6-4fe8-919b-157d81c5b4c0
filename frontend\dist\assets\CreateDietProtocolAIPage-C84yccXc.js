import{u as f,at as j,y as u,b as h,t as y,aA as b,aC as A,j as e,aD as w,aO as P,B as g,aT as N,bj as C}from"./index-Dwgh6cj0.js";const x={diet:{active:["diet","protocols","active"],list:["diet","protocols"],detail:t=>["diet","protocols",t]},workout:{active:["workout","protocols","active"],list:["workout","protocols"],detail:t=>["workout","protocols",t]}};function I(){const t=f();return{invalidateProtocols:r=>{r?t.invalidateQueries({queryKey:[r,"protocols"]}):(t.invalidateQueries({queryKey:["diet","protocols"]}),t.invalidateQueries({queryKey:["workout","protocols"]}))},clearProtocolCache:r=>{r?t.removeQueries({queryKey:[r,"protocols"]}):(t.removeQueries({queryKey:["diet","protocols"]}),t.removeQueries({queryKey:["workout","protocols"]}))},updateProtocolCache:(r,i)=>{t.setQueryData(x[r].active,i),t.setQueryData(x[r].list,n=>{if(!n)return[i];const s=n.findIndex(l=>l.id===i.id);if(s>=0){const l=[...n];return l[s]=i,l}return[i,...n]})}}}function k(){f();const{invalidateProtocols:t}=I();return j({mutationFn:async o=>{var c;console.log("🤖 useCreateDietProtocolAI: Gerando protocolo com IA:",o);const a=await h.post("users/protocols/diet/ai",o,{timeout:6e5});if(((c=a==null?void 0:a.data)==null?void 0:c.status)==="success")return a.data.data;throw new Error("Falha ao gerar protocolo de dieta com IA")},onSuccess:o=>{console.log("✅ useCreateDietProtocolAI: Protocolo gerado com sucesso:",o),t("diet"),u.success("Protocolo de dieta gerado com IA com sucesso!")},onError:o=>{var c,r,i,n;console.error("❌ useCreateDietProtocolAI: Erro ao gerar protocolo:",o);let a="Erro ao gerar protocolo de dieta com IA. Tente novamente.";throw(r=(c=o==null?void 0:o.response)==null?void 0:c.data)!=null&&r.errors&&Array.isArray(o.response.data.errors)?a=o.response.data.errors.join(" "):(n=(i=o==null?void 0:o.response)==null?void 0:i.data)!=null&&n.message&&(Array.isArray(o.response.data.message)?a=o.response.data.message.join(" "):a=o.response.data.message),u.error(a),o}})}function z(){const t=y(),o=k(),a=b(),c=A(),r=async n=>{try{console.log("🚀 Enviando dados para IA:",n);try{const s=await a.mutateAsync("diet");s&&s.id&&(console.log("🏁 Finalizing active diet protocol before creating new one"),await c.mutateAsync({protocolId:s.id.toString(),protocolType:"diet"}),console.log("✅ Active diet protocol finalized successfully"))}catch(s){console.log("ℹ️ No active diet protocol to finalize or error finalizing:",s)}o.mutate(n,{onSuccess:s=>{if(console.log("✅ Diet protocol created successfully:",s),s!=null&&s.id){let l=0;const v=10,p=setInterval(async()=>{var m;l++;try{const d=await h.get(`users/protocols/diet/${s.id}`);(m=d==null?void 0:d.data)!=null&&m.has_protocol?(clearInterval(p),t("/dashboard/diet")):l>=v&&(clearInterval(p),u.error("Protocolo não encontrado após geração. Tente novamente."))}catch(d){console.error("Error checking protocol availability:",d)}},5e3)}else u.error("Protocolo de dieta não foi criado corretamente")},onError:s=>{console.error("Error generating AI diet protocol:",s)}})}catch(s){console.error("Error generating AI diet protocol:",s)}},i=()=>{t("/dashboard/diet")};return o.isPending||a.isPending||c.isPending?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(w,{type:"diet",message:"Gerando seu protocolo de dieta personalizado..."})}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-between p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:i,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(P,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(g,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-medium text-white",children:"Gerar Protocolo de Dieta com IA"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(N,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Protocolo nutricional personalizado baseado em seus dados"})]})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsx(C,{onGenerate:r,onCancel:i}),e.jsx("div",{className:"bg-gradient-to-r from-snapfit-green/10 to-blue-500/10 rounded-xl p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5",children:e.jsx(g,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Como funciona a IA Nutricional do SnapFit"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"• Analisa seus dados corporais, objetivos e preferências alimentares"}),e.jsx("p",{children:"• Calcula necessidades calóricas e distribuição de macronutrientes"}),e.jsx("p",{children:"• Considera restrições alimentares e alergias"}),e.jsx("p",{children:"• Gera cardápio personalizado com alimentos que você gosta"}),e.jsx("p",{children:"• Ajusta horários das refeições ao seu estilo de vida"}),e.jsx("p",{children:"• Inclui suplementação quando necessário"})]})]})]})})]})})]})}export{z as CreateDietProtocolAIPage};
