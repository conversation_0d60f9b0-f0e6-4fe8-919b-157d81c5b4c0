import{c as Le,s as Qe,R as He,j as e,D as We,ae as Ue,as as _e,M as Je,P as Ke,r as v,aT as Xe,c5 as Ye,G as Ze,bF as Ge,ac as ye,ad as et,H as pe,I as ge,f as ce,aV as tt,a8 as Ce,az as Ne,bo as st,aj as at,b as we,y as xe,u as rt,at as it,c6 as ot,c7 as nt,c8 as lt,c9 as dt}from"./index-PNQkLtwW.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ct=[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]],mt=Le("thumbs-up",ct);function xt({day:w,current:T=0,goal:I=2500,onAdd:se,loadDailyData:F,onShowHistory:D,onShowSettings:B}){const{dailyHydration:O,addWater:Y,removeWater:le,isLoading:U}=Qe(),[f,me]=He.useState(!1),u=O.current||T,R=O.goal||I,g=Math.min(100,u/R*100),V=[100,200,300,500],ae=async h=>{try{await Y(h)}catch(C){console.error("Error adding water:",C)}},L=async h=>{if(!(u-h<0))try{await le(h)}catch(C){console.error("Error removing water:",C)}};return e.jsxs("div",{className:"bg-snapfit-gray backdrop-blur-sm rounded-xl sm:rounded-3xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-400/10 rounded-full flex items-center justify-center border border-blue-400/30",children:e.jsx(We,{className:"w-5 h-5 text-blue-400"})}),e.jsx("h2",{className:"text-sm sm:text-base font-bold text-white",children:"Hidratação"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-gray-400 water-goal","data-goal":R,children:["Meta: ",R,"ml"]}),e.jsxs("div",{className:"flex gap-1",children:[D&&e.jsx("button",{onClick:D,className:"p-1.5 text-gray-400 hover:text-blue-400 hover:bg-blue-500/10 rounded-lg transition-colors",title:"Ver histórico",children:e.jsx(Ue,{className:"w-4 h-4"})}),B&&e.jsx("button",{onClick:B,className:"p-1.5 text-gray-400 hover:text-blue-400 hover:bg-blue-500/10 rounded-lg transition-colors",title:"Configurações",children:e.jsx(_e,{className:"w-4 h-4"})})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 sm:gap-6 items-center",children:[e.jsxs("div",{className:"relative w-full sm:w-1/3 h-40 sm:h-48 bg-snapfit-dark-gray rounded-xl border border-blue-400/20 overflow-hidden",children:[e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-blue-400/30 backdrop-blur-sm transition-all duration-1000 ease-in-out water-visual",style:{height:`${g}%`},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-blue-500/40 to-blue-400/20"}),e.jsx("div",{className:"absolute inset-0 overflow-hidden",children:[...Array(10)].map((h,C)=>e.jsx("div",{className:"absolute bg-white/30 rounded-full animate-bubble",style:{width:`${Math.random()*10+5}px`,height:`${Math.random()*10+5}px`,left:`${Math.random()*100}%`,animationDelay:`${Math.random()*5}s`,animationDuration:`${Math.random()*3+2}s`}},C))})]}),e.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"text-3xl sm:text-4xl font-bold text-white water-current",children:u}),e.jsx("div",{className:"text-sm text-gray-300",children:"ml"}),e.jsxs("div",{className:"mt-2 text-xs text-blue-300 water-percentage","data-goal":R,children:[Math.round(g),"%"]})]})]}),e.jsxs("div",{className:"w-full sm:w-2/3 space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("button",{onClick:()=>L(200),disabled:u<=0||f||U,className:"flex items-center justify-center gap-2 p-3 bg-red-500/20 hover:bg-red-500/30 rounded-xl border border-red-400/30 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors water-button","data-amount":"-200",children:[e.jsx(Je,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{children:"Remover 200ml"})]}),e.jsxs("button",{onClick:()=>ae(200),disabled:f||U,className:"flex items-center justify-center gap-2 p-3 bg-blue-500/20 hover:bg-blue-500/30 rounded-xl border border-blue-400/30 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors water-button","data-amount":"200",children:[e.jsx(Ke,{className:"w-4 h-4 text-blue-400"}),e.jsx("span",{children:"Adicionar 200ml"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-xs text-gray-400 font-medium",children:"Remover:"}),e.jsx("div",{className:"grid grid-cols-4 gap-2",children:V.map(h=>e.jsxs("button",{onClick:()=>L(h),disabled:u<h||f||U,className:"p-2 sm:p-3 bg-red-500/10 hover:bg-red-500/20 rounded-xl border border-red-400/20 text-white text-sm disabled:opacity-30 disabled:cursor-not-allowed transition-colors water-button","data-amount":-h,children:["-",h,"ml"]},`remove-${h}`))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-xs text-gray-400 font-medium",children:"Adicionar:"}),e.jsx("div",{className:"grid grid-cols-4 gap-2",children:V.map(h=>e.jsxs("button",{onClick:()=>ae(h),disabled:f||U,className:"p-2 sm:p-3 bg-blue-500/10 hover:bg-blue-500/20 rounded-xl border border-blue-400/20 text-white text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-colors water-button","data-amount":h,children:["+",h,"ml"]},`add-${h}`))})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-blue-400/20",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Dicas para se manter hidratado:"}),e.jsxs("ul",{className:"text-xs text-gray-300 space-y-1 pl-4 list-disc",children:[e.jsx("li",{children:"Mantenha uma garrafa de água sempre por perto"}),e.jsx("li",{children:"Defina lembretes para beber água regularmente"}),e.jsx("li",{children:"Aumente a ingestão em dias de treino intenso"})]})]})]})]})]})}const ut=(w,T,I,se,F=2e3,D=150,B=200,O=70,Y=[],le=[],U)=>{const f=Math.max(0,F-w),me=Math.max(0,D-T),u=Math.max(0,B-I),R=Math.max(0,O-se),g=Math.round(f/F*100),V=Math.round(me/D*100),ae=Math.round(u/B*100),L=Math.round(R/O*100),h=w<0,C=V<80,$=ae>110,J=L>110;let x=F-w,re=D-T,ne=B-I,oe=O-se;if(Y&&Y.length>0){for(const K of Y)if(K.foods&&K.foods.length>0)for(const Z of K.foods)x+=Z.calories,re+=Z.protein,ne+=Z.carbs,oe+=Z.fat}if(le&&le.length>0)for(const K of le)x-=K.caloriesBurned;const m=Math.round(x/F*100),p=Math.round(re/D*100),S=Math.round(ne/B*100),a=Math.round(oe/O*100),A=x>F,Q=x<F*.85,c=re<D*.9,P=ne>B*1.1,ie=oe>O*1.1;let r="on-track";h||C&&V<60?r="off-track":(C||$||J)&&(r="warning"),r==="on-track"&&(A||Q||c)&&(r="warning"),U==="poor"&&r==="warning"&&(r="off-track");let b="";if(r==="off-track"?h?(b=`Você excedeu sua meta calórica em ${Math.abs(w)} calorias (${Math.round(Math.abs(w)/F*100)}% acima). `,C&&(b+=`Além disso, você atingiu apenas ${V}% da sua meta de proteínas. `),b+="Recomendo ajustes para compensar o excesso calórico."):C&&(b=`Você atingiu apenas ${V}% da sua meta de proteínas. Considere adicionar alimentos ricos em proteínas nas próximas refeições.`):r==="warning"?C?b=`Você está abaixo da meta de proteínas (${V}%). Considere adicionar mais proteínas às próximas refeições.`:$?b=`Você consumiu ${ae}% da sua meta de carboidratos. Considere reduzir carboidratos nas próximas refeições.`:J&&(b=`Você consumiu ${L}% da sua meta de gorduras. Considere opções com menos gordura para as próximas refeições.`):b="Você está seguindo bem o seu plano! Continue assim para atingir seus objetivos.",r!=="off-track"){if(A){const K=Math.round(x-F);b+=` Projeção: Você terminará o dia com aproximadamente ${K} calorias acima da meta (${m}%).`}else if(Q){const K=Math.round(F-x);b+=` Projeção: Você terminará o dia com aproximadamente ${K} calorias abaixo da meta (${m}%).`}c&&(b+=` Sua ingestão projetada de proteínas está em ${p}% da meta.`)}return U==="poor"&&(b+=" Considerando sua qualidade de sono ruim, recomendo reduzir a intensidade das atividades físicas e ajustar as calorias."),{caloriesPercentage:g,proteinPercentage:V,carbsPercentage:ae,fatPercentage:L,status:r,alertMessage:b,isCaloriesOverBudget:h,isProteinDeficient:C,isCarbsExcessive:$,isFatExcessive:J,projectedCalories:x,projectedProtein:re,projectedCarbs:ne,projectedFat:oe,projectedCaloriesPercentage:m,projectedProteinPercentage:p,projectedCarbsPercentage:S,projectedFatPercentage:a,isProjectedCaloriesOverBudget:A,isProjectedCaloriesUnderBudget:Q,isProjectedProteinDeficient:c,isProjectedCarbsExcessive:P,isProjectedFatExcessive:ie,caloriesAdjustmentNeeded:A?Math.round(x-F):Q?Math.round(F-x):0,proteinAdjustmentNeeded:c?Math.round(D-re):0,sleepQualityIssue:U==="poor"}};function ft({caloriesRemaining:w,proteinRemaining:T,carbsRemaining:I,fatRemaining:se,mealsEaten:F,currentTime:D,sleepQuality:B,plannedMeals:O=[],plannedActivities:Y=[],userPreferences:le,onActivityAdded:U,onMealAdded:f}){console.log("AISuggestionCard rendered with updated code - v3");const[me,u]=v.useState(null),[R,g]=v.useState(!1),[V,ae]=v.useState(null),[L,h]=v.useState(!1),[C,$]=v.useState(""),[J,x]=v.useState([]),[re,ne]=v.useState(!0),[oe,m]=v.useState(!0),[p,S]=v.useState(!1),[a,A]=v.useState(null),[Q,c]=v.useState(!1),[P,ie]=v.useState(!1),[r,b]=v.useState(null),[K,Z]=v.useState(null),[X,de]=v.useState("diagnosis"),[ue,fe]=v.useState(!1),[he,je]=v.useState(!1),Ae=async t=>{var i;if(t.type==="workout"){fe(!0);try{const n=parseInt(((i=t.timeEstimate)==null?void 0:i.replace(" min",""))||"30"),N={id:{"Caminhada rápida":"1",Caminhada:"1","Corrida leve":"2",Corrida:"2",Ciclismo:"3",Natação:"4","Treino HIIT":"5",HIIT:"5",Yoga:"6",Musculação:"7",Dança:"8","Exercícios cardiovasculares":"1","Treino de força":"7","Treino rápido de alta intensidade":"5","Exercícios de baixa intensidade":"1","Atividade física leve":"1",Cardio:"1",Aeróbico:"1"}[t.title]||"1",duration:n},j=await we.post("users/daily/workouts_activities",N);xe.success(`${t.title} adicionada com sucesso!`,{position:"bottom-right"}),U&&U(),A(null),c(!1)}catch(n){console.error("Error adding activity:",n),xe.error("Erro ao adicionar atividade. Tente novamente.",{position:"bottom-right"})}finally{fe(!1)}}},Se=async t=>{if(t.type==="meal"){fe(!0);try{xe.info("Funcionalidade de adicionar refeições será implementada em breve!",{position:"bottom-right"}),f&&f(),A(null),c(!1)}catch(i){console.error("Error adding meal:",i),xe.error("Erro ao adicionar refeição. Tente novamente.",{position:"bottom-right"})}finally{fe(!1)}}},ke=async t=>{t.type==="workout"?await Ae(t):t.type==="meal"?await Se(t):(xe.info("Ajuste aplicado com sucesso!",{position:"bottom-right"}),A(null),c(!1))},qe=Math.max(0,w),Ee=Math.max(0,T),ze=Math.max(0,I),Oe=Math.max(0,se),s=ut(qe,Ee,ze,Oe,2e3,150,200,70,O,Y,B),Me=async()=>{var t,i,n,k;g(!0),ae(null),c(!1),A(null),ie(!1),b(null),x([]),Z(null),de("diagnosis");try{const d=`
        Macros restantes: ${w}kcal, ${T}g proteína, ${I}g carboidratos, ${se}g gordura.
        Hora atual: ${D}.
        Qualidade do sono: ${B||"não informada"}.
        Refeições já consumidas: ${F.map(j=>j.name).join(", ")}.
        Análise do plano: ${JSON.stringify(s)}.
      `;console.log("🤖 Gerando sugestões de IA com contexto:",d);const N=await we.post("users/ai/foods-suggestions",{type:"text",content:d,meal_type:"suggestion"},{timeout:15e3});if(console.log("✅ Resposta da API de sugestões:",N),N!=null&&N.data&&Array.isArray(N.data)&&N.data.length>0){const j=N.data,o={type:"meal",title:"Sugestão Personalizada da IA",description:"Baseado no seu perfil e necessidades nutricionais atuais.",calories:((t=j[0])==null?void 0:t.calories)||0,protein:((i=j[0])==null?void 0:i.protein)||0,carbs:((n=j[0])==null?void 0:n.carbs)||0,fat:((k=j[0])==null?void 0:k.fat)||0,mealDetails:{items:j.map(l=>({name:l.name,quantity:`${l.quantity}${l.unit}`,calories:l.calories,protein:l.protein,carbs:l.carbs,fat:l.fat})),totalCalories:j.reduce((l,y)=>l+(y.calories||0),0),totalProtein:j.reduce((l,y)=>l+(y.protein||0),0),totalCarbs:j.reduce((l,y)=>l+(y.carbs||0),0),totalFat:j.reduce((l,y)=>l+(y.fat||0),0),prepTime:"15 minutos"}};u(o)}else console.warn("⚠️ API não retornou sugestões válidas, usando fallback local"),$e()}catch(d){console.error("❌ Erro ao buscar sugestões da API:",d),$e()}finally{g(!1)}},$e=()=>{if(s.caloriesAdjustmentNeeded>0){const t={type:"meal",title:"Ajuste calórico necessário",description:`Você está ${s.caloriesAdjustmentNeeded} calorias acima da meta. Considere reduzir as porções da próxima refeição ou fazer uma caminhada de 30 minutos.`,calories:-s.caloriesAdjustmentNeeded};u(t)}else if(s.proteinAdjustmentNeeded>0){const t={type:"meal",title:"Aumente sua proteína",description:`Você precisa de mais ${s.proteinAdjustmentNeeded}g de proteína. Considere adicionar peito de frango grelhado, ovos ou um shake de proteína.`,protein:s.proteinAdjustmentNeeded,calories:s.proteinAdjustmentNeeded*4};u(t)}else u({type:"meal",title:"Seu plano está equilibrado!",description:"Parabéns! Você está no caminho certo. Continue mantendo esse equilíbrio nutricional. Que tal um lanche leve como uma fruta ou iogurte?",calories:100,protein:5,carbs:15,fat:2})},Te=t=>{ae(t)},Fe=()=>{g(!0),setTimeout(()=>{const t=Math.abs(s.caloriesAdjustmentNeeded)||200,i=[{id:"walking",title:"Caminhada Moderada",description:`${Math.round(t/5)} minutos de caminhada`,calories:-t,timeEstimate:`${Math.round(t/5)} min`,type:"workout"},{id:"running",title:"Corrida Leve",description:`${Math.round(t/10)} minutos de corrida`,calories:-t,timeEstimate:`${Math.round(t/10)} min`,type:"workout"},{id:"cycling",title:"Ciclismo",description:`${Math.round(t/8)} minutos de bicicleta`,calories:-t,timeEstimate:`${Math.round(t/8)} min`,type:"workout"}],n={type:"choice",title:"Exercícios Cardiovasculares",description:`Escolha uma atividade para queimar ${t} calorias:`,options:i};u(n),c(!0),g(!1)},1e3)},De=()=>{g(!0),setTimeout(()=>{const t=Math.abs(s.caloriesAdjustmentNeeded)||200,i=[{id:"bodyweight",title:"Exercícios Corporais",description:`${Math.round(t/6)} minutos de flexões, agachamentos e abdominais`,calories:-t,timeEstimate:`${Math.round(t/6)} min`,type:"workout"},{id:"weights",title:"Musculação",description:`${Math.round(t/8)} minutos de treino com pesos`,calories:-t,timeEstimate:`${Math.round(t/8)} min`,type:"workout"},{id:"functional",title:"Treino Funcional",description:`${Math.round(t/7)} minutos de exercícios funcionais`,calories:-t,timeEstimate:`${Math.round(t/7)} min`,type:"workout"}],n={type:"choice",title:"Exercícios de Força",description:`Escolha uma atividade para queimar ${t} calorias:`,options:i};u(n),c(!0),g(!1)},1e3)},Be=()=>{g(!0),setTimeout(()=>{if(!O||O.length===0){x(k=>[...k,{role:"ai",content:"Não encontrei refeições planejadas para hoje. Aqui estão algumas sugestões de substituições baseadas em refeições comuns:"}]),u({type:"meal-replacement",title:"Substituições de Refeições Recomendadas",description:"Aqui estão algumas opções de substituição para refeições comuns:",options:[{id:"breakfast-replacement",title:"Café da manhã mais leve",description:"Substituir café da manhã tradicional (pão, manteiga, café com leite) por opção mais leve.",type:"meal",calories:250,protein:15,carbs:30,fat:8,substitution:{original:{name:"Café da manhã tradicional",calories:450,protein:12,carbs:60,fat:15},replacement:{name:"Café da manhã leve",calories:250,protein:15,carbs:30,fat:8},benefits:["Menos 200 calorias","Mais proteína (+3g)","Menos carboidratos (-30g)","Menos gordura (-7g)"],originalMeal:{name:"Café da manhã tradicional",description:"Café da manhã com pão, manteiga e café com leite",items:[{name:"Pão francês",quantity:"2 unidades (100g)",calories:300,protein:8,carbs:50,fat:4},{name:"Manteiga",quantity:"2 colheres (20g)",calories:150,protein:0,carbs:0,fat:16},{name:"Café com leite integral",quantity:"1 xícara (200ml)",calories:100,protein:4,carbs:10,fat:4}],totalCalories:450,totalProtein:12,totalCarbs:60,totalFat:15},replacementMeal:{name:"Café da manhã leve",description:"Café da manhã com omelete de claras e torrada integral",items:[{name:"Omelete de claras",quantity:"3 claras (90g)",calories:50,protein:10,carbs:1,fat:0},{name:"Torrada integral",quantity:"1 fatia (30g)",calories:80,protein:3,carbs:15,fat:1},{name:"Queijo cottage",quantity:"2 colheres (50g)",calories:60,protein:7,carbs:2,fat:2},{name:"Café com leite desnatado",quantity:"1 xícara (200ml)",calories:60,protein:4,carbs:8,fat:0}],totalCalories:250,totalProtein:15,totalCarbs:30,totalFat:8}}},{id:"lunch-replacement",title:"Almoço com menos carboidratos",description:"Substituir almoço tradicional (arroz, feijão, carne) por opção com menos carboidratos.",type:"meal",calories:400,protein:35,carbs:25,fat:15,substitution:{original:{name:"Almoço tradicional",calories:650,protein:30,carbs:80,fat:20},replacement:{name:"Almoço com menos carboidratos",calories:400,protein:35,carbs:25,fat:15},benefits:["Menos 250 calorias","Mais proteína (+5g)","Muito menos carboidratos (-55g)","Menos gordura (-5g)"],originalMeal:{name:"Almoço tradicional",description:"Almoço com arroz, feijão e carne",items:[{name:"Arroz branco",quantity:"4 colheres (200g)",calories:260,protein:5,carbs:58,fat:0},{name:"Feijão",quantity:"2 conchas (150g)",calories:120,protein:7,carbs:22,fat:1},{name:"Carne bovina",quantity:"1 bife médio (100g)",calories:250,protein:26,carbs:0,fat:17},{name:"Óleo para preparo",quantity:"1 colher (10ml)",calories:90,protein:0,carbs:0,fat:10}],totalCalories:650,totalProtein:30,totalCarbs:80,totalFat:20},replacementMeal:{name:"Almoço com menos carboidratos",description:"Almoço com salada, legumes e frango grelhado",items:[{name:"Peito de frango grelhado",quantity:"150g",calories:250,protein:30,carbs:0,fat:8},{name:"Mix de folhas verdes",quantity:"2 xícaras (100g)",calories:25,protein:2,carbs:5,fat:0},{name:"Legumes cozidos",quantity:"1 xícara (150g)",calories:60,protein:3,carbs:12,fat:0},{name:"Azeite de oliva",quantity:"1 colher (10ml)",calories:90,protein:0,carbs:0,fat:10},{name:"Quinoa cozida",quantity:"2 colheres (50g)",calories:60,protein:2,carbs:10,fat:1}],totalCalories:400,totalProtein:35,totalCarbs:25,totalFat:15}}},{id:"dinner-replacement",title:"Jantar mais leve",description:"Substituir jantar tradicional por opção mais leve e rica em proteínas.",type:"meal",calories:300,protein:30,carbs:20,fat:10,substitution:{original:{name:"Jantar tradicional",calories:550,protein:25,carbs:60,fat:20},replacement:{name:"Jantar leve",calories:300,protein:30,carbs:20,fat:10},benefits:["Menos 250 calorias","Mais proteína (+5g)","Menos carboidratos (-40g)","Menos gordura (-10g)","Melhor para o sono"],originalMeal:{name:"Jantar tradicional",description:"Jantar com macarrão, molho e carne moída",items:[{name:"Macarrão",quantity:"1 prato (200g)",calories:300,protein:10,carbs:60,fat:2},{name:"Molho de tomate",quantity:"3 colheres (75g)",calories:50,protein:2,carbs:10,fat:0},{name:"Carne moída",quantity:"100g",calories:200,protein:20,carbs:0,fat:14},{name:"Queijo parmesão",quantity:"1 colher (10g)",calories:40,protein:3,carbs:0,fat:3}],totalCalories:550,totalProtein:25,totalCarbs:60,totalFat:20},replacementMeal:{name:"Jantar leve",description:"Jantar com omelete e salada",items:[{name:"Omelete (2 ovos)",quantity:"2 unidades (100g)",calories:160,protein:14,carbs:1,fat:10},{name:"Queijo branco",quantity:"30g",calories:80,protein:6,carbs:1,fat:6},{name:"Salada verde",quantity:"2 xícaras (100g)",calories:25,protein:2,carbs:5,fat:0},{name:"Batata doce",quantity:"1 pequena (80g)",calories:70,protein:1,carbs:16,fat:0},{name:"Azeite de oliva",quantity:"1/2 colher (5ml)",calories:45,protein:0,carbs:0,fat:5}],totalCalories:300,totalProtein:30,totalCarbs:20,totalFat:10}}}]}),g(!1);return}const t=[];O.forEach((n,k)=>{let d=0,N=0,j=0,o=0;const l=[];n.foods.forEach(W=>{d+=W.calories,N+=W.protein,j+=W.carbs,o+=W.fat,l.push({name:W.name,quantity:W.quantity,calories:W.calories,protein:W.protein,carbs:W.carbs,fat:W.fat})});let y=[],M=0,q=0,E=0,z=0;const H=parseInt(n.time.split(":")[0]),G=H<10||n.name.toLowerCase().includes("café")||n.name.toLowerCase().includes("manhã"),ee=H>=11&&H<=14||n.name.toLowerCase().includes("almoço"),te=H>=18||n.name.toLowerCase().includes("jantar");G?y=[{name:"Omelete de claras",quantity:"3 claras (90g)",calories:50,protein:10,carbs:1,fat:0},{name:"Torrada integral",quantity:"1 fatia (30g)",calories:80,protein:3,carbs:15,fat:1},{name:"Queijo cottage",quantity:"2 colheres (50g)",calories:60,protein:7,carbs:2,fat:2},{name:"Café com leite desnatado",quantity:"1 xícara (200ml)",calories:60,protein:4,carbs:8,fat:0}]:ee?y=[{name:"Peito de frango grelhado",quantity:"150g",calories:250,protein:30,carbs:0,fat:8},{name:"Mix de folhas verdes",quantity:"2 xícaras (100g)",calories:25,protein:2,carbs:5,fat:0},{name:"Legumes cozidos",quantity:"1 xícara (150g)",calories:60,protein:3,carbs:12,fat:0},{name:"Azeite de oliva",quantity:"1 colher (10ml)",calories:90,protein:0,carbs:0,fat:10},{name:"Quinoa cozida",quantity:"2 colheres (50g)",calories:60,protein:2,carbs:10,fat:1}]:te?y=[{name:"Omelete (2 ovos)",quantity:"2 unidades (100g)",calories:160,protein:14,carbs:1,fat:10},{name:"Queijo branco",quantity:"30g",calories:80,protein:6,carbs:1,fat:6},{name:"Salada verde",quantity:"2 xícaras (100g)",calories:25,protein:2,carbs:5,fat:0},{name:"Batata doce",quantity:"1 pequena (80g)",calories:70,protein:1,carbs:16,fat:0},{name:"Azeite de oliva",quantity:"1/2 colher (5ml)",calories:45,protein:0,carbs:0,fat:5}]:y=[{name:"Iogurte grego",quantity:"1 pote (170g)",calories:100,protein:15,carbs:6,fat:0},{name:"Frutas vermelhas",quantity:"1/2 xícara (75g)",calories:40,protein:1,carbs:10,fat:0},{name:"Castanhas",quantity:"10g",calories:60,protein:2,carbs:2,fat:5}],y.forEach(W=>{M+=W.calories,q+=W.protein,E+=W.carbs,z+=W.fat});const be=G?"café da manhã":ee?"almoço":te?"jantar":"lanche";t.push({id:`meal-replacement-${k}`,title:`Substituir ${n.name}`,description:`Trocar ${n.name} (${d} kcal) por um ${be} mais leve (${M} kcal).`,type:"meal",calories:M-d,protein:q-N,carbs:E-j,fat:z-o,substitution:{original:{name:n.name,calories:d,protein:N,carbs:j,fat:o},replacement:{name:`${be} alternativo`,calories:M,protein:q,carbs:E,fat:z},benefits:[`${Math.abs(M-d)} calorias a ${M<d?"menos":"mais"}`,`${Math.abs(q-N)}g de proteína a ${q>N?"mais":"menos"}`,`${Math.abs(E-j)}g de carboidratos a ${E<j?"menos":"mais"}`,`${Math.abs(z-o)}g de gordura a ${z<o?"menos":"mais"}`],originalMeal:{name:n.name,description:`${n.name} original com ${d} calorias`,items:l,totalCalories:d,totalProtein:N,totalCarbs:j,totalFat:o},replacementMeal:{name:`${be} alternativo`,description:`${be} alternativo com ${M} calorias`,items:y,totalCalories:M,totalProtein:q,totalCarbs:E,totalFat:z}}})}),u({type:"meal-replacement",title:"Substituições de Refeições Personalizadas",description:"Aqui estão algumas opções de substituição para suas refeições planejadas:",options:t}),g(!1),x(n=>[...n,{role:"ai",content:`Analisei suas refeições planejadas e criei ${t.length} opções de substituição que podem ajudar você a atingir sua meta. Cada opção mostra detalhes completos da refeição original e da substituição sugerida, incluindo todos os ingredientes e quantidades.`}])},1500)},Ie=()=>{g(!0),setTimeout(()=>{if(!O||O.length===0){x(k=>[...k,{role:"ai",content:"Não encontrei refeições planejadas para hoje. Aqui estão algumas sugestões de ajustes de porções para refeições comuns:"}]),u({type:"portion-adjustment",title:"Ajustes de Porções Recomendados",description:"Aqui estão algumas opções de ajustes de porções para refeições comuns:",options:[{id:"reduce-carbs-portion",title:"Reduzir porções de carboidratos",description:"Reduzir as porções de arroz, pão, massas e outros carboidratos em 30-50%.",type:"adjustment",calories:-150,carbs:-30,portionChange:{food:"Carboidratos (arroz, pão, massas)",original:{quantity:"Porção normal (100-200g)",calories:250,protein:5,carbs:50,fat:1},adjusted:{quantity:"Porção reduzida (50-100g)",calories:125,protein:2.5,carbs:25,fat:.5},add:{name:"Vegetais",quantity:"1-2 xícaras (100-200g)",calories:50,protein:3,carbs:10,fat:0},benefits:["Redução de 125 calorias","Redução de 25g de carboidratos","Aumento de fibras e nutrientes","Maior volume e saciedade"]}},{id:"reduce-fat-portion",title:"Reduzir gorduras adicionadas",description:"Reduzir pela metade a quantidade de óleos, azeites e molhos gordurosos.",type:"adjustment",calories:-120,fat:-13,portionChange:{food:"Gorduras adicionadas (óleos, azeites, molhos)",original:{quantity:"Porção normal (15-30ml)",calories:135,protein:0,carbs:0,fat:15},adjusted:{quantity:"Porção reduzida (7-15ml)",calories:67,protein:0,carbs:0,fat:7.5},benefits:["Redução de 68 calorias","Redução de 7.5g de gordura","Mesmo sabor com metade da gordura","Melhor para saúde cardiovascular"]}},{id:"increase-protein-portion",title:"Aumentar porções de proteína",description:"Aumentar as porções de proteínas magras como frango, peixe, ovos ou tofu.",type:"adjustment",calories:100,protein:20,portionChange:{food:"Proteínas magras (frango, peixe, ovos, tofu)",original:{quantity:"Porção normal (100g)",calories:150,protein:25,carbs:0,fat:5},adjusted:{quantity:"Porção aumentada (150g)",calories:225,protein:37.5,carbs:0,fat:7.5},benefits:["Aumento de 75 calorias","Aumento de 12.5g de proteína","Maior saciedade","Melhor recuperação muscular","Maior termogênese (gasto calórico)"]}}]}),g(!1);return}const t=[];O.forEach((n,k)=>{const d=n.foods.filter(o=>o.carbs/o.calories>.15),N=n.foods.filter(o=>o.fat/o.calories>.3),j=n.foods.filter(o=>o.protein/o.calories>.2);if(d.length>0){const o=d[0],l=o.quantity,y=`${Math.round(parseInt(l)*.7)}${l.replace(/[0-9]/g,"")}`,M=o.calories,q=o.carbs,E=o.protein,z=o.fat,H=Math.round(M*.7),G=Math.round(q*.7),ee=Math.round(E*.7),te=Math.round(z*.7);t.push({id:`reduce-carbs-${k}`,title:`Reduzir ${o.name} em ${n.name}`,description:`Reduzir a porção de ${o.name} de ${l} para ${y}.`,type:"adjustment",calories:H-M,carbs:G-q,protein:ee-E,fat:te-z,portionChange:{food:o.name,original:{quantity:l,calories:M,protein:E,carbs:q,fat:z},adjusted:{quantity:y,calories:H,protein:ee,carbs:G,fat:te},add:{name:"Vegetais variados",quantity:"1 xícara (100g)",calories:30,protein:2,carbs:5,fat:0},benefits:[`Redução de ${M-H} calorias`,`Redução de ${q-G}g de carboidratos`,"Aumento de fibras e nutrientes com vegetais","Maior volume e saciedade"]}})}if(N.length>0){const o=N[0],l=o.quantity,y=`${Math.round(parseInt(l)*.5)}${l.replace(/[0-9]/g,"")}`,M=o.calories,q=o.carbs,E=o.protein,z=o.fat,H=Math.round(M*.5),G=Math.round(q*.5),ee=Math.round(E*.5),te=Math.round(z*.5);t.push({id:`reduce-fat-${k}`,title:`Reduzir ${o.name} em ${n.name}`,description:`Reduzir a porção de ${o.name} de ${l} para ${y}.`,type:"adjustment",calories:H-M,carbs:G-q,protein:ee-E,fat:te-z,portionChange:{food:o.name,original:{quantity:l,calories:M,protein:E,carbs:q,fat:z},adjusted:{quantity:y,calories:H,protein:ee,carbs:G,fat:te},benefits:[`Redução de ${M-H} calorias`,`Redução de ${z-te}g de gordura`,"Mesmo sabor com menos gordura","Melhor para saúde cardiovascular"]}})}if(j.length>0&&s.isProjectedProteinDeficient){const o=j[0],l=o.quantity,y=`${Math.round(parseInt(l)*1.5)}${l.replace(/[0-9]/g,"")}`,M=o.calories,q=o.carbs,E=o.protein,z=o.fat,H=Math.round(M*1.5),G=Math.round(q*1.5),ee=Math.round(E*1.5),te=Math.round(z*1.5);t.push({id:`increase-protein-${k}`,title:`Aumentar ${o.name} em ${n.name}`,description:`Aumentar a porção de ${o.name} de ${l} para ${y}.`,type:"adjustment",calories:H-M,carbs:G-q,protein:ee-E,fat:te-z,portionChange:{food:o.name,original:{quantity:l,calories:M,protein:E,carbs:q,fat:z},adjusted:{quantity:y,calories:H,protein:ee,carbs:G,fat:te},benefits:[`Aumento de ${ee-E}g de proteína`,"Maior saciedade","Melhor recuperação muscular","Maior termogênese (gasto calórico)"]}})}}),u({type:"portion-adjustment",title:"Ajustes de Porções Personalizados",description:"Aqui estão algumas opções de ajustes de porções para suas refeições planejadas:",options:t}),g(!1),x(n=>[...n,{role:"ai",content:`Analisei suas refeições planejadas e criei ${t.length} opções de ajustes de porções que podem ajudar você a atingir sua meta. Cada opção mostra detalhes completos das porções originais e ajustadas, incluindo o impacto nutricional.`}])},1500)},ve=()=>{a&&(ie(!0),b({...a}),x(t=>[...t,{role:"user",content:"Eu gostaria de personalizar esta opção."}]),setTimeout(()=>{const t="Claro! Você pode ajustar os detalhes conforme sua preferência. O que você gostaria de modificar?";x(i=>[...i,{role:"ai",content:t}])},1e3))},Re=()=>{r&&(A(r),ie(!1),x(t=>[...t,{role:"user",content:`Eu personalizei para: ${r.title} - ${r.description}`}]),setTimeout(()=>{const t="Perfeito! Suas modificações foram salvas. Esta opção personalizada se encaixa bem no seu plano diário.";x(i=>[...i,{role:"ai",content:t}])},1e3))},Ve=(t,i)=>{x(k=>[...k,{role:"ai",content:i}]),u({type:"choice",title:"Opções Sugeridas",description:i,options:t}),c(!0)},_=async t=>{if(t.preventDefault(),!C.trim())return;const i=C;$(""),x(n=>[...n,{role:"user",content:i}]);try{const n=`
        Pergunta do usuário: ${i}
        Macros restantes: ${w}kcal, ${T}g proteína, ${I}g carboidratos, ${se}g gordura.
        Hora atual: ${D}.
        Qualidade do sono: ${B||"não informada"}.
      `;let d=(await we.post("users/ai/foods-suggestions",{type:"text",content:n,meal_type:"chat"},{timeout:15e3})).data,N="",j=null;if(typeof d=="string")try{d=JSON.parse(d).recommendations||[]}catch(o){console.error("Error parsing AI suggestions:",o),d=[]}if(d&&d.length>0){const o=Array.isArray(d[0])?d.flat():d;N="Baseado na sua pergunta, aqui estão algumas sugestões personalizadas:",j=o.slice(0,3).map((l,y)=>({id:`chat-${y}`,type:"meal",title:l.name,description:l.description||`${l.quantity}${l.unit} - ${l.calories}kcal`,calories:l.calories,protein:l.protein,carbs:l.carbs,fat:l.fat}))}else N=Pe(i);x(o=>[...o,{role:"ai",content:N}]),j&&j.length>0&&setTimeout(()=>{Ve(j,N)},500)}catch(n){console.error("Erro ao obter resposta da IA:",n);const k=Pe(i);x(d=>[...d,{role:"ai",content:k}])}},Pe=t=>{const i=t.toLowerCase();return i.includes("refeição")||i.includes("comida")||i.includes("alimentação")?"Aqui estão algumas sugestões de refeições balanceadas para você baseadas nos seus macros restantes.":i.includes("treino")||i.includes("exercício")||i.includes("atividade física")?"Aqui estão algumas opções de atividades físicas que você pode fazer hoje para atingir suas metas.":i.includes("proteína")||i.includes("proteico")?`Você ainda precisa de ${T}g de proteína hoje. Aqui estão algumas opções ricas em proteínas.`:i.includes("caloria")?w>0?`Você ainda tem ${w} calorias disponíveis para hoje. Tente distribuí-las entre proteínas e carboidratos complexos.`:`Você excedeu sua meta calórica em ${Math.abs(w)} calorias. Considere atividades físicas para compensar.`:"Baseado no seu consumo atual, recomendo focar em alimentos ricos em proteínas e com baixo teor de gordura para o restante do dia."};return v.useEffect(()=>{Me();const t=()=>{console.log("Evento showSubstitutions recebido"),$("Sugestões de substituições de alimentos"),setTimeout(()=>{_(new Event("submit"))},100)};return document.addEventListener("showSubstitutions",t),()=>{document.removeEventListener("showSubstitutions",t)}},[]),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl border border-snapfit-green/20 shadow-lg overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-snapfit-green/20 flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Xe,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h3",{className:"text-base font-bold text-white",children:"Sugestões de IA"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>ne(!re),className:`p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors ${re?"text-snapfit-green bg-snapfit-green/10":""}`,title:"Análise do Plano",children:e.jsx(Ye,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>m(!oe),className:`p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors ${oe?"text-snapfit-green bg-snapfit-green/10":""}`,title:"Projeção do Dia",children:e.jsx(Ze,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>je(!he),className:`p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors ${he?"text-snapfit-green bg-snapfit-green/10":""}`,title:"Opções Rápidas",children:e.jsx(Ge,{className:`w-4 h-4 transform transition-transform ${he?"rotate-90":""}`})}),e.jsx("button",{onClick:Me,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",disabled:R,title:"Atualizar Sugestões",children:e.jsx(ye,{className:`w-4 h-4 ${R?"animate-spin":""}`})}),e.jsx("button",{onClick:()=>S(!p),className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",title:p?"Expandir card":"Minimizar card",children:e.jsx(et,{className:`w-4 h-4 transform transition-transform ${p?"rotate-180":""}`})})]})]}),!p&&he&&e.jsxs("div",{className:"p-4 border-b border-snapfit-green/20 bg-snapfit-dark-gray/30",children:[e.jsx("h4",{className:"text-white font-semibold text-sm mb-3",children:"Opções Rápidas"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsx("button",{onClick:()=>{de("meal-options"),g(!0),setTimeout(()=>{let t;s.caloriesAdjustmentNeeded>0?t={type:"meal",title:"Ajuste calórico necessário",description:`Você está ${s.caloriesAdjustmentNeeded} calorias acima da meta. Considere reduzir as porções da próxima refeição ou substituir por opções menos calóricas.`,calories:-s.caloriesAdjustmentNeeded}:s.caloriesAdjustmentNeeded<0?t={type:"meal",title:"Aumente sua ingestão calórica",description:`Você está ${Math.abs(s.caloriesAdjustmentNeeded)} calorias abaixo da meta. Considere adicionar um lanche nutritivo.`,calories:Math.abs(s.caloriesAdjustmentNeeded)}:s.proteinAdjustmentNeeded>0?t={type:"meal",title:"Aumente sua proteína",description:`Você precisa de mais ${s.proteinAdjustmentNeeded}g de proteína. Considere adicionar fontes proteicas às suas refeições.`,protein:s.proteinAdjustmentNeeded,calories:s.proteinAdjustmentNeeded*4}:t={type:"meal",title:"Plano equilibrado",description:"Seu plano está bem balanceado! Continue mantendo esse equilíbrio nutricional.",calories:0},u(t),g(!1),je(!1)},1e3)},className:"p-3 bg-snapfit-dark-gray hover:bg-snapfit-green/10 text-white rounded-lg text-left transition-colors border border-snapfit-green/10 hover:border-snapfit-green/30",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"p-1.5 bg-snapfit-green/10 rounded-full",children:e.jsx(pe,{className:"w-3 h-3 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs font-medium",children:"Ajustar Refeições"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Substituir ou reduzir"})]})]})}),e.jsx("button",{onClick:()=>{de("exercise-options"),g(!0),setTimeout(()=>{const t=Math.abs(s.caloriesAdjustmentNeeded)||200,i={type:"workout",title:"Exercício recomendado",description:`Para queimar aproximadamente ${t} calorias, recomendo uma caminhada de ${Math.round(t/5)} minutos ou exercícios funcionais por ${Math.round(t/8)} minutos.`,calories:-t,timeEstimate:`${Math.round(t/5)} min`};u(i),g(!1),je(!1)},1e3)},className:"p-3 bg-snapfit-dark-gray hover:bg-snapfit-green/10 text-white rounded-lg text-left transition-colors border border-snapfit-green/10 hover:border-snapfit-green/30",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"p-1.5 bg-snapfit-green/10 rounded-full",children:e.jsx(ge,{className:"w-3 h-3 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs font-medium",children:"Fazer Exercícios"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Queimar calorias"})]})]})})]})]}),!p&&re&&e.jsxs("div",{className:"p-4 border-b border-snapfit-green/20 bg-snapfit-dark-gray/50",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${s.status==="on-track"?"bg-green-500":s.status==="warning"?"bg-yellow-500":"bg-red-500"}`}),e.jsx("h4",{className:"text-sm font-medium text-white",children:"Análise do Plano"})]}),e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:`text-sm ${s.status==="on-track"?"text-green-400":s.status==="warning"?"text-yellow-400":"text-red-400"}`,children:s.alertMessage})}),e.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-4",children:[e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Calorias"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isCaloriesOverBudget?"bg-red-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.caloriesPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.caloriesPercentage,"%"]}),e.jsxs("span",{className:s.isCaloriesOverBudget?"text-red-400":"text-gray-400",children:[w<0?`+${Math.abs(w)}`:w," kcal"]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Proteínas"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isProteinDeficient?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.proteinPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.proteinPercentage,"%"]}),e.jsxs("span",{className:s.isProteinDeficient?"text-yellow-400":"text-gray-400",children:[T,"g"]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Carbos"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isCarbsExcessive?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.carbsPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.carbsPercentage,"%"]}),e.jsxs("span",{className:s.isCarbsExcessive?"text-yellow-400":"text-gray-400",children:[I,"g"]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Gorduras"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isFatExcessive?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.fatPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.fatPercentage,"%"]}),e.jsxs("span",{className:s.isFatExcessive?"text-yellow-400":"text-gray-400",children:[se,"g"]})]})]})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[e.jsx("p",{children:"Projeção para o restante do dia:"}),e.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[s.isCaloriesOverBudget&&e.jsxs("li",{className:"text-red-400",children:["Você já excedeu sua meta calórica diária em ",Math.abs(w)," calorias."]}),!s.isCaloriesOverBudget&&w<500&&e.jsxs("li",{className:"text-yellow-400",children:["Você tem apenas ",w," calorias restantes para o dia."]}),s.isProteinDeficient&&e.jsxs("li",{className:"text-yellow-400",children:["Você precisa consumir mais ",T,"g de proteína para atingir sua meta."]}),s.status==="on-track"&&!s.isProteinDeficient&&!s.isCaloriesOverBudget&&e.jsx("li",{className:"text-green-400",children:"Você está no caminho certo para atingir todas as suas metas nutricionais hoje!"})]})]})]}),!p&&oe&&e.jsxs("div",{className:"p-4 border-b border-snapfit-green/20 bg-snapfit-dark-gray/50",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${!s.isProjectedCaloriesOverBudget&&!s.isProjectedCaloriesUnderBudget&&!s.isProjectedProteinDeficient?"bg-green-500":s.isProjectedCaloriesOverBudget?"bg-red-500":"bg-yellow-500"}`}),e.jsx("h4",{className:"text-sm font-medium text-white",children:"Projeção para o Fim do Dia"})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("p",{className:`text-sm ${!s.isProjectedCaloriesOverBudget&&!s.isProjectedCaloriesUnderBudget&&!s.isProjectedProteinDeficient?"text-green-400":s.isProjectedCaloriesOverBudget?"text-red-400":"text-yellow-400"}`,children:[s.isProjectedCaloriesOverBudget?`Você está projetado para terminar o dia com ${Math.round(s.projectedCalories-2e3)} calorias acima da meta.`:s.isProjectedCaloriesUnderBudget?`Você está projetado para terminar o dia com ${Math.round(2e3-s.projectedCalories)} calorias abaixo da meta.`:"Você está projetado para terminar o dia dentro da sua meta calórica.",s.isProjectedProteinDeficient?` Sua ingestão projetada de proteínas está ${Math.round(s.projectedProteinPercentage)}% da meta.`:"",s.sleepQualityIssue?" Considerando sua qualidade de sono ruim, recomendamos ajustes na alimentação e atividade física.":""]})}),e.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-4",children:[e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Calorias"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isProjectedCaloriesOverBudget?"bg-red-500":s.isProjectedCaloriesUnderBudget?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.projectedCaloriesPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.projectedCaloriesPercentage,"%"]}),e.jsx("span",{className:`text-xs ${s.isProjectedCaloriesOverBudget?"text-red-400":s.isProjectedCaloriesUnderBudget?"text-yellow-400":"text-green-400"}`,children:s.isProjectedCaloriesOverBudget?"Excesso":s.isProjectedCaloriesUnderBudget?"Déficit":"Ideal"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Proteínas"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isProjectedProteinDeficient?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.projectedProteinPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.projectedProteinPercentage,"%"]}),e.jsx("span",{className:s.isProjectedProteinDeficient?"text-yellow-400":"text-gray-400",children:s.isProjectedProteinDeficient?"Baixo":"OK"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Carboidratos"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isProjectedCarbsExcessive?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.projectedCarbsPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.projectedCarbsPercentage,"%"]}),e.jsx("span",{className:s.isProjectedCarbsExcessive?"text-yellow-400":"text-gray-400",children:s.isProjectedCarbsExcessive?"Alto":"OK"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Gorduras"}),e.jsx("div",{className:"relative h-1.5 bg-snapfit-dark-gray rounded-full overflow-hidden",children:e.jsx("div",{className:`absolute top-0 left-0 h-full rounded-full ${s.isProjectedFatExcessive?"bg-yellow-500":"bg-snapfit-green"}`,style:{width:`${Math.min(s.projectedFatPercentage,100)}%`}})}),e.jsxs("div",{className:"text-xs font-medium mt-1 flex justify-between",children:[e.jsxs("span",{className:"text-white",children:[s.projectedFatPercentage,"%"]}),e.jsx("span",{className:s.isProjectedFatExcessive?"text-yellow-400":"text-gray-400",children:s.isProjectedFatExcessive?"Alto":"OK"})]})]})]}),X==="diagnosis"&&(s.isProjectedCaloriesOverBudget||s.isProjectedCaloriesUnderBudget||s.isProjectedProteinDeficient)&&e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:()=>{de("meal-options"),g(!0),setTimeout(()=>{let t;s.caloriesAdjustmentNeeded>0?t={type:"meal",title:"Ajuste calórico necessário",description:`Você está ${s.caloriesAdjustmentNeeded} calorias acima da meta. Considere reduzir as porções da próxima refeição ou substituir por opções menos calóricas como salada com proteína magra.`,calories:-s.caloriesAdjustmentNeeded}:s.caloriesAdjustmentNeeded<0?t={type:"meal",title:"Aumente sua ingestão calórica",description:`Você está ${Math.abs(s.caloriesAdjustmentNeeded)} calorias abaixo da meta. Considere adicionar um lanche nutritivo como mix de castanhas, frutas ou iogurte grego.`,calories:Math.abs(s.caloriesAdjustmentNeeded)}:s.proteinAdjustmentNeeded>0?t={type:"meal",title:"Aumente sua proteína",description:`Você precisa de mais ${s.proteinAdjustmentNeeded}g de proteína. Considere adicionar peito de frango grelhado, ovos, queijo cottage ou um shake de proteína.`,protein:s.proteinAdjustmentNeeded,calories:s.proteinAdjustmentNeeded*4}:t={type:"meal",title:"Ajuste no plano alimentar",description:"Com base na sua alimentação atual, considere fazer pequenos ajustes nas próximas refeições para otimizar seus resultados.",calories:0},u(t),g(!1)},1e3)},className:"flex-1 py-3 px-4 bg-snapfit-dark-gray text-white rounded-lg text-sm font-medium hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors border border-snapfit-green/20 flex items-center justify-center gap-2",children:[e.jsx(pe,{className:"w-4 h-4"}),"Ajustar Refeições"]}),e.jsxs("button",{onClick:()=>{de("exercise-options"),g(!0),setTimeout(()=>{const t=Math.abs(s.caloriesAdjustmentNeeded)||200,i={type:"workout",title:"Exercício recomendado",description:`Para queimar aproximadamente ${t} calorias, recomendo uma caminhada de ${Math.round(t/5)} minutos ou uma corrida leve de ${Math.round(t/10)} minutos. Escolha a atividade que mais se adequa ao seu condicionamento atual.`,calories:-t,timeEstimate:`${Math.round(t/5)} min`};u(i),g(!1)},1e3)},className:"flex-1 py-3 px-4 bg-snapfit-dark-gray text-white rounded-lg text-sm font-medium hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors border border-snapfit-green/20 flex items-center justify-center gap-2",children:[e.jsx(ge,{className:"w-4 h-4"}),"Fazer Exercícios"]})]})]}),!p&&X==="meal-options"&&e.jsxs("div",{className:"p-4 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:"Opções de Ajuste Alimentar"}),e.jsx("button",{onClick:()=>de("diagnosis"),className:"text-gray-400 hover:text-snapfit-green text-sm transition-colors",children:"← Voltar"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{x([...J,{role:"user",content:"Quero substituir alimentos por opções mais saudáveis"}]),setTimeout(()=>{Be()},500)},className:"w-full p-4 bg-snapfit-dark-gray hover:bg-snapfit-green/10 text-white rounded-lg text-left transition-colors border border-snapfit-green/10 hover:border-snapfit-green/30",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full",children:e.jsx(pe,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Substituir Alimentos"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Trocar por opções mais saudáveis e menos calóricas"})]})]})}),e.jsx("button",{onClick:()=>{x([...J,{role:"user",content:"Quero ajustar as porções dos meus alimentos"}]),setTimeout(()=>{Ie()},500)},className:"w-full p-4 bg-snapfit-dark-gray hover:bg-snapfit-green/10 text-white rounded-lg text-left transition-colors border border-snapfit-green/10 hover:border-snapfit-green/30",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full",children:e.jsx(ce,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Ajustar Porções"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Reduzir quantidades mantendo os mesmos alimentos"})]})]})})]})]}),!p&&X==="exercise-options"&&e.jsxs("div",{className:"p-4 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:"Opções de Exercícios"}),e.jsx("button",{onClick:()=>de("diagnosis"),className:"text-gray-400 hover:text-snapfit-green text-sm transition-colors",children:"← Voltar"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{x([...J,{role:"user",content:"Quero fazer exercícios cardiovasculares"}]),setTimeout(()=>{Fe()},500)},className:"w-full p-4 bg-snapfit-dark-gray hover:bg-snapfit-green/10 text-white rounded-lg text-left transition-colors border border-snapfit-green/10 hover:border-snapfit-green/30",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full",children:e.jsx(ge,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Exercícios Cardiovasculares"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Caminhada, corrida, ciclismo para queimar calorias"})]})]})}),e.jsx("button",{onClick:()=>{x([...J,{role:"user",content:"Quero fazer exercícios de força"}]),setTimeout(()=>{De()},500)},className:"w-full p-4 bg-snapfit-dark-gray hover:bg-snapfit-green/10 text-white rounded-lg text-left transition-colors border border-snapfit-green/10 hover:border-snapfit-green/30",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full",children:e.jsx(tt,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Exercícios de Força"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Musculação e exercícios funcionais"})]})]})})]})]}),!p&&(X==="meal-options"||X==="exercise-options")&&me&&e.jsx("div",{className:"p-4",children:R?e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx("div",{className:"w-12 h-12 border-2 border-snapfit-green/30 border-t-snapfit-green rounded-full animate-spin mb-4"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Gerando sugestões personalizadas..."})]}):e.jsxs("div",{className:"space-y-4",children:[!1,a&&!P&&e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full border border-snapfit-green/30 flex-shrink-0 mt-1",children:a.type==="meal"?e.jsx(pe,{className:"w-5 h-5 text-snapfit-green"}):a.type==="workout"?e.jsx(ge,{className:"w-5 h-5 text-snapfit-green"}):e.jsx(ce,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:a.title}),e.jsx("p",{className:"text-gray-400 text-sm mt-1",children:a.description}),(a.calories||a.protein||a.carbs||a.fat)&&e.jsxs("div",{className:"grid grid-cols-4 gap-2 mt-3",children:[a.calories!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:a.calories})]}),a.protein!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[a.protein,"g"]})]}),a.carbs!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carbos"}),e.jsxs("div",{className:"text-sm font-medium text-green-500",children:[a.carbs,"g"]})]}),a.fat!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-sm font-medium text-yellow-500",children:[a.fat,"g"]})]})]}),a.timeEstimate&&e.jsxs("div",{className:"flex items-center gap-1 mt-2 text-gray-400 text-xs",children:[e.jsx(ce,{className:"w-3 h-3"}),e.jsx("span",{children:a.timeEstimate})]}),e.jsxs("div",{className:"mt-3 pt-3 border-t border-snapfit-green/10",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:()=>a&&ke(a),disabled:ue,className:"flex-1 py-2 bg-snapfit-green text-black rounded-lg text-sm font-medium hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:[ue?e.jsx(ye,{className:"w-4 h-4 inline-block mr-1 animate-spin"}):e.jsx(Ce,{className:"w-4 h-4 inline-block mr-1"}),ue?"Adicionando...":"Adicionar ao Plano"]})}),e.jsx("button",{onClick:()=>{A(null),c(!0)},className:"w-full mt-2 py-1.5 text-gray-400 hover:text-snapfit-green text-xs transition-colors",children:"Voltar às opções"})]})]})]})})]})}),!p&&X==="diagnosis"&&e.jsx("div",{className:"p-4",children:R?e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsx("div",{className:"w-12 h-12 border-2 border-snapfit-green/30 border-t-snapfit-green rounded-full animate-spin mb-4"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Gerando sugestões personalizadas... ⚡"})]}):e.jsxs("div",{className:"space-y-4",children:[!1,a&&!P&&e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full border border-snapfit-green/30 flex-shrink-0 mt-1",children:a.type==="meal"?e.jsx(pe,{className:"w-5 h-5 text-snapfit-green"}):a.type==="workout"?e.jsx(ge,{className:"w-5 h-5 text-snapfit-green"}):e.jsx(ce,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:a.title}),e.jsx("div",{className:"flex gap-2",children:e.jsx("button",{onClick:ve,className:"p-1 text-gray-400 hover:text-snapfit-green rounded-lg transition-colors",title:"Personalizar",children:e.jsx(Ne,{className:"w-4 h-4"})})})]}),e.jsx("p",{className:"text-gray-400 text-sm mt-1",children:a.description}),(a.calories||a.protein||a.carbs||a.fat)&&e.jsxs("div",{className:"grid grid-cols-4 gap-2 mt-3",children:[a.calories!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:a.calories})]}),a.protein!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[a.protein,"g"]})]}),a.carbs!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carbos"}),e.jsxs("div",{className:"text-sm font-medium text-green-500",children:[a.carbs,"g"]})]}),a.fat!==void 0&&e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-sm font-medium text-yellow-500",children:[a.fat,"g"]})]})]}),a.timeEstimate&&e.jsxs("div",{className:"flex items-center gap-1 mt-2 text-gray-400 text-xs",children:[e.jsx(ce,{className:"w-3 h-3"}),e.jsx("span",{children:a.timeEstimate})]}),e.jsxs("div",{className:"mt-3 pt-3 border-t border-snapfit-green/10",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:ve,className:"flex-1 py-2 bg-snapfit-dark-gray text-white rounded-lg text-sm font-medium hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/20",children:[e.jsx(Ne,{className:"w-4 h-4 inline-block mr-1"}),"Personalizar"]}),e.jsxs("button",{onClick:()=>a&&ke(a),disabled:ue,className:"flex-1 py-2 bg-snapfit-green text-black rounded-lg text-sm font-medium hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:[ue?e.jsx(ye,{className:"w-4 h-4 inline-block mr-1 animate-spin"}):e.jsx(Ce,{className:"w-4 h-4 inline-block mr-1"}),ue?"Adicionando...":"Adicionar ao Plano"]})]}),e.jsx("button",{onClick:()=>{A(null),c(!0)},className:"w-full mt-2 py-1.5 text-gray-400 hover:text-snapfit-green text-xs transition-colors",children:"Voltar às opções"})]})]})]})}),P&&r&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:"Personalizar Porções"}),e.jsx("div",{className:"text-xs text-snapfit-green bg-snapfit-green/10 px-2 py-1 rounded-full",children:"Macros calculados automaticamente"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start gap-3 pb-3 border-b border-snapfit-green/10",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-full border border-snapfit-green/30 flex-shrink-0 mt-1",children:r.type==="meal"?e.jsx(pe,{className:"w-4 h-4 text-snapfit-green"}):r.type==="workout"?e.jsx(ge,{className:"w-4 h-4 text-snapfit-green"}):e.jsx(ce,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium",children:r.title}),e.jsx("p",{className:"text-gray-400 text-xs mt-1",children:r.description})]})]}),r.type==="meal"&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white text-sm font-medium mb-2",children:"Ajustar Porções"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between bg-snapfit-gray p-2 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/10 rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-4 h-4 bg-snapfit-green rounded-full"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white text-sm",children:"Proteína principal"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Frango, peixe ou tofu"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>{const t=Math.max(0,(r.protein||0)-10),i=Math.max(0,(r.calories||0)-50);b({...r,protein:t,calories:i})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"-"}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"text-white text-sm w-12 text-center",children:Math.round((r.protein||0)/10)}),e.jsxs("span",{className:"text-gray-400 text-xs",children:[Math.round((r.protein||0)/10)*50,"g"]})]}),e.jsx("button",{onClick:()=>{const t=(r.protein||0)+10,i=(r.calories||0)+50;b({...r,protein:t,calories:i})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"+"})]})]}),e.jsxs("div",{className:"flex items-center justify-between bg-snapfit-gray p-2 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-green-500/10 rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-4 h-4 bg-green-500 rounded-full"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white text-sm",children:"Carboidratos"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Arroz, batata ou quinoa"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>{const t=Math.max(0,(r.carbs||0)-15),i=Math.max(0,(r.calories||0)-60);b({...r,carbs:t,calories:i})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"-"}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"text-white text-sm w-12 text-center",children:Math.round((r.carbs||0)/15)}),e.jsxs("span",{className:"text-gray-400 text-xs",children:[Math.round((r.carbs||0)/15)*40,"g"]})]}),e.jsx("button",{onClick:()=>{const t=(r.carbs||0)+15,i=(r.calories||0)+60;b({...r,carbs:t,calories:i})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"+"})]})]}),e.jsxs("div",{className:"flex items-center justify-between bg-snapfit-gray p-2 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-yellow-500/10 rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-4 h-4 bg-yellow-500 rounded-full"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white text-sm",children:"Gorduras saudáveis"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Azeite, abacate ou oleaginosas"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>{const t=Math.max(0,(r.fat||0)-5),i=Math.max(0,(r.calories||0)-45);b({...r,fat:t,calories:i})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"-"}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"text-white text-sm w-12 text-center",children:Math.round((r.fat||0)/5)}),e.jsxs("span",{className:"text-gray-400 text-xs",children:[Math.round((r.fat||0)/5)*10,"g"]})]}),e.jsx("button",{onClick:()=>{const t=(r.fat||0)+5,i=(r.calories||0)+45;b({...r,fat:t,calories:i})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"+"})]})]})]})]}),r.type==="workout"&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white text-sm font-medium mb-2",children:"Ajustar Duração"}),e.jsxs("div",{className:"flex items-center justify-between bg-snapfit-gray p-3 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"w-5 h-5 text-snapfit-green"}),e.jsx("div",{className:"text-white text-sm",children:"Duração do treino"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>{var d;const t=parseInt(((d=r.timeEstimate)==null?void 0:d.replace(" min",""))||"30"),i=Math.max(5,t-5),n=i/t,k=Math.round((r.calories||0)*n);b({...r,timeEstimate:`${i} min`,calories:k})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"-"}),e.jsx("span",{className:"text-white text-sm w-16 text-center",children:r.timeEstimate}),e.jsx("button",{onClick:()=>{var d;const t=parseInt(((d=r.timeEstimate)==null?void 0:d.replace(" min",""))||"30"),i=t+5,n=i/t,k=Math.round((r.calories||0)*n);b({...r,timeEstimate:`${i} min`,calories:k})},className:"w-8 h-8 bg-snapfit-dark-gray rounded-full flex items-center justify-center text-gray-300 hover:bg-snapfit-green/20 hover:text-snapfit-green",children:"+"})]})]}),e.jsxs("div",{className:"mt-3 text-xs text-gray-400",children:["Calorias estimadas: ",e.jsx("span",{className:"text-snapfit-green",children:Math.abs(r.calories||0)})," kcal"]})]}),r.type==="meal"&&e.jsxs("div",{className:"bg-snapfit-gray p-3 rounded-lg",children:[e.jsx("h5",{className:"text-white text-sm font-medium mb-2",children:"Resumo Nutricional"}),e.jsxs("div",{className:"grid grid-cols-4 gap-2",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:r.calories})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[r.protein,"g"]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carbos"}),e.jsxs("div",{className:"text-sm font-medium text-green-500",children:[r.carbs,"g"]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-sm font-medium text-yellow-500",children:[r.fat,"g"]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-3",children:[e.jsx("button",{onClick:()=>ie(!1),className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancelar"}),e.jsx("button",{onClick:Re,className:"px-4 py-2 bg-snapfit-green text-black rounded-lg text-sm font-medium hover:bg-snapfit-green/90 transition-colors",children:"Salvar Alterações"})]})]})]}),e.jsxs("div",{className:"flex justify-between items-center pt-3 border-t border-snapfit-green/10",children:[e.jsx("div",{className:"flex gap-2",children:!P&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>Te("liked"),className:`p-2 rounded-lg text-sm flex items-center gap-1 transition-colors ${V==="liked"?"bg-snapfit-green/20 text-snapfit-green":"text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10"}`,children:[e.jsx(mt,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Útil"})]}),a&&e.jsxs("button",{onClick:ve,className:"p-2 rounded-lg text-sm flex items-center gap-1 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 transition-colors",children:[e.jsx(Ne,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Personalizar"})]})]})}),e.jsxs("div",{className:"flex gap-2",children:[!P&&e.jsxs("button",{onClick:()=>h(!L),className:`p-2 rounded-lg text-sm flex items-center gap-1 transition-colors ${L?"bg-snapfit-green/20 text-snapfit-green":"text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10"}`,children:[e.jsx(st,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Conversar"})]}),e.jsx("button",{onClick:()=>u(null),className:"p-2 text-gray-400 hover:text-red-400 rounded-lg transition-colors",children:e.jsx(at,{className:"w-4 h-4"})})]})]})]})}),L&&!P&&e.jsxs("div",{className:"border-t border-snapfit-green/20 p-4",children:[e.jsxs("div",{className:"hidden md:flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"max-h-60 overflow-y-auto mb-3 space-y-3 pr-1",style:{scrollBehavior:"smooth"},children:J.length===0?e.jsx("p",{className:"text-center text-gray-400 text-sm py-4",children:"Faça perguntas sobre nutrição, treinos ou ajustes no seu plano."}):J.map((t,i)=>e.jsx("div",{className:`flex ${t.role==="user"?"justify-end":"justify-start"}`,children:e.jsx("div",{className:`max-w-[90%] p-3 rounded-lg ${t.role==="user"?"bg-snapfit-green/20 text-white":"bg-snapfit-dark-gray text-white"}`,children:e.jsx("p",{className:"text-sm",children:t.content})})},i))}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.jsx("button",{onClick:()=>{$("Sugestões de refeições"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Sugestões de refeições"}),e.jsx("button",{onClick:()=>{$("Opções de treino"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Opções de treino"}),e.jsx("button",{onClick:()=>{$("Alimentos ricos em proteína"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Alimentos proteicos"}),e.jsx("button",{onClick:()=>{$("Refeições com baixas calorias"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Baixas calorias"}),e.jsx("button",{onClick:()=>{$("Sugestões de substituições de alimentos"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Substituições"})]}),e.jsxs("form",{onSubmit:_,className:"flex gap-2",children:[e.jsx("input",{type:"text",value:C,onChange:t=>$(t.target.value),placeholder:"Pergunte algo ou sugira modificações...",className:"flex-1 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-snapfit-green/50"}),e.jsx("button",{type:"submit",disabled:!C.trim(),className:"px-4 py-2 bg-snapfit-green text-black rounded-lg text-sm font-medium hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:"Enviar"})]})]}),!1]}),e.jsxs("div",{className:"md:hidden",children:[e.jsxs("div",{className:"flex border-b border-snapfit-green/20 mb-3",children:[e.jsx("button",{onClick:()=>c(!1),className:`flex-1 py-2 text-sm font-medium transition-colors ${Q?"text-gray-400 hover:text-gray-300":"text-snapfit-green border-b-2 border-snapfit-green"}`,children:"Chat"}),!1]}),!Q&&e.jsxs("div",{children:[e.jsx("div",{className:"max-h-60 overflow-y-auto mb-3 space-y-3 pr-1",style:{scrollBehavior:"smooth"},children:J.length===0?e.jsx("p",{className:"text-center text-gray-400 text-sm py-4",children:"Faça perguntas sobre nutrição, treinos ou ajustes no seu plano."}):J.map((t,i)=>e.jsx("div",{className:`flex ${t.role==="user"?"justify-end":"justify-start"}`,children:e.jsx("div",{className:`max-w-[90%] p-3 rounded-lg ${t.role==="user"?"bg-snapfit-green/20 text-white":"bg-snapfit-dark-gray text-white"}`,children:e.jsx("p",{className:"text-sm",children:t.content})})},i))}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.jsx("button",{onClick:()=>{$("Sugestões de refeições"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Sugestões de refeições"}),e.jsx("button",{onClick:()=>{$("Opções de treino"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Opções de treino"}),e.jsx("button",{onClick:()=>{$("Alimentos ricos em proteína"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Alimentos proteicos"}),e.jsx("button",{onClick:()=>{$("Refeições com baixas calorias"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Baixas calorias"}),e.jsx("button",{onClick:()=>{$("Sugestões de substituições de alimentos"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Substituições"}),e.jsx("button",{onClick:()=>{$("Ajuste de porções dos alimentos atuais"),setTimeout(()=>{_(new Event("submit"))},100)},className:"px-3 py-1.5 bg-snapfit-dark-gray text-gray-300 text-xs rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:"Ajuste de porções"})]}),e.jsxs("form",{onSubmit:_,className:"flex gap-2",children:[e.jsx("input",{type:"text",value:C,onChange:t=>$(t.target.value),placeholder:"Pergunte algo ou sugira modificações...",className:"flex-1 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-snapfit-green/50"}),e.jsx("button",{type:"submit",disabled:!C.trim(),className:"px-4 py-2 bg-snapfit-green text-black rounded-lg text-sm font-medium hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:"Enviar"})]})]}),!1]})]})]})}const pt=`
  .rs-picker-popup {
    z-index: 99999 !important;
  }
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
  }
`,ht=({id:w,dateInit:T,timeInit:I,onConfirm:se,onCancel:F})=>{const D=rt(),B=v.useRef(null),[O,Y]=v.useState(!1),[le,U]=v.useState("left"),[f,me]=v.useState(null),[u,R]=v.useState(new Date(`${T} ${I}`)),[g,V]=v.useState(new Date(`${T} ${I}`)),[ae,L]=v.useState(!1),[h,C]=v.useState(!1);v.useEffect(()=>{R(new Date(`${T} ${I}`)),V(new Date(`${T} ${I}`))},[T,I]);const{mutate:$,isLoading:J}=it({mutationFn:async m=>await dt.checkMeal(m),onSuccess:(m,p)=>{const S=p.daily_at.split(" ")[0],a=p.daily_at.split(" ")[1];D.invalidateQueries({queryKey:["meals",S]}),D.invalidateQueries({queryKey:["dailyDataNutritionalSummary",S]}),D.invalidateQueries({queryKey:["dashboard","meals",S]}),se(S,a),Y(!1),xe.success("Refeição marcada como consumida",{position:"bottom-right"})},onError:m=>{console.error("Erro ao marcar refeição como consumida:",m),L(!0)}}),x=async()=>{if(L(!1),!u||!g){L(!0),C(!1);return}C(!0);const m=u.toISOString().split("T")[0],p=g.toISOString().split("T")[1].split(".")[0];console.log("Data selecionada:",m),console.log("Hora selecionada:",p);const S={meal_id:w,meal_name:null,daily_at:`${m} ${p}`,meal_foods:null};$(S),se(m,p),Y(!1)},re=()=>{if(B.current){const m=B.current.getBoundingClientRect();me(m);const p=window.innerWidth,S=320;console.log("Button rect:",{left:m.left,right:m.right,top:m.top,bottom:m.bottom,width:m.width,height:m.height}),console.log("Viewport width:",p);const a=m.left,A=p-m.right;if(console.log("Space calculations:",{spaceOnLeft:a,spaceOnRight:A,modalWidth:S}),p<640)U("right"),console.log("Mobile: using right position");else{const Q=a>=S?"left":"right";U(Q),console.log("Desktop: using",Q,"position")}}Y(!0)},ne=()=>{F(),Y(!1)},oe=()=>{if(!O||!f)return null;const m=320,p=250,S=8,a=16,A=window.innerWidth,Q=window.innerHeight;console.log("=== MODAL POSITIONING DEBUG ==="),console.log("Button rect:",{left:f.left,right:f.right,top:f.top,bottom:f.bottom,width:f.width,height:f.height}),console.log("Viewport:",{width:A,height:Q}),console.log("Modal settings:",{modalWidth:m,modalHeight:p,gap:S,padding:a});let c,P=f.bottom+S,ie=!1;console.log("Initial modalTop (below button):",P),le==="right"?(c=f.right-m,console.log("Right positioning: buttonRect.right =",f.right,"modalWidth =",m,"result =",c)):(c=f.left,console.log("Left positioning: buttonRect.left =",f.left,"result =",c));const r=c;c<a?(c=a,console.log("Adjusted modalLeft from",r,"to",c,"(too far left)")):c+m>A-a&&(c=A-m-a,console.log("Adjusted modalLeft from",r,"to",c,"(too far right)"));const b=P+p>Q-a;if(console.log("Bottom check: modalTop + modalHeight =",P+p,"viewportHeight - padding =",Q-a,"wouldGoOffBottom =",b),b){const X=f.top-p-S;console.log("Positioning above: buttonRect.top =",f.top,"modalHeight =",p,"gap =",S,"result =",X),P=X,ie=!0}P<a&&(console.log("Modal would go above viewport, clamping to padding:",a),P=a,ie=!1);const K=f.left+f.width/2,Z=Math.max(16,Math.min(m-16,K-c));return console.log("Final positioning:",{modalLeft:c,modalTop:P,isAbove:ie,buttonCenterX:K,arrowLeft:Z}),(c<-1e3||c>A+1e3)&&(console.error("WARNING: modalLeft seems unreasonable:",c),c=Math.max(a,Math.min(A-m-a,f.left))),(P<-1e3||P>Q+1e3)&&(console.error("WARNING: modalTop seems unreasonable:",P),P=Math.max(a,f.bottom+S)),console.log("=== END DEBUG ==="),ot.createPortal(e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed inset-0 z-[9998]",onClick:ne,style:{backgroundColor:"rgba(0, 0, 0, 0.5)"}}),e.jsxs("div",{className:"fixed z-[9999]",style:{left:`${c}px`,top:`${P}px`,width:`${m}px`,position:"fixed"},children:[ie?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute -bottom-2 w-0 h-0 border-l-[8px] border-r-[8px] border-t-[8px] border-l-transparent border-r-transparent border-t-snapfit-green/20",style:{left:Z-8}}),e.jsx("div",{className:"absolute -bottom-1 w-0 h-0 border-l-[7px] border-r-[7px] border-t-[7px] border-l-transparent border-r-transparent border-t-snapfit-gray",style:{left:Z-7}})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute -top-2 w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-snapfit-green/20",style:{left:Z-8}}),e.jsx("div",{className:"absolute -top-1 w-0 h-0 border-l-[7px] border-r-[7px] border-b-[7px] border-l-transparent border-r-transparent border-b-snapfit-gray",style:{left:Z-7}})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray border border-snapfit-green/20 rounded-lg shadow-lg",children:[h&&e.jsx("div",{className:"absolute inset-0 bg-snapfit-gray/80 rounded-lg flex items-center justify-center z-10",children:e.jsx("div",{className:"text-white",children:"Confirmando..."})}),e.jsx("h2",{className:"text-lg font-bold mb-2 text-white",children:"Confirmar Refeição"}),e.jsxs("div",{className:"flex flex-col",children:[ae&&!h&&e.jsx("p",{className:"text-red-400 mb-2",children:"Por favor, selecione uma data e hora válidas."}),e.jsxs("div",{onClick:()=>L(!1),children:[w&&e.jsx("div",{className:"text-sm font-medium text-snapfit-green mb-2",children:u==null?void 0:u.toISOString().split("T")[0].split("-").reverse().join("/")}),!w&&e.jsx(nt,{value:u,onChange:X=>R(X||u),className:"mt-2 w-full",placeholder:"Selecione a data",format:"dd/MM/yyyy",onClean:()=>R(null)}),e.jsx(lt,{value:g,onChange:X=>V(X||g),className:"mt-2 w-full",placeholder:"Selecione a hora",format:"HH:mm",onClean:()=>V(null)})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 mt-4",children:[e.jsx("button",{onClick:x,disabled:h,className:"flex-1 bg-snapfit-green text-black px-4 py-2 rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95 disabled:opacity-50",children:h?"Confirmando...":"Confirmar"}),e.jsx("button",{onClick:ne,disabled:h,className:"flex-1 text-gray-400 hover:text-snapfit-green px-4 py-2 transition-colors",children:"Cancelar"})]})]})]})]})]}),document.body)};return e.jsxs(e.Fragment,{children:[e.jsx("style",{children:pt}),e.jsx("button",{ref:B,onClick:re,className:"p-1.5 sm:p-2 rounded-full transition-colors bg-snapfit-dark-gray text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 border border-snapfit-green/10",children:e.jsx(Ce,{className:"w-4 h-4 sm:w-5 sm:h-5"})}),e.jsx(oe,{})]})};export{ft as A,xt as M,ht as a};
