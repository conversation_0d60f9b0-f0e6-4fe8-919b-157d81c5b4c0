var S=Object.defineProperty;var M=(g,s,e)=>s in g?S(g,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):g[s]=e;var P=(g,s,e)=>M(g,typeof s!="symbol"?s+"":s,e);import{bl as u,b as d}from"./index-BwF2e626.js";const E=[{id:"1",name:"T<PERSON><PERSON> A - Peito e Tríceps",type:"hypertrophy",date:"2024-03-14",completed:!1,duration:60,exercises:[{id:"1",name:"<PERSON><PERSON><PERSON>",sets:4,reps:12,weight:60,restTime:90,completed:!1,notes:"Foco na contração e tempo sob tensão",gifUrl:"https://images.unsplash.com/photo-1571019614242-c5c5dee9f50b?w=400&h=300&fit=crop"},{id:"2",name:"<PERSON><PERSON><PERSON>",sets:3,reps:12,weight:50,restTime:90,completed:!1,gifUrl:"https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=400&h=300&fit=crop"},{id:"3",name:"Extensão de Tríceps na Polia",sets:4,reps:15,weight:25,restTime:60,completed:!1,notes:"Manter cotovelos fixos",gifUrl:"https://images.unsplash.com/photo-1598575285675-d6d4d761a1f3?w=400&h=300&fit=crop"},{id:"4",name:"Crucifixo",sets:3,reps:12,weight:14,restTime:60,completed:!1,gifUrl:"https://images.unsplash.com/photo-1605296867424-35fc25c9212a?w=400&h=300&fit=crop"}]},{id:"2",name:"Treino B - Costas e Bíceps",type:"hypertrophy",date:"2024-03-14",completed:!1,duration:55,exercises:[{id:"5",name:"Puxada na Frente",sets:4,reps:12,weight:65,restTime:90,completed:!1,notes:"Puxar com as escápulas",gifUrl:"https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=400&h=300&fit=crop"},{id:"6",name:"Remada Curvada",sets:4,reps:12,weight:40,restTime:90,completed:!1,gifUrl:"https://images.unsplash.com/photo-1603287681836-b174ce5074c2?w=400&h=300&fit=crop"},{id:"7",name:"Rosca Direta",sets:3,reps:12,weight:20,restTime:60,completed:!1,notes:"Manter cotovelos junto ao corpo",gifUrl:"https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop"}]},{id:"3",name:"Treino C - Pernas",type:"hypertrophy",date:"2024-03-14",completed:!1,duration:65,exercises:[{id:"8",name:"Agachamento",sets:4,reps:12,weight:80,restTime:120,completed:!1,notes:"Profundidade paralela",gifUrl:"https://images.unsplash.com/photo-1566241142559-40e1dab266c6?w=400&h=300&fit=crop"},{id:"9",name:"Leg Press",sets:4,reps:15,weight:160,restTime:90,completed:!1,gifUrl:"https://images.unsplash.com/photo-1597452485669-2c7bb5fef90d?w=400&h=300&fit=crop"},{id:"10",name:"Extensão de Pernas",sets:3,reps:15,weight:45,restTime:60,completed:!1,gifUrl:"https://images.unsplash.com/photo-1594381898411-846e7d193883?w=400&h=300&fit=crop"}]}],m=class m{static getInstance(){return m.instance||(m.instance=new m),m.instance}async getActiveProtocol(){var s,e,r,l;if(u.isMockModeEnabled())return console.log("🔧 WorkoutService: Modo mock habilitado"),this.getMockProtocol();try{console.log("🔄 WorkoutService: Buscando protocolo ativo da API...");const t=await d.get("users/protocols/workouts/active");let c=null;return(t==null?void 0:t.status)==="success"&&(t!=null&&t.data)?c=t.data:((s=t==null?void 0:t.data)==null?void 0:s.status)==="success"&&((e=t==null?void 0:t.data)!=null&&e.data)?c=t.data.data:(t==null?void 0:t.has_protocol)!==void 0?c=t:c=((r=t==null?void 0:t.data)==null?void 0:r.data)||(t==null?void 0:t.data)||t,c&&c.has_protocol===!0?(console.log("✅ WorkoutService: Protocolo encontrado:",c.name),{has_protocol:!0,...c,notes:c.general_notes||c.notes,workouts:c.workouts||[]}):(console.log("❌ WorkoutService: Nenhum protocolo encontrado na resposta"),{has_protocol:!1,workouts:[]})}catch(t){if(console.error("❌ WorkoutService: Erro na API:",t),(t==null?void 0:t.status)===401||(l=t==null?void 0:t.message)!=null&&l.includes("401"))throw t;return console.log("🔄 WorkoutService: Usando dados mockados devido ao erro"),this.getMockProtocol()}}async getWorkoutHistory(s=10){var e;if(u.isMockModeEnabled())return this.getMockHistory();try{const r=await d.get("users/workouts/history",{searchParams:{limit:s.toString(),period:"month"}});return((e=r==null?void 0:r.data)==null?void 0:e.workouts)||[]}catch(r){return console.warn("Failed to load workout history from API, falling back to mock data:",r),this.getMockHistory()}}async getWorkoutStats(){if(u.isMockModeEnabled())return this.getMockStats();try{const s=await d.get("users/workouts/stats");return(s==null?void 0:s.data)||this.getMockStats()}catch(s){return console.warn("Failed to load workout stats from API, falling back to mock data:",s),this.getMockStats()}}async startWorkout(s){if(u.isMockModeEnabled())return console.log("Mock mode: Starting workout",s),{success:!0,session_id:"mock-session-"+Date.now()};try{const e=await d.post("users/workouts/start",{workout_id:s,started_at:new Date().toISOString()});return(e==null?void 0:e.data)||{success:!0}}catch(e){throw console.error("Failed to start workout:",e),e}}async completeWorkout(s,e){if(u.isMockModeEnabled())return console.log("Mock mode: Completing workout",s,e),{success:!0};try{const r=await d.post("users/workouts/complete",{session_id:s,completed_at:new Date().toISOString(),...e});return(r==null?void 0:r.data)||{success:!0}}catch(r){throw console.error("Failed to complete workout:",r),r}}async createProtocol(s){if(u.isMockModeEnabled())return console.log("Mock mode: Creating protocol",s),{success:!0,protocol_id:"mock-protocol-"+Date.now()};try{const e=await d.post("users/protocols/workout",s);return(e==null?void 0:e.data)||{success:!0}}catch(e){throw console.error("Failed to create workout protocol:",e),e}}async getProtocolById(s){var e,r,l,t,c,p;if(u.isMockModeEnabled())return console.log("Mock mode: Getting protocol by ID",s),this.getMockProtocol();try{console.log(`🔄 WorkoutService: Buscando protocolo ID ${s}...`);const o=await d.get(`users/protocols/workout/${s}`);let a=null;if((o==null?void 0:o.status)==="success"&&o.data?(a=o.data,console.log("📋 Estrutura 1: Resposta direta da API")):((e=o==null?void 0:o.data)==null?void 0:e.status)==="success"&&o.data.data?(a=o.data.data,console.log("📋 Estrutura 2: Resposta encapsulada")):o!=null&&o.data&&(a=o.data,console.log("📋 Estrutura 3: Resposta simples")),a&&a.id){console.log("✅ WorkoutService: Protocolo encontrado:",a.name),console.log("🔍 WorkoutService: Dados brutos dos workouts:",a.workouts);const v=(a.workouts||[]).map((i,f)=>{var y;console.log(`🏋️ WorkoutService: Processando workout ${f+1}:`,{name:i.name,id:i.id,exercisesCount:((y=i.exercises)==null?void 0:y.length)||0}),console.log(`📋 WorkoutService: Exercícios brutos do workout ${f+1}:`,i.exercises);const b=i.name||`Treino ${String.fromCharCode(65+f)}`;i.name||console.warn(`⚠️ WorkoutService: Workout sem nome encontrado no índice ${f}, usando fallback:`,b);const _=(i.exercises||[]).map((n,h)=>{console.log(`🎯 WorkoutService: Processando exercício ${h+1}:`,{exercise_id:n.exercise_id,name:n.name,exercise_name:n.exercise_name,muscle_group:n.muscle_group});const k=n.name||n.exercise_name||`Exercício ${h+1}`;return!n.name&&!n.exercise_name&&console.warn(`⚠️ WorkoutService: Exercício sem nome encontrado no índice ${h}, usando fallback:`,k),{...n,name:k,exercise_name:k}});return console.log(`✅ WorkoutService: Exercícios processados do workout ${f+1}:`,_.map(n=>({name:n.name,exercise_name:n.exercise_name}))),{...i,name:b,exercises:_}});return console.log("✅ WorkoutService: Workouts processados:",v.map(i=>({name:i.name,exercisesCount:i.exercises.length}))),{has_protocol:!0,id:(r=a.id)==null?void 0:r.toString(),name:a.name,type:(l=a.type_id)==null?void 0:l.toString(),objective:a.objective,started_at:a.started_at,frequency:a.frequency,split:a.split,workouts_completed:a.workouts_completed||0,notes:a.general_notes,workouts:v}}return console.log("❌ WorkoutService: Resposta inválida ou protocolo não encontrado"),console.log("❌ WorkoutService: protocolData:",a),null}catch(o){throw console.error("Failed to get workout protocol by ID:",o),((t=o==null?void 0:o.response)==null?void 0:t.status)===404?new Error("Protocolo não encontrado"):((c=o==null?void 0:o.response)==null?void 0:c.status)===401?new Error("Sessão expirada. Faça login novamente"):((p=o==null?void 0:o.response)==null?void 0:p.status)===403?new Error("Você não tem permissão para acessar este protocolo"):new Error("Erro ao carregar protocolo. Tente novamente")}}async updateProtocol(s,e){var r,l,t,c,p;if(console.log("🔄 WorkoutService.updateProtocol: Iniciando atualização do protocolo",s),u.isMockModeEnabled())return console.log("🔧 Mock mode: Updating protocol",s,e),{success:!0};try{if(console.log("🔍 Validando dados do protocolo antes de enviar"),!e.name||e.name.trim().length===0)return console.log("❌ Validação falhou: Nome do protocolo é obrigatório"),{success:!1,message:"Nome do protocolo é obrigatório"};if(!e.objective||e.objective.trim().length===0)return console.log("❌ Validação falhou: Objetivo do protocolo é obrigatório"),{success:!1,message:"Objetivo do protocolo é obrigatório"};if(!e.frequency||e.frequency<1||e.frequency>7)return console.log("❌ Validação falhou: Frequência deve ser entre 1 e 7 dias por semana"),{success:!1,message:"Frequência deve ser entre 1 e 7 dias por semana"};console.log(`🔄 Enviando requisição PUT para users/protocols/workout/${s}`);const o=await d.put(`users/protocols/workout/${s}`,e);return console.log("✅ Resposta recebida:",o),((r=o==null?void 0:o.data)==null?void 0:r.status)==="success"?(console.log("✅ Protocolo atualizado com sucesso:",o.data.message),{success:!0,message:o.data.message}):(console.log("✅ Protocolo atualizado com sucesso (formato alternativo)"),{success:!0,message:"Protocolo de treino atualizado com sucesso!"})}catch(o){if(console.error("❌ Error updating workout protocol:",o),o.response){const a=o.response.status;return console.log(`❌ Erro na resposta da API: Status ${a}`,o.response.data),a===404?(console.log("❌ Protocolo não encontrado (404)"),{success:!1,message:"Protocolo não encontrado"}):a===400?(console.log("❌ Dados inválidos (400):",(l=o.response.data)==null?void 0:l.message),{success:!1,message:((t=o.response.data)==null?void 0:t.message)||"Dados inválidos"}):a===401?(console.log("❌ Sessão expirada (401), redirecionando para login"),localStorage.removeItem("accessToken"),window.location.href="/login",{success:!1,message:"Sessão expirada"}):(console.log("❌ Erro genérico da API:",(c=o.response.data)==null?void 0:c.message),{success:!1,message:((p=o.response.data)==null?void 0:p.message)||"Erro ao atualizar protocolo"})}return console.log("❌ Erro não relacionado à API:",o.message),{success:!1,message:o.message||"Erro ao atualizar protocolo"}}}getMockProtocol(){return{has_protocol:!0,id:"mock-123",name:"Protocolo de Hipertrofia",type:"Hipertrofia",objective:"Ganho de massa muscular",started_at:"2024-01-15",frequency:5,split:"ABCDE",workouts_completed:32,notes:`Foco em tempo sob tensão nos exercícios de peito.
Aumentar carga progressivamente nos exercícios compostos.
Manter strict form em todos os exercícios.`,workouts:E}}getMockHistory(){return[{id:"1",date:"2024-03-14",workout_name:"Treino A - Peito e Tríceps",duration:65,exercises_completed:6,total_exercises:6,calories_burned:420,notes:"Ótimo treino, consegui aumentar a carga no supino"},{id:"2",date:"2024-03-12",workout_name:"Treino B - Costas e Bíceps",duration:70,exercises_completed:5,total_exercises:6,calories_burned:380,notes:"Não consegui completar o último exercício"},{id:"3",date:"2024-03-10",workout_name:"Treino C - Pernas",duration:80,exercises_completed:7,total_exercises:7,calories_burned:520,notes:"Treino intenso, pernas bem trabalhadas"}]}getMockStats(){return{total_workouts:156,this_week:4,this_month:18,average_duration:68,total_volume:12500,favorite_muscle_group:"Peito"}}};P(m,"instance");let w=m;const T=w.getInstance();export{T as w};
