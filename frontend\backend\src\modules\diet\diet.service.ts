import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Protocol } from './entities/protocol.entity';
import { Meal } from './entities/meal.entity';
import { Food } from './entities/food.entity';
import { Supplement } from './entities/supplement.entity';
import { ShoppingList } from './entities/shopping-list.entity';
import { CreateProtocolDto } from './dto/create-protocol.dto';
import { UpdateProtocolDto } from './dto/update-protocol.dto';
import { CreateSupplementDto } from './dto/create-supplement.dto';
import { CreateShoppingListDto } from './dto/create-shopping-list.dto';

@Injectable()
export class DietService {
  constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(Protocol)
    private readonly protocolRepository: Repository<Protocol>,
    @InjectRepository(Meal)
    private readonly mealRepository: Repository<Meal>,
    @InjectRepository(Food)
    private readonly foodRepository: Repository<Food>,
    @InjectRepository(Supplement)
    private readonly supplementRepository: Repository<Supplement>,
    @InjectRepository(ShoppingList)
    private readonly shoppingListRepository: Repository<ShoppingList>
  ) {}

  async getCurrentProtocol(userId: string) {
    const protocol = await this.protocolRepository.findOne({
      where: {
        userId,
        status: 'active',
        type: 'diet'
      },
      order: { startDate: 'DESC' },
      relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
    });

    if (!protocol) {
      throw new NotFoundException('No active diet protocol found');
    }

    return protocol;
  }

  async getProtocol(id: string, userId: string) {
    const protocol = await this.protocolRepository.findOne({
      where: { id, userId },
      relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
    });

    if (!protocol) {
      throw new NotFoundException('Diet protocol not found');
    }

    return protocol;
  }

  async createProtocol(userId: string, createProtocolDto: CreateProtocolDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create protocol
      const { weeklyMeals, supplements, ...protocolData } = createProtocolDto;
      const protocol = this.protocolRepository.create({
        ...protocolData,
        userId,
        type: 'diet',
        dietType: createProtocolDto.dietType
      });
      await queryRunner.manager.save(protocol);

      // Create weekly meals
      for (const [day, meals] of Object.entries(createProtocolDto.weeklyMeals)) {
        for (const meal of meals) {
          const newMeal = this.mealRepository.create({
            ...meal,
            protocolId: protocol.id,
            weekDay: day
          });
          await queryRunner.manager.save(newMeal);

          // Create foods for meal
          for (const food of meal.foods) {
            const newFood = this.foodRepository.create({
              ...food,
              mealId: newMeal.id
            });
            await queryRunner.manager.save(newFood);
          }
        }
      }

      // Create supplements
      for (const supplement of createProtocolDto.supplements) {
        const newSupplement = this.supplementRepository.create({
          ...supplement,
          protocolId: protocol.id
        });
        await queryRunner.manager.save(newSupplement);
      }

      await queryRunner.commitTransaction();
      return protocol;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateProtocol(id: string, userId: string, updateProtocolDto: UpdateProtocolDto) {
    const protocol = await this.protocolRepository.findOne({
      where: { id, userId }
    });

    if (!protocol) {
      throw new NotFoundException('Protocol not found');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update protocol
      Object.assign(protocol, updateProtocolDto);
      await queryRunner.manager.save(protocol);

      // Update weekly meals if provided
      if (updateProtocolDto.weeklyMeals) {
        // Remove existing meals
        await queryRunner.manager.delete(Meal, { protocolId: protocol.id });

        // Create new meals
        for (const [day, meals] of Object.entries(updateProtocolDto.weeklyMeals)) {
          for (const meal of meals) {
            const newMeal = this.mealRepository.create({
              ...meal,
              protocolId: protocol.id,
              weekDay: day
            });
            await queryRunner.manager.save(newMeal);

            // Create foods for meal
            for (const food of meal.foods) {
              const newFood = this.foodRepository.create({
                ...food,
                mealId: newMeal.id
              });
              await queryRunner.manager.save(newFood);
            }
          }
        }
      }

      // Update supplements if provided
      if (updateProtocolDto.supplements) {
        // Remove existing supplements
        await queryRunner.manager.delete(Supplement, { protocolId: protocol.id });

        // Create new supplements
        for (const supplement of updateProtocolDto.supplements) {
          const newSupplement = this.supplementRepository.create({
            ...supplement,
            protocolId: protocol.id
          });
          await queryRunner.manager.save(newSupplement);
        }
      }

      await queryRunner.commitTransaction();
      return protocol;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async addSupplement(protocolId: string, createSupplementDto: CreateSupplementDto) {
    const protocol = await this.protocolRepository.findOne({
      where: { id: protocolId }
    });

    if (!protocol) {
      throw new NotFoundException('Protocol not found');
    }

    const supplement = this.supplementRepository.create({
      ...createSupplementDto,
      protocolId
    });

    return this.supplementRepository.save(supplement);
  }

  async updateSupplement(id: string, updateData: Partial<Supplement>) {
    const supplement = await this.supplementRepository.findOne({
      where: { id }
    });

    if (!supplement) {
      throw new NotFoundException('Supplement not found');
    }

    Object.assign(supplement, updateData);
    return this.supplementRepository.save(supplement);
  }

  async removeSupplement(id: string) {
    const supplement = await this.supplementRepository.findOne({
      where: { id }
    });

    if (!supplement) {
      throw new NotFoundException('Supplement not found');
    }

    await this.supplementRepository.remove(supplement);
  }

  async generateShoppingList(userId: string, createShoppingListDto: CreateShoppingListDto) {
    const protocol = await this.getCurrentProtocol(userId);

    // Get all foods from weekly meals
    const foods = await this.foodRepository
      .createQueryBuilder('food')
      .innerJoin('food.meal', 'meal')
      .where('meal.protocolId = :protocolId', { protocolId: protocol.id })
      .getMany();

    // Calculate quantities for shopping list period
    const multiplier = {
      weekly: 7,
      biweekly: 14,
      monthly: 30
    }[createShoppingListDto.period];

    // Group and sum quantities
    const items = new Map<string, {
      name: string;
      quantity: number;
      unit: string;
    }>();

    foods.forEach(food => {
      const key = `${food.name}-${food.unit}`;
      const existing = items.get(key);

      if (existing) {
        existing.quantity += food.quantity * multiplier;
      } else {
        items.set(key, {
          name: food.name,
          quantity: food.quantity * multiplier,
          unit: food.unit
        });
      }
    });

    // Create shopping list
    const shoppingList = this.shoppingListRepository.create({
      userId,
      protocolId: protocol.id,
      period: createShoppingListDto.period,
      items: Array.from(items.values()),
      generatedAt: new Date()
    });

    return this.shoppingListRepository.save(shoppingList);
  }

  async getShoppingLists(userId: string) {
    return this.shoppingListRepository.find({
      where: { userId },
      order: { generatedAt: 'DESC' }
    });
  }

  async getShoppingList(id: string, userId: string) {
    const list = await this.shoppingListRepository.findOne({
      where: { id, userId }
    });

    if (!list) {
      throw new NotFoundException('Shopping list not found');
    }

    return list;
  }

  async getProtocolsHistory(userId: string, filters: {
    page: number;
    limit: number;
    status?: string;
  }): Promise<{ protocols: Protocol[]; total: number; hasMore: boolean }> {
    const query = this.protocolRepository.createQueryBuilder('protocol')
      .where('protocol.userId = :userId AND protocol.type = :type', {
        userId,
        type: 'diet'
      })
      .orderBy('protocol.createdAt', 'DESC');

    if (filters.status) {
      query.andWhere('protocol.status = :status', { status: filters.status });
    }

    // Pagination
    const offset = (filters.page - 1) * filters.limit;
    query.skip(offset).take(filters.limit + 1); // Take one extra to check if there are more

    const protocols = await query.getMany();
    const hasMore = protocols.length > filters.limit;

    if (hasMore) {
      protocols.pop(); // Remove the extra item
    }

    const total = await query.getCount();

    return {
      protocols,
      total,
      hasMore
    };
  }

  async duplicateProtocol(id: string, userId: string, newName?: string): Promise<Protocol> {
    const originalProtocol = await this.protocolRepository.findOne({
      where: { id, userId },
      relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
    });

    if (!originalProtocol) {
      throw new NotFoundException('Diet protocol not found');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create new protocol
      const newProtocol = this.protocolRepository.create({
        name: newName || `${originalProtocol.name} (Cópia)`,
        type: 'diet',
        objective: originalProtocol.objective,
        startDate: new Date(),
        goals: originalProtocol.goals,
        waterCalculationMethod: originalProtocol.waterCalculationMethod,
        notes: originalProtocol.notes,
        status: 'active',
        userId,
        createdById: originalProtocol.createdById
      });
      await queryRunner.manager.save(newProtocol);

      // Copy weekly meals
      for (const meal of originalProtocol.weeklyMeals) {
        const newMeal = this.mealRepository.create({
          name: meal.name,
          time: meal.time,
          weekDay: meal.weekDay,
          protocolId: newProtocol.id
        });
        await queryRunner.manager.save(newMeal);

        // Copy foods for meal
        for (const food of meal.foods) {
          const newFood = this.foodRepository.create({
            name: food.name,
            quantity: food.quantity,
            unit: food.unit,
            calories: food.calories,
            protein: food.protein,
            carbs: food.carbs,
            fat: food.fat,
            mealId: newMeal.id
          });
          await queryRunner.manager.save(newFood);
        }
      }

      // Copy supplements
      for (const supplement of originalProtocol.supplements) {
        const newSupplement = this.supplementRepository.create({
          name: supplement.name,
          dosage: supplement.dosage,
          timing: supplement.timing,
          notes: supplement.notes,
          protocolId: newProtocol.id
        });
        await queryRunner.manager.save(newSupplement);
      }

      await queryRunner.commitTransaction();

      const result = await this.protocolRepository.findOne({
        where: { id: newProtocol.id },
        relations: ['weeklyMeals', 'weeklyMeals.foods', 'supplements']
      });

      if (!result) {
        throw new Error('Failed to retrieve duplicated protocol');
      }

      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}