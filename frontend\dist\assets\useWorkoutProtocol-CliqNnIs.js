import{h as m,bk as i,u as y,at as d,y as n,b as g}from"./index-Dwgh6cj0.js";import{w as P}from"./workoutService-m-tiMRKS.js";const c={all:["workout","protocol"],protocols:()=>[...c.all,"protocols"],activeProtocol:()=>[...c.protocols(),"active"],protocolHistory:()=>[...c.protocols(),"history"],exercises:()=>[...c.all,"exercises"],activeExercises:()=>[...c.exercises(),"active"],analytics:()=>[...c.all,"analytics"],stats:e=>[...c.analytics(),"stats",e]};function w(){return m({queryKey:i.activeProtocol(),queryFn:async()=>{console.log("🔄 useActiveProtocol: Buscando protocolo ativo...");try{const e=await P.getActiveProtocol();return console.log("📋 useActiveProtocol: Protocolo recebido:",(e==null?void 0:e.name)||"Nenhum protocolo"),e}catch(e){throw console.error("❌ useActiveProtocol: Erro ao buscar protocolo:",e),(e==null?void 0:e.status)===401?(localStorage.removeItem("accessToken"),window.location.href="/login",new Error("Sessão expirada. Redirecionando para login...")):e}},staleTime:1e3*60*3,refetchInterval:1e3*60*10,refetchIntervalInBackground:!0,refetchOnWindowFocus:!0,retry:3})}function v(e="week"){return m({queryKey:c.stats(e),queryFn:async()=>{var o;console.log("🔄 useWorkoutStats: Buscando estatísticas para período:",e);try{const t=await g.get("dashboard/workout");console.log("📊 useWorkoutStats: Dashboard response:",t);const s=await g.get("dashboard/workout-analytics",{searchParams:{period:e}});console.log("📈 useWorkoutStats: Analytics response:",s);const r=(t==null?void 0:t.data)||{},a=(s==null?void 0:s.data)||{},u=((o=a.chart)==null?void 0:o.reduce((k,h)=>k+(h.training_volume||0),0))||0,p=r.protocol_frequency||5,l={weeklyWorkouts:{completed:r.weekly_frequency||0,planned:p},totalTime:r.total_minutes||0,totalCalories:r.total_calories||0,totalVolume:Math.round(u),currentStreak:r.current_streak||0,bestStreak:r.best_streak||0,weeklyProgressPercentage:r.weekly_progress_percentage||0,protocolCompletionPercentage:r.protocol_completion_percentage||0,totalWorkouts:r.total_workouts||0,progressPercentage:r.protocol_completion_percentage||0,chartData:a.chart||[],rawDashboard:r,rawAnalytics:a};return console.log("✅ useWorkoutStats: Dados processados:",l),console.log("📊 useWorkoutStats: Weekly workouts:",l.weeklyWorkouts),console.log("📈 useWorkoutStats: Progress percentages:",{weekly:l.weeklyProgressPercentage,protocol:l.protocolCompletionPercentage}),l}catch(t){return console.warn("⚠️ useWorkoutStats: Erro ao buscar dados da API, usando fallback:",t),{weeklyWorkouts:{completed:0,planned:5},totalTime:0,totalCalories:0,totalVolume:0,currentStreak:0,bestStreak:0,weeklyProgressPercentage:0,protocolCompletionPercentage:0,totalWorkouts:0,chartData:[],rawDashboard:{},rawAnalytics:{},progressPercentage:0}}},staleTime:1e3*60*5,refetchInterval:1e3*60*15,refetchIntervalInBackground:!0,refetchOnWindowFocus:!0,retry:2})}function f(){const e=y();return d({mutationFn:async o=>(console.log("🗑️ useRemoveProtocol: Removendo protocolo...",o),await g.delete(`users/protocols/workout/${o}`)),onSuccess:()=>{e.invalidateQueries({queryKey:i.protocols()}),e.invalidateQueries({queryKey:i.exercises()}),n.success("Protocolo removido com sucesso!",{position:"bottom-right"}),console.log("✅ useRemoveProtocol: Protocolo removido com sucesso")},onError:o=>{n.error("Erro ao remover protocolo",{position:"bottom-right"}),console.error("❌ useRemoveProtocol: Erro ao remover protocolo:",o)}})}function b(){const e=y();return d({mutationFn:async o=>(console.log("➕ useCreateProtocol: Criando protocolo...",o),await P.createProtocol(o)),onSuccess:()=>{e.invalidateQueries({queryKey:i.protocols()}),e.invalidateQueries({queryKey:i.exercises()}),n.success("Protocolo criado com sucesso!",{position:"bottom-right"}),console.log("✅ useCreateProtocol: Protocolo criado com sucesso")},onError:o=>{var s,r;const t=((r=(s=o==null?void 0:o.response)==null?void 0:s.payload)==null?void 0:r.errors)||"Erro ao criar protocolo";Array.isArray(t)?t.forEach(a=>n.error(a,{position:"bottom-right"})):n.error(t,{position:"bottom-right"}),console.error("❌ useCreateProtocol: Erro ao criar protocolo:",o)}})}function S(){const e=y();return d({mutationFn:async o=>await g.post("users/protocols/workout/ai",o,{timeout:6e5}),onSuccess:()=>{e.invalidateQueries({queryKey:i.protocols()}),e.invalidateQueries({queryKey:i.exercises()}),n.success("Protocolo criado com IA com sucesso!",{position:"bottom-right"})},onError:o=>{var s,r,a,u,p;const t=((a=(r=(s=o==null?void 0:o.response)==null?void 0:s.data)==null?void 0:r.errors)==null?void 0:a[0])||((p=(u=o==null?void 0:o.response)==null?void 0:u.data)==null?void 0:p.message)||"Erro ao criar protocolo com IA";Array.isArray(t)?t.forEach(l=>n.error(l,{position:"bottom-right"})):n.error(t,{position:"bottom-right"})}})}function C(e="week"){const o=w(),t=v(e),s=f(),r=b(),a=S();return{protocol:o.data,stats:t.data,isLoadingProtocol:o.isLoading,isLoadingStats:t.isLoading,isLoading:o.isLoading||t.isLoading,protocolError:o.error,statsError:t.error,hasError:!!o.error||!!t.error,removeProtocol:s.mutate,createProtocol:r.mutate,createProtocolAI:a.mutate,isRemovingProtocol:s.isPending,isCreatingProtocol:r.isPending,isCreatingProtocolAI:a.isPending,isMutating:s.isPending||r.isPending||a.isPending,refetchProtocol:o.refetch,refetchStats:t.refetch,refetchAll:()=>{o.refetch(),t.refetch()}}}export{C as u,c as w};
