import { Check } from 'lucide-react';
import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { DatePicker, TimePicker } from 'rsuite';
import 'rsuite/DatePicker/styles/index.css';
import 'rsuite/TimePicker/styles/index.css';
import { apiService } from '../../services/api';
import { foodAIService } from '../../services/foodAIService';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';

const globalStyles = `
  .rs-picker-popup {
    z-index: 99999 !important;
  }
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
  }
`;

interface MealCheckProps {
  id: string | number;
  dateInit: string; // Exemplo: "2025-03-28"
  timeInit: string; // Exemplo: "14:30"
  onConfirm: (date: string, time: string) => void; // Retorna strings
  onCancel: () => void;
}

const MealCheckModal = ({ id, dateInit, timeInit, onConfirm, onCancel }: MealCheckProps) => {
  const queryClient = useQueryClient();
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [open, setOpen] = useState(false);
  const [modalPosition, setModalPosition] = useState<'left' | 'right'>('left');
  const [buttonRect, setButtonRect] = useState<DOMRect | null>(null);
  // Inicializa os estados como objetos Date a partir das strings
  const [date, setDate] = useState<Date|null>(new Date(`${dateInit} ${timeInit}`));
  const [time, setTime] = useState<Date|null>(new Date(`${dateInit} ${timeInit}`));
  const [error, setError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setDate(new Date(`${dateInit} ${timeInit}`));
    setTime(new Date(`${dateInit} ${timeInit}`));
  }, [dateInit, timeInit]);


  // Mutation para marcar refeição como consumida
  const { mutate: checkMeal, isLoading: isLoadingMutation } = useMutation({
    mutationFn: async (mealData: {
      meal_id: string;
      daily_at: string;
      meal_name: null;
      meal_foods: null;
    }) => {
      const response = await foodAIService.checkMeal(mealData);
      return response;
    },
    onSuccess: (data, variables: any) => {
      // 1. Atualiza as queries relacionadas
      const dateString = variables.daily_at.split(' ')[0];
      const timeString = variables.daily_at.split(' ')[1];

      queryClient.invalidateQueries({ queryKey: ['meals', dateString] });
      queryClient.invalidateQueries({ queryKey: ['dailyDataNutritionalSummary', dateString] });
      // Invalidate dashboard meals query
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'meals', dateString] });

      // 2. Executa callback e fecha popover
      // onConfirm(dateString, variables.daily_at.split(' ')[1]);
      onConfirm(dateString, timeString);
      setOpen(false);
      toast.success('Refeição marcada como consumida', {
        position: 'bottom-right',
      });
    },
    onError: (error) => {
      console.error('Erro ao marcar refeição como consumida:', error);
      setError(true);
    }
  });

  const handleConfirm = async () => {
    setError(false);

    if (!date || !time) {
      setError(true);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    const dateString = date.toISOString().split('T')[0]; // yyyy-MM-dd
    const timeString = time.toISOString().split('T')[1].split('.')[0]; // HH:mm:ss

    console.log('Data selecionada:', dateString);
    console.log('Hora selecionada:', timeString);

    const mealData: any = {
      meal_id: id,
      meal_name: null,
      daily_at: `${dateString} ${timeString}`,
      meal_foods: null
    };

    checkMeal(mealData);

    onConfirm(dateString, timeString); // Passa as strings para a função onConfirm
    setOpen(false); // Fecha o popover
  };

  const handleOpen = () => {
    // Calculate optimal position based on button location
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setButtonRect(rect);

      const viewportWidth = window.innerWidth;
      const modalWidth = 320; // Approximate modal width

      // Debug logging
      console.log('Button rect:', {
        left: rect.left,
        right: rect.right,
        top: rect.top,
        bottom: rect.bottom,
        width: rect.width,
        height: rect.height
      });
      console.log('Viewport width:', viewportWidth);

      // Check if there's enough space on the left side for desktop
      const spaceOnLeft = rect.left;
      const spaceOnRight = viewportWidth - rect.right;

      console.log('Space calculations:', { spaceOnLeft, spaceOnRight, modalWidth });

      // On mobile (< 640px), always use right positioning
      // On desktop, use left if there's space, otherwise right
      if (viewportWidth < 640) {
        setModalPosition('right');
        console.log('Mobile: using right position');
      } else {
        const position = spaceOnLeft >= modalWidth ? 'left' : 'right';
        setModalPosition(position);
        console.log('Desktop: using', position, 'position');
      }
    }
    setOpen(true);
  };

  const handleClose = () => {
    onCancel(); // Chama a função onCancel
    setOpen(false);
  };

  // Portal Modal Component
  const PortalModal = () => {
    if (!open || !buttonRect) return null;

    const modalWidth = 320;
    const modalHeight = 250; // More accurate height estimate
    const gap = 8; // Gap between button and modal
    const padding = 16; // Viewport padding

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Enhanced debug logging
    console.log('=== MODAL POSITIONING DEBUG ===');
    console.log('Button rect:', {
      left: buttonRect.left,
      right: buttonRect.right,
      top: buttonRect.top,
      bottom: buttonRect.bottom,
      width: buttonRect.width,
      height: buttonRect.height
    });
    console.log('Viewport:', { width: viewportWidth, height: viewportHeight });
    console.log('Modal settings:', { modalWidth, modalHeight, gap, padding });

    // Calculate initial position (below button)
    let modalLeft: number;
    let modalTop = buttonRect.bottom + gap;
    let isAbove = false;

    console.log('Initial modalTop (below button):', modalTop);

    // Determine horizontal position
    if (modalPosition === 'right') {
      modalLeft = buttonRect.right - modalWidth;
      console.log('Right positioning: buttonRect.right =', buttonRect.right, 'modalWidth =', modalWidth, 'result =', modalLeft);
    } else {
      modalLeft = buttonRect.left;
      console.log('Left positioning: buttonRect.left =', buttonRect.left, 'result =', modalLeft);
    }

    // Horizontal bounds checking
    const originalModalLeft = modalLeft;
    if (modalLeft < padding) {
      modalLeft = padding;
      console.log('Adjusted modalLeft from', originalModalLeft, 'to', modalLeft, '(too far left)');
    } else if (modalLeft + modalWidth > viewportWidth - padding) {
      modalLeft = viewportWidth - modalWidth - padding;
      console.log('Adjusted modalLeft from', originalModalLeft, 'to', modalLeft, '(too far right)');
    }

    // Vertical bounds checking
    const wouldGoOffBottom = modalTop + modalHeight > viewportHeight - padding;
    console.log('Bottom check: modalTop + modalHeight =', modalTop + modalHeight, 'viewportHeight - padding =', viewportHeight - padding, 'wouldGoOffBottom =', wouldGoOffBottom);

    if (wouldGoOffBottom) {
      const newModalTop = buttonRect.top - modalHeight - gap;
      console.log('Positioning above: buttonRect.top =', buttonRect.top, 'modalHeight =', modalHeight, 'gap =', gap, 'result =', newModalTop);
      modalTop = newModalTop;
      isAbove = true;
    }

    // Final bounds check
    if (modalTop < padding) {
      console.log('Modal would go above viewport, clamping to padding:', padding);
      modalTop = padding;
      isAbove = false;
    }

    // Calculate arrow position
    const buttonCenterX = buttonRect.left + (buttonRect.width / 2);
    const arrowLeft = Math.max(16, Math.min(modalWidth - 16, buttonCenterX - modalLeft));

    console.log('Final positioning:', {
      modalLeft,
      modalTop,
      isAbove,
      buttonCenterX,
      arrowLeft
    });

    // Safety checks for unreasonable values
    if (modalLeft < -1000 || modalLeft > viewportWidth + 1000) {
      console.error('WARNING: modalLeft seems unreasonable:', modalLeft);
      modalLeft = Math.max(padding, Math.min(viewportWidth - modalWidth - padding, buttonRect.left));
    }

    if (modalTop < -1000 || modalTop > viewportHeight + 1000) {
      console.error('WARNING: modalTop seems unreasonable:', modalTop);
      modalTop = Math.max(padding, buttonRect.bottom + gap);
    }

    console.log('=== END DEBUG ===');

    return createPortal(
      <>
        {/* Backdrop */}
        <div
          className="fixed inset-0 z-[9998]"
          onClick={handleClose}
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        />

        {/* Modal */}
        <div
          className="fixed z-[9999]"
          style={{
            left: `${modalLeft}px`,
            top: `${modalTop}px`,
            width: `${modalWidth}px`,
            position: 'fixed', // Ensure fixed positioning
          }}
        >
          {/* Arrow pointing to button */}
          {!isAbove ? (
            // Arrow pointing up (modal below button)
            <>
              <div
                className="absolute -top-2 w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-snapfit-green/20"
                style={{ left: arrowLeft - 8 }}
              />
              <div
                className="absolute -top-1 w-0 h-0 border-l-[7px] border-r-[7px] border-b-[7px] border-l-transparent border-r-transparent border-b-snapfit-gray"
                style={{ left: arrowLeft - 7 }}
              />
            </>
          ) : (
            // Arrow pointing down (modal above button)
            <>
              <div
                className="absolute -bottom-2 w-0 h-0 border-l-[8px] border-r-[8px] border-t-[8px] border-l-transparent border-r-transparent border-t-snapfit-green/20"
                style={{ left: arrowLeft - 8 }}
              />
              <div
                className="absolute -bottom-1 w-0 h-0 border-l-[7px] border-r-[7px] border-t-[7px] border-l-transparent border-r-transparent border-t-snapfit-gray"
                style={{ left: arrowLeft - 7 }}
              />
            </>
          )}

          <div className="p-4 bg-snapfit-gray border border-snapfit-green/20 rounded-lg shadow-lg">
            {/* Loading overlay */}
            {isLoading && (
              <div className="absolute inset-0 bg-snapfit-gray/80 rounded-lg flex items-center justify-center z-10">
                <div className="text-white">Confirmando...</div>
              </div>
            )}

            <h2 className="text-lg font-bold mb-2 text-white">Confirmar Refeição</h2>
            <div className="flex flex-col">
              {error && !isLoading && <p className="text-red-400 mb-2">Por favor, selecione uma data e hora válidas.</p>}
              <div onClick={() => setError(false)}>
                {id && (
                  <div className="text-sm font-medium text-snapfit-green mb-2">
                    {date?.toISOString().split('T')[0].split('-').reverse().join('/')}
                  </div>
                )}
                {!id && (
                  <DatePicker
                    value={date}
                    onChange={(value: Date | null) => setDate(value || date)}
                    className="mt-2 w-full"
                    placeholder="Selecione a data"
                    format="dd/MM/yyyy"
                    onClean={() => setDate(null)}
                  />
                )}
                <TimePicker
                  value={time}
                  onChange={(value: Date | null) => setTime(value || time)}
                  className="mt-2 w-full"
                  placeholder="Selecione a hora"
                  format="HH:mm"
                  onClean={() => setTime(null)}
                />
              </div>
              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                <button
                  onClick={handleConfirm}
                  disabled={isLoading}
                  className="flex-1 bg-snapfit-green text-black px-4 py-2 rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95 disabled:opacity-50"
                >
                  {isLoading ? 'Confirmando...' : 'Confirmar'}
                </button>
                <button
                  onClick={handleClose}
                  disabled={isLoading}
                  className="flex-1 text-gray-400 hover:text-snapfit-green px-4 py-2 transition-colors"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        </div>
      </>,
      document.body
    );
  };

  return (
    <>
      <style>{globalStyles}</style>

      {/* Button */}
      <button
        ref={buttonRef}
        onClick={handleOpen}
        className={`p-1.5 sm:p-2 rounded-full transition-colors bg-snapfit-dark-gray text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 border border-snapfit-green/10`}
      >
        <Check className="w-4 h-4 sm:w-5 sm:h-5" />
      </button>

      {/* Portal Modal */}
      <PortalModal />
    </>
  );
};

export default MealCheckModal;