import React from 'react';
import { FileText, Plus, Save, Calendar, Copy, XIcon } from 'lucide-react';
import { FoodSelector } from './FoodSelector';
import { WEEK_DAYS, type WeekDay } from '../types';
import type { Meal, Food } from '../types';
import { MacroCounterUser } from './MacroCounterUser';
import AICalorieCalculator from './AICalorieCalculator';
import WeightEstimationCard from './WeightEstimationCard';
import { apiService } from '../services/api';

interface DietProtocolManualCreatorProps {
  onSave: (protocol: any) => void;
  onCancel: () => void;
  initialData?: any;
}

export function DietProtocolManualCreator({ onSave, onCancel, initialData }: DietProtocolManualCreatorProps) {
  const [name, setName] = React.useState('');
  const [nutriTypes, setNutriTypes] = React.useState<Array<{id: string, value_option: string}>>([]);
  const [type, setType] = React.useState('');
  const [objective, setObjective] = React.useState('');
  const [selectedDay, setSelectedDay] = React.useState<WeekDay>('monday');
  const [goals, setGoals] = React.useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    water: 0,
  });
  const [weeklyMeals, setWeeklyMeals] = React.useState<Record<WeekDay, Meal[]>>({
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  });
  const [showFoodSelector, setShowFoodSelector] = React.useState(false);
  const [editingMealIndex, setEditingMealIndex] = React.useState<number | null>(null);
  const [supplements, setSupplements] = React.useState<any>([]);
  const [disableCopyToAllDays, setDisableCopyToAllDays] = React.useState(false);
  const [showAICalculator, setShowAICalculator] = React.useState(false);

  React.useEffect(() => {
    const fetchNutriTypes = async () => {
      try {
        const response: any = await apiService.get('admin/select_options/nutri_types');
        const data = await response.data;
        setNutriTypes(data);
      } catch (error) {
        // Fallback para quando a API não está disponível
        console.log('⚠️ API de tipos não disponível, usando fallback');
        const fallbackTypes = [
          { id: 'cutting', value_option: 'Cutting (Perda de Peso)' },
          { id: 'bulking', value_option: 'Bulking (Ganho de Massa)' },
          { id: 'maintenance', value_option: 'Manutenção' },
          { id: 'recomposition', value_option: 'Recomposição Corporal' },
          { id: 'performance', value_option: 'Performance Esportiva' }
        ];
        setNutriTypes(fallbackTypes);
        console.log('📋 Tipos de protocolo carregados (fallback):', fallbackTypes);
      }
    };
    fetchNutriTypes();
  }, []);

  // Load initial data if provided
  React.useEffect(() => {
    if (initialData) {
      console.log('🔄 DietProtocolManualCreator: Carregando dados iniciais:', initialData);

      setName(initialData.name || '');
      setType(initialData.type || '');
      setObjective(initialData.objective || '');

      console.log('🔧 Definindo tipo do protocolo:', initialData.type);

      console.log('📝 Campos básicos carregados:', {
        name: initialData.name,
        type: initialData.type,
        objective: initialData.objective
      });

      // Handle both nutritional_goals and goals properties
      if (initialData.nutritional_goals) {
        setGoals(initialData.nutritional_goals);
        console.log('🎯 Metas nutricionais carregadas (nutritional_goals):', initialData.nutritional_goals);
      } else if (initialData.goals) {
        setGoals(initialData.goals);
        console.log('🎯 Metas nutricionais carregadas (goals):', initialData.goals);
      }

      if (initialData.weeklyMeals) {
        setWeeklyMeals(initialData.weeklyMeals);
        console.log('🍽️ Refeições semanais carregadas:', initialData.weeklyMeals);
        console.log('📊 Estrutura das refeições:', Object.keys(initialData.weeklyMeals));
      }

      if (initialData.supplements) {
        setSupplements(initialData.supplements);
        console.log('💊 Suplementos carregados:', initialData.supplements);
      }
    } else {
      console.log('⚠️ DietProtocolManualCreator: Nenhum dado inicial fornecido');
    }
  }, [initialData]);

  // Calculate current macros
  const currentMacros = React.useMemo(() => {
    const initialValues = { calories: 0, protein: 0, carbs: 0, fat: 0 };
    const dayMeals = weeklyMeals[selectedDay] || [];
    return dayMeals.reduce((dayTotal: any, meal: any) => {
      const mealFoods = meal.foods || [];
      const mealTotal = mealFoods.reduce((mealAcc: any, food: any) => {
        // Safely convert values to numbers, defaulting to 0 if invalid
        const calories = Number(food.calories) || 0;
        const protein = Number(food.protein) || 0;
        const carbs = Number(food.carbs) || 0;
        const fat = Number(food.fat) || 0;

        return {
          calories: mealAcc.calories + calories,
          protein: mealAcc.protein + protein,
          carbs: mealAcc.carbs + carbs,
          fat: mealAcc.fat + fat
        };
      }, initialValues);

      return {
        calories: Math.round(dayTotal.calories + mealTotal.calories),
        protein: Math.round(dayTotal.protein + mealTotal.protein),
        carbs: Math.round(dayTotal.carbs + mealTotal.carbs),
        fat: Math.round(dayTotal.fat + mealTotal.fat)
      };
    }, initialValues);
  }, [weeklyMeals, selectedDay]);

  // Calculate macros for a specific meal
  const calculateMealMacros = (meal: Meal) => {
    const foods = meal.foods || [];
    return foods.reduce((total, food) => {
      // Safely convert values to numbers, defaulting to 0 if invalid
      const calories = Number(food.calories) || 0;
      const protein = Number(food.protein) || 0;
      const carbs = Number(food.carbs) || 0;
      const fat = Number(food.fat) || 0;

      return {
        calories: total.calories + calories,
        protein: total.protein + protein,
        carbs: total.carbs + carbs,
        fat: total.fat + fat
      };
    }, { calories: 0, protein: 0, carbs: 0, fat: 0 });
  };

  const handleCopyToAllDays = () => {
    const currentDayMeals = weeklyMeals[selectedDay] || [];
    const updatedMeals = WEEK_DAYS.reduce((acc, { key: day }) => ({
      ...acc,
      [day]: day === selectedDay ? currentDayMeals : currentDayMeals.map(meal => ({
        ...meal,
        id: `${meal.id}-${day}`
      }))
    }), {} as Record<WeekDay, Meal[]>);
    
    setWeeklyMeals(updatedMeals);
    setDisableCopyToAllDays(true);
  };

  const handleAddMeal = (day: WeekDay) => {
    setWeeklyMeals(prev => ({
      ...prev,
      [day]: [
        ...prev[day],
        {
          id: `meal-${prev[day].length + 1}`,
          name: `Refeição ${prev[day].length + 1}`,
        time: '12:00',
        foods: [],
        completed: false
        }
      ]
    }));
  };

  const handleAddFood = (day: WeekDay, mealIndex: number, food: Food) => {
    setWeeklyMeals(prev => ({
      ...prev,
      [day]: (prev[day] || []).map((meal, idx) =>
        idx === mealIndex
          ? { ...meal, foods: [...(meal.foods || []), food] }
          : meal
      )
    }));
    setDisableCopyToAllDays(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if(showFoodSelector) return;

    if (!name) return;

    // if (!name || Object.values(weeklyMeals).every(meals => meals.length === 0)) return;
    const saveData = {
      name,
      nutritional_goals: goals,
      objective,
      type,
      weeklyMeals,
      supplements: supplements?.map((supplement: any) => ({
        name: supplement.name,
        dosage: supplement.dosage,
        supplement_time: supplement.supplement_time,
        notes: supplement.notes
      })) || [],
      startDate: new Date().toISOString()
    };
    console.log('saveData', saveData);
    console.log('supplements', supplements);
    onSave(saveData);
  };

  return (
    <>
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
            <FileText className="w-5 h-5 text-snapfit-green" />
          </div>
          <h2 className="text-xl font-bold text-white">
            {initialData ? 'Editar Protocolo' : 'Criar Protocolo Manual'}
          </h2>
        </div>
      </div>


      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">
          Nome do Protocolo
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white placeholder-gray-500"
          placeholder="Ex: Dieta de Cutting Personalizada"
          required
        />
      </div>

      
      <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-400">
              Tipo
            </label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value)}
              className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
            >
              <option value="">Selecione...</option>
              {nutriTypes.map(({ id, value_option }) => (
                <option key={id} value={id}>{value_option}</option>
              ))}
            </select>
          </div>

          

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-400">
              Objetivo
            </label>
            <textarea
              value={objective}
              onChange={(e) => setObjective(e.target.value)}
              className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg h-20 resize-none focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white placeholder-gray-500"
              placeholder="Descreva o objetivo principal deste protocolo..."
            />
          </div>

      

      <div className="border-t border-snapfit-green/20 pt-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
            <h3 className="text-lg font-medium text-white flex items-center gap-2">
              <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                <span className="text-snapfit-green text-sm">🎯</span>
              </div>
              Metas Nutricionais
            </h3>
            <button
              type="button"
              onClick={() => setShowAICalculator(true)}
              className="flex items-center gap-2 px-4 py-2 text-sm text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-all duration-200 shadow-lg hover:shadow-snapfit-green/50"
            >
              <span className="text-lg">🤖</span>
              <span className="hidden sm:inline">Calcular com IA</span>
              <span className="sm:hidden">IA</span>
            </button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-400">
                Calorias (kcal)
              </label>
              <input
                type="number"
                value={goals.calories || ''}
                onChange={(e) => setGoals(prev => ({
                  ...prev,
                  calories: Number(e.target.value)
                }))}
                className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                min="0"
                placeholder="2000"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-400">
                Proteína (g)
              </label>
              <input
                type="number"
                value={goals.protein || ''}
                onChange={(e) => setGoals(prev => ({
                  ...prev,
                  protein: Number(e.target.value)
                }))}
                className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                min="0"
                placeholder="150"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-400">
                Carboidratos (g)
              </label>
              <input
                type="number"
                value={goals.carbs || ''}
                onChange={(e) => setGoals(prev => ({
                  ...prev,
                  carbs: Number(e.target.value)
                }))}
                className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                min="0"
                placeholder="200"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-400">
                Gorduras (g)
              </label>
              <input
                type="number"
                value={goals.fat || ''}
                onChange={(e) => setGoals(prev => ({
                  ...prev,
                  fat: Number(e.target.value)
                }))}
                className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                min="0"
                placeholder="70"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-400">
                Água (ml)
              </label>
              <input
                type="number"
                value={goals.water || ''}
                onChange={(e) => setGoals(prev => ({
                  ...prev,
                  water: Number(e.target.value)
                }))}
                className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                min="0"
                placeholder="3000"
              />
            </div>
          </div>
        </div>
      
            <MacroCounterUser
              currentValues={currentMacros}
              goals={goals}
            />

          {/* Estimativa de Perda/Ganho de Peso */}
          {goals.calories > 0 && (
            <div className="mt-6">
              <WeightEstimationCard
                targetCalories={goals.calories}
                className="w-full"
              />
            </div>
          )}

      {/* Seleção de Dias da Semana */}
      <div className="border-t border-snapfit-green/20 pt-6">
        <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
          <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
            <span className="text-snapfit-green text-sm">📅</span>
          </div>
          Selecionar Dia para Editar
        </h3>
        <div className="flex gap-3 overflow-x-auto pb-2 mb-6">
          {WEEK_DAYS.map(({ key, label }) => (
            <button
              key={key}
              type="button"
              onClick={() => setSelectedDay(key)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all duration-200 text-sm ${
                selectedDay === key
                  ? 'bg-snapfit-green text-black border border-snapfit-green shadow-lg shadow-snapfit-green/20'
                  : 'bg-snapfit-dark-gray text-gray-400 border border-snapfit-green/20 hover:bg-snapfit-green/10 hover:text-white'
              }`}
            >
              <Calendar className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <h3 className="text-lg font-medium text-white flex items-center gap-2">
            <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
              <span className="text-snapfit-green text-sm">🍽️</span>
            </div>
            Refeições - {WEEK_DAYS.find(d => d.key === selectedDay)?.label}
          </h3>
          <button
            type="button"
            onClick={() => handleCopyToAllDays()}
            className={`
            flex items-center justify-center gap-2 px-3 py-2 text-sm text-gray-400 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg hover:bg-snapfit-green/10 hover:text-white transition-all duration-200 w-full sm:w-auto
            ${disableCopyToAllDays && 'opacity-40 cursor-default pointer-events-none'}
            `}
          >
            <Copy className="w-4 h-4" />
            <span className="hidden sm:inline">Aplicar para Todos os Dias</span>
            <span className="sm:hidden">Copiar para Todos</span>
          </button>
        </div>

        {(weeklyMeals[selectedDay] || []).map((meal, mealIndex) => {
          const mealMacros = calculateMealMacros(meal);

          return (
            <div key={meal.id} className="relative p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20">
              {/* Botão de excluir refeição */}
              <button
                type="button"
                onClick={() => {
                  setWeeklyMeals(prev => ({
                    ...prev,
                    [selectedDay]: prev[selectedDay].filter((_, idx) => idx !== mealIndex)
                  }));
                }}
                className="absolute top-2 right-2 p-1 text-gray-400 hover:text-red-400 transition-colors"
              >
                <XIcon className="w-4 h-4" />
              </button>

              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-4 pr-8">
                <input
                  type="text"
                  value={meal.name}
                  onChange={(e) => {
                    setWeeklyMeals(prev => ({
                      ...prev,
                      [selectedDay]: prev[selectedDay].map((m, idx) =>
                        idx === mealIndex ? { ...m, name: e.target.value } : m
                      )
                    }));
                  }}
                  className="flex-1 text-sm font-medium bg-transparent border-none focus:ring-0 text-white placeholder-gray-400"
                  placeholder="Nome da Refeição"
                />
                <input
                  type="time"
                  value={meal.time}
                  onChange={(e) => {
                    setWeeklyMeals(prev => ({
                      ...prev,
                      [selectedDay]: prev[selectedDay].map((m, idx) =>
                        idx === mealIndex ? { ...m, time: e.target.value } : m
                      )
                    }));
                  }}
                  className="w-full sm:w-auto p-2 text-sm bg-snapfit-gray border border-snapfit-green/20 rounded text-white focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green"
                />
              </div>

              {/* Macros da Refeição */}
              {meal.foods.length > 0 && (
                <div className="mb-4 p-3 bg-snapfit-gray rounded-lg border border-snapfit-green/10">
                  <div className="text-xs text-gray-400 mb-2">Totais da Refeição:</div>
                  <div className="flex flex-wrap gap-2 sm:gap-4 text-sm">
                    <span className="text-snapfit-green font-medium">{mealMacros.calories} kcal</span>
                    <span className="text-blue-400">P: {mealMacros.protein.toFixed(1)}g</span>
                    <span className="text-yellow-400">C: {mealMacros.carbs.toFixed(1)}g</span>
                    <span className="text-red-400">G: {mealMacros.fat.toFixed(1)}g</span>
                  </div>
                </div>
              )}

            <div className="space-y-3">
              {(meal.foods || []).map((food, foodIndex) => (
                <div key={foodIndex} className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-3 p-3 bg-snapfit-gray rounded-lg border border-snapfit-green/10">
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-white truncate">{food.name}</div>
                    <div className="text-xs text-gray-400 flex flex-wrap gap-1">
                      <span>{food.quantity}{food.unit}</span>
                      <span>•</span>
                      <span>{food.calories}kcal</span>
                      <span>•</span>
                      <span>P:{food.protein}g</span>
                      <span>C:{food.carbs}g</span>
                      <span>G:{food.fat}g</span>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setWeeklyMeals(prev => ({
                        ...prev,
                        [selectedDay]: prev[selectedDay].map((m, idx) =>
                          idx === mealIndex
                            ? {
                                ...m,
                                foods: m.foods.filter((_, i) => i !== foodIndex)
                              }
                            : m
                        )
                      }));
                      setDisableCopyToAllDays(false);
                    }}
                    className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors flex-shrink-0"
                  >
                    <Plus className="w-4 h-4 rotate-45" />
                  </button>
                </div>
              ))}

              <button
                type="button"
                onClick={() => {
                  setEditingMealIndex(mealIndex);
                  setSelectedDay(selectedDay);
                  setShowFoodSelector(true);
                }}
                className="w-full p-3 text-sm text-snapfit-green bg-snapfit-green/10 border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/20 transition-all duration-200 flex items-center justify-center gap-2"
              >
                <Plus className="w-4 h-4" />
                <span>Adicionar Alimento</span>
              </button>
            </div>
          </div>
        );
        })}

        {/* Botão Adicionar Refeição - sempre no final */}
        <button
          type="button"
          onClick={() => handleAddMeal(selectedDay)}
          className="w-full flex items-center justify-center gap-2 p-4 text-sm text-snapfit-green bg-snapfit-green/10 border-2 border-dashed border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/20 hover:border-snapfit-green/50 transition-all duration-200"
        >
          <Plus className="w-5 h-5" />
          Adicionar Nova Refeição
        </button>
      </div>


      <div className="border-t border-snapfit-green/20 pt-6">
        <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
          <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
            <span className="text-snapfit-green text-sm">💊</span>
          </div>
          Suplementações
        </h3>
        <button className="flex items-center gap-2 px-3 py-2 text-sm text-snapfit-green bg-snapfit-green/10 border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/20 transition-all duration-200 mb-4"
          onClick={() => setSupplements([...supplements, { name: '', dosage: '', supplement_time: '', time: '', notes: '' }])}
          type="button"
        >
          <Plus className="w-4 h-4" />
          Adicionar Suplemento
        </button>
      {/* Suplementos - adiciona formulario com Nome
Dosagem
Horário Observações */}
        <div className="space-y-4">
          {supplements?.map((supplement: any, index: number) => (
            <div key={index} className="relative p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20">
              <div className="absolute top-2 right-2">
                <button
                  type="button"
                  onClick={() => {
                    const newSupplements = [...supplements];
                    newSupplements.splice(index, 1);
                    setSupplements(newSupplements);
                  }}
                  className="text-gray-400 hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Nome</label>
                  <input
                    type="text"
                    required
                    value={supplement.name}
                    onChange={(e) => {
                      const newSupplements = [...supplements];
                      newSupplements[index].name = e.target.value;
                      setSupplements(newSupplements);
                    }}
                    className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-gray text-white placeholder-gray-500"
                    placeholder="Ex: Whey Protein"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Dosagem</label>
                  <input
                    type="text"
                    value={supplement.dosage}
                    required
                    onChange={(e) => {
                      const newSupplements = [...supplements];
                      newSupplements[index].dosage = e.target.value;
                      setSupplements(newSupplements);
                    }}
                    className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-gray text-white placeholder-gray-500"
                    placeholder="Ex: 30g"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Horário</label>
                  <input
                    type="time"
                    value={supplement.time || ''}
                    onChange={(e) => {
                      const newSupplements = [...supplements];
                      newSupplements[index].time = e.target.value;
                      setSupplements(newSupplements);
                    }}
                    className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-gray text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Momento</label>
                  <input
                    type="text"
                    value={supplement.supplement_time}
                    onChange={(e) => {
                      const supplement_time = e.target.value;
                      const newSupplements = [...supplements];
                      newSupplements[index].supplement_time = supplement_time;
                      setSupplements(newSupplements);
                    }}
                    className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-gray text-white placeholder-gray-500"
                    placeholder="Ex: Pós-treino"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Observações</label>
                <textarea
                  value={supplement.notes}
                  onChange={(e) => {
                    const newSupplements = [...supplements];
                    newSupplements[index].notes = e.target.value;
                    setSupplements(newSupplements);
                  }}
                  className="w-full p-3 text-sm border border-snapfit-green/20 rounded-lg resize-none h-20 focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-gray text-white placeholder-gray-500"
                  placeholder="Observações adicionais..."
                />
              </div>
          </div>
        ))}
      
      </div>
      </div>




      <div className="flex gap-4 pt-6 border-t border-snapfit-green/20">
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 px-6 py-3 text-sm text-gray-400 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg hover:bg-gray-700 hover:text-white transition-all duration-200"
        >
          Cancelar
        </button>
        <button
          type="submit"
          disabled={!name || Object.values(weeklyMeals).every(meals => meals.length === 0)}
          className="flex-1 flex items-center justify-center gap-2 px-6 py-3 text-sm text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-all duration-200 shadow-lg shadow-snapfit-green/20 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Save className="w-4 h-4" />
          Salvar Protocolo
        </button>
      </div>
    </form>

    {showFoodSelector && editingMealIndex !== null && (
      <FoodSelector
        onSelect={(food) => {
          const foodWithId = { ...food, id: food.id || `food-${Date.now()}` };
          handleAddFood(selectedDay, editingMealIndex, foodWithId);
          // Não fechar o modal para permitir múltiplas adições
        }}
        onClose={() => {
          setShowFoodSelector(false);
          setEditingMealIndex(null);
        }}
      />
    )}

    {showAICalculator && (
      <AICalorieCalculator
        onCalculate={(calculatedGoals) => {
          setGoals(calculatedGoals);
          setShowAICalculator(false);
        }}
        onClose={() => setShowAICalculator(false)}
      />
    )}
    </>
  );
}