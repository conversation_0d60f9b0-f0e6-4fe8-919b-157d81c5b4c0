import React from 'react';
import { Brain, Scale, UtensilsCrossed, ChevronRight, ChevronLeft, Target as TargetIcon, Loader2, AlertCircle } from 'lucide-react';
import { LoadingScreen } from './LoadingScreen';
import { useCreateDietProtocolAI, checkProtocolConsistency } from '../hooks/useProtocols';
import { toast } from 'react-toastify';
import { useForm, Controller } from 'react-hook-form';
import { DietProtocolAIData } from '../types/protocol';
import { validateDietAIData, DIET_AI_VALIDATION_RULES } from '../validation/protocolValidation';

interface DietProtocolAIFormProps {
  onGenerate: (data: DietProtocolAIData) => Promise<void>;
  onCancel: () => void;
}

const ACTIVITY_LEVELS = {
  sedentary: 'Sedentário (pouco ou nenhum exercício)',
  lightly_active: 'Levemente Ativo (exercício leve 1-3x/semana)',
  moderately_active: 'Moderadamente Ativo (exercício moderado 3-5x/semana)',
  very_active: 'Muito Ativo (exercício intenso 6-7x/semana)',
  extra_active: 'Extra Ativo (exercício muito intenso + trabalho físico)'
};

const DIETARY_PREFERENCES = [
  'Vegetariano',
  'Vegano',
  'Low Carb',
  'Cetogênica',
  'Mediterrânea',
  'Paleo',
  'Sem Glúten',
  'Sem Lactose'
];

export function DietProtocolAIForm({ onGenerate, onCancel }: DietProtocolAIFormProps) {
  const [step, setStep] = React.useState(1);
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [showLoadingScreen, setShowLoadingScreen] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [includedFood, setIncludedFood] = React.useState('');
  
  // Active protocol checking is now handled at the page level

  // React Hook Form setup
  const {
    control,
    handleSubmit: onSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    trigger,
    getValues
  } = useForm<DietProtocolAIData>({
    mode: 'onChange',
    defaultValues: {
      height: 0,
      weight: 0,
      goal: 'Emagrecimento',
      bodyFat: 0,
      activityLevel: 'moderately_active',
      dietaryPreferences: [],
      restrictions: [],
      includedFoods: [],
      mealFrequency: 5,
      notes: ''
    }
  });

  // Watch form values for dynamic updates
  const watchedValues = watch();

  React.useEffect(() => {
    setIsLoading(false);
  }, []);



  const addIncludedFood = () => {
    if (includedFood.trim()) {
      const currentFoods = getValues('includedFoods') || [];
      setValue('includedFoods', [...currentFoods, includedFood.trim()]);
      setIncludedFood('');
    }
  };

  const removeIncludedFood = (index: number) => {
    const currentFoods = getValues('includedFoods') || [];
    setValue('includedFoods', currentFoods.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (includedFood.trim()) {
        addIncludedFood();
      }
    }
  };

  const handleFormSubmit = async (data: DietProtocolAIData) => {
    setShowLoadingScreen(true);
    setError(null);
    setIsGenerating(true);

    try {
      console.log('🚀 Dados do formulário validados:', data);
      await onGenerate(data);
    } catch (err: any) {
      console.error('Erro na geração do protocolo:', err);
      
      // Handle specific error cases
      if (err?.response?.status === 400) {
        const errorData = err?.response?.data;
        console.log('🔍 Erro 400 no form - dados completos:', errorData);

        // Extrair mensagem de erro da API
        let errorMessage = 'Dados inválidos. Verifique se todos os campos foram preenchidos corretamente.';

        if (errorData?.errors && Array.isArray(errorData.errors)) {
          // Formato: { errors: ["Você já possui um protocolo ativo."] }
          errorMessage = errorData.errors.join(' ');
        } else if (errorData?.message && Array.isArray(errorData.message)) {
          // Formato: { message: ["Você já possui um protocolo ativo."] }
          errorMessage = errorData.message.join(' ');
        } else if (errorData?.message && typeof errorData.message === 'string') {
          // Formato: { message: "Você já possui um protocolo ativo." }
          errorMessage = errorData.message;
        }

        console.log('📝 Mensagem de erro extraída no form:', errorMessage);
        setError(errorMessage);
      } else if (err?.response?.status === 401) {
        setError('Sessão expirada. Faça login novamente.');
      } else {
        setError(err instanceof Error ? err.message : 'Erro ao gerar protocolo. Tente novamente.');
      }
      
      setShowLoadingScreen(false);
      setIsGenerating(false);
    }
  };

  const handleStepSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (step < 4) {
      // Validate current step before proceeding
      const fieldsToValidate = getFieldsForStep(step);
      const isStepValid = await trigger(fieldsToValidate);
      if (isStepValid) {
        setStep(step + 1);
      }
      return;
    }

    // Final submission - use React Hook Form validation
    onSubmit(handleFormSubmit)();
  };

  const getFieldsForStep = (currentStep: number): (keyof DietProtocolAIData)[] => {
    switch (currentStep) {
      case 1:
        return ['height', 'weight', 'bodyFat', 'activityLevel'];
      case 2:
        return ['goal'];
      case 3:
        return ['mealFrequency'];
      case 4:
        return [];
      default:
        return [];
    }
  };

  if (showLoadingScreen) {
    return <LoadingScreen type="diet" />;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600" />
      </div>
    );
  }

  // Note: Active protocol checking is now handled at the page level with confirmation dialog

  return (
    <div className="bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
          <Brain className="w-5 h-5 text-snapfit-green" />
        </div>
        <div>
          <h2 className="text-sm font-medium text-white">Gerar Protocolo com IA</h2>
          <p className="text-xs text-gray-400">Campos marcados com * são obrigatórios</p>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
          <span>Passo {step} de 4</span>
          <span>{Math.round((step / 4) * 100)}% completo</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-1.5">
          <div 
            className="bg-snapfit-green h-1.5 rounded-full transition-all duration-300"
            style={{ width: `${(step / 4) * 100}%` }}
          />
        </div>
      </div>

      <div className="space-y-6">
        <form onSubmit={handleStepSubmit}>
          {step === 1 && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white flex items-center gap-1.5">
                <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                  <Scale className="w-3.5 h-3.5 text-snapfit-green" />
                </div>
                Dados Corporais
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-400 mb-1.5">
                    Altura (cm) *
                  </label>
                  <Controller
                    name="height"
                    control={control}
                    rules={{
                      required: 'Altura é obrigatória',
                      min: { value: 100, message: 'Altura deve ser pelo menos 100cm' },
                      max: { value: 250, message: 'Altura deve ser no máximo 250cm' }
                    }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="number"
                        className={`w-full p-2 text-xs border rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white ${
                          errors.height ? 'border-red-500' : 'border-snapfit-green/20'
                        }`}
                        placeholder="Ex: 175"
                        min="100"
                        max="250"
                      />
                    )}
                  />
                  {errors.height && (
                    <p className="text-red-400 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.height.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-400 mb-1.5">
                    Peso (kg) *
                  </label>
                  <Controller
                    name="weight"
                    control={control}
                    rules={{
                      required: 'Peso é obrigatório',
                      min: { value: 30, message: 'Peso deve ser pelo menos 30kg' },
                      max: { value: 300, message: 'Peso deve ser no máximo 300kg' }
                    }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="number"
                        step="0.1"
                        className={`w-full p-2 text-xs border rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white ${
                          errors.weight ? 'border-red-500' : 'border-snapfit-green/20'
                        }`}
                        placeholder="Ex: 75.5"
                        min="30"
                        max="300"
                      />
                    )}
                  />
                  {errors.weight && (
                    <p className="text-red-400 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.weight.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-400 mb-1.5">
                    Nível de Atividade *
                  </label>
                  <Controller
                    name="activityLevel"
                    control={control}
                    rules={{ required: 'Nível de atividade é obrigatório' }}
                    render={({ field }) => (
                      <select
                        {...field}
                        className={`w-full p-2 text-xs border rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white ${
                          errors.activityLevel ? 'border-red-500' : 'border-snapfit-green/20'
                        }`}
                      >
                        {Object.entries(ACTIVITY_LEVELS).map(([value, label]) => (
                          <option key={value} value={value}>{label}</option>
                        ))}
                      </select>
                    )}
                  />
                  {errors.activityLevel && (
                    <p className="text-red-400 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.activityLevel.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-400 mb-1.5">
                    Percentual de Gordura (%) *
                  </label>
                  <Controller
                    name="bodyFat"
                    control={control}
                    rules={{
                      required: 'Percentual de gordura é obrigatório',
                      min: { value: 3, message: 'Percentual deve ser pelo menos 3%' },
                      max: { value: 50, message: 'Percentual deve ser no máximo 50%' }
                    }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="number"
                        step="0.1"
                        className={`w-full p-2 text-xs border rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white ${
                          errors.bodyFat ? 'border-red-500' : 'border-snapfit-green/20'
                        }`}
                        placeholder="Ex: 15.0"
                        min="3"
                        max="50"
                      />
                    )}
                  />
                  {errors.bodyFat && (
                    <p className="text-red-400 text-xs mt-1 flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.bodyFat.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white flex items-center gap-1.5">
                <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                  <TargetIcon className="w-3.5 h-3.5 text-snapfit-green" />
                </div>
                Objetivo
              </h3>

              <Controller
                name="goal"
                control={control}
                rules={{ required: 'Objetivo é obrigatório' }}
                render={({ field }) => (
                  <div className="space-y-2">
                    {['Emagrecimento', 'Manutenção', 'Ganho de Massa Muscular'].map((goal) => (
                      <label
                        key={goal}
                        className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                          field.value === goal
                            ? 'border-snapfit-green bg-snapfit-green/10'
                            : 'border-gray-600 hover:border-gray-500'
                        }`}
                      >
                        <input
                          type="radio"
                          {...field}
                          value={goal}
                          checked={field.value === goal}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                          field.value === goal
                            ? 'border-snapfit-green bg-snapfit-green'
                            : 'border-gray-400'
                        }`}>
                          {field.value === goal && (
                            <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                          )}
                        </div>
                        <span className="text-sm text-white">{goal}</span>
                      </label>
                    ))}
                  </div>
                )}
              />
              {errors.goal && (
                <p className="text-red-400 text-xs mt-1 flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {errors.goal.message}
                </p>
              )}
            </div>
          )}

          {step === 3 && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white flex items-center gap-1.5">
                <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                  <UtensilsCrossed className="w-3.5 h-3.5 text-snapfit-green" />
                </div>
                Preferências Alimentares
              </h3>

              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1.5">
                  Frequência de Refeições por Dia *
                </label>
                <Controller
                  name="mealFrequency"
                  control={control}
                  rules={{
                    required: 'Frequência de refeições é obrigatória',
                    min: { value: 3, message: 'Mínimo de 3 refeições por dia' },
                    max: { value: 8, message: 'Máximo de 8 refeições por dia' }
                  }}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="number"
                      className={`w-full p-2 text-xs border rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white ${
                        errors.mealFrequency ? 'border-red-500' : 'border-snapfit-green/20'
                      }`}
                      placeholder="Ex: 5"
                      min="3"
                      max="8"
                    />
                  )}
                />
                {errors.mealFrequency && (
                  <p className="text-red-400 text-xs mt-1 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {errors.mealFrequency.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-400 mb-2">
                  Preferências Dietéticas
                </label>
                <Controller
                  name="dietaryPreferences"
                  control={control}
                  render={({ field }) => (
                    <div className="grid grid-cols-2 gap-2">
                      {DIETARY_PREFERENCES.map((pref) => (
                        <label
                          key={pref}
                          className="flex items-center p-2 border border-gray-600 rounded-lg cursor-pointer hover:border-gray-500 transition-colors"
                        >
                          <input
                            type="checkbox"
                            checked={field.value.includes(pref)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                field.onChange([...field.value, pref]);
                              } else {
                                field.onChange(field.value.filter(p => p !== pref));
                              }
                            }}
                            className="w-3 h-3 text-snapfit-green bg-gray-700 border-gray-600 rounded focus:ring-snapfit-green focus:ring-1"
                          />
                          <span className="ml-2 text-xs text-white">{pref}</span>
                        </label>
                      ))}
                    </div>
                  )}
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1.5">
                  Restrições Alimentares
                </label>
                <Controller
                  name="restrictions"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      className="w-full p-2 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                      placeholder="Ex: Alergia a amendoim, intolerância à lactose..."
                      rows={3}
                      onChange={(e) => {
                        const restrictions = e.target.value.split(',').map(r => r.trim()).filter(r => r);
                        field.onChange(restrictions);
                      }}
                      value={field.value.join(', ')}
                    />
                  )}
                />
              </div>
            </div>
          )}

          {step === 4 && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white flex items-center gap-1.5">
                <div className="w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
                  <UtensilsCrossed className="w-3.5 h-3.5 text-snapfit-green" />
                </div>
                Finalização & Revisão
              </h3>

              {/* Resumo dos dados */}
              <div className="bg-gray-800/50 rounded-lg p-3 border border-gray-700">
                <h4 className="text-xs font-medium text-gray-300 mb-2">Resumo dos seus dados:</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-gray-400">Altura:</span>
                    <span className="text-white ml-1">{watchedValues.height || 0}cm</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Peso:</span>
                    <span className="text-white ml-1">{watchedValues.weight || 0}kg</span>
                  </div>
                  <div>
                    <span className="text-gray-400">% Gordura:</span>
                    <span className="text-white ml-1">{watchedValues.bodyFat || 0}%</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Objetivo:</span>
                    <span className="text-white ml-1">{watchedValues.goal}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Atividade:</span>
                    <span className="text-white ml-1">{ACTIVITY_LEVELS[watchedValues.activityLevel]?.split('(')[0]}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Refeições/dia:</span>
                    <span className="text-white ml-1">{watchedValues.mealFrequency || 5}</span>
                  </div>
                </div>
                {watchedValues.dietaryPreferences.length > 0 && (
                  <div className="mt-2">
                    <span className="text-gray-400 text-xs">Preferências:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {watchedValues.dietaryPreferences.map((pref, index) => (
                        <span key={index} className="px-2 py-0.5 text-xs bg-snapfit-green/20 text-snapfit-green rounded">
                          {pref}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1.5">
                  Alimentos que você gosta de incluir
                </label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={includedFood}
                      onChange={(e) => setIncludedFood(e.target.value)}
                      onKeyDown={handleKeyPress}
                      className="flex-1 p-2 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                      placeholder="Ex: Batata Doce"
                    />
                    <button
                      type="button"
                      onClick={addIncludedFood}
                      className="px-3 py-2 text-xs bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/30 transition-colors"
                    >
                      Adicionar
                    </button>
                  </div>

                  {watchedValues.includedFoods.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {watchedValues.includedFoods.map((food, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-snapfit-green/20 text-snapfit-green rounded-full"
                        >
                          {food}
                          <button
                            type="button"
                            onClick={() => removeIncludedFood(index)}
                            className="text-snapfit-green hover:text-red-400 transition-colors"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-400 mb-1.5">
                  Observações Adicionais
                </label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      className="w-full p-2 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"
                      placeholder="Qualquer informação adicional que possa ajudar na criação do protocolo..."
                      rows={4}
                    />
                  )}
                />
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-xs flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {error}
              </p>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <button
              type="button"
              onClick={step === 1 ? onCancel : () => setStep(step - 1)}
              className="flex items-center gap-1.5 px-4 py-2 text-xs font-medium text-gray-400 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
            >
              <ChevronLeft className="w-3.5 h-3.5" />
              {step === 1 ? 'Cancelar' : 'Anterior'}
            </button>

            <button
              type="submit"
              disabled={isGenerating}
              className="flex items-center gap-1.5 px-4 py-2 text-xs font-medium text-white bg-snapfit-green rounded-lg hover:bg-snapfit-green/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-3.5 h-3.5 animate-spin" />
                  Gerando...
                </>
              ) : step === 4 ? (
                <>
                  <Brain className="w-3.5 h-3.5" />
                  Gerar Protocolo
                </>
              ) : (
                <>
                  Próximo
                  <ChevronRight className="w-3.5 h-3.5" />
                </>
              )}
            </button>
          </div>

        </form>
      </div>
    </div>
  );
}
