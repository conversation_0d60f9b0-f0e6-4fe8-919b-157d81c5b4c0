import{h as u,u as c,at as d,y as n,b as t}from"./index-BwF2e626.js";const i={all:["diary"],entries:()=>[...i.all,"entries"],entry:e=>[...i.entries(),e],nutritionalSummary:(e,a)=>[...i.all,"nutritional-summary",e,a],meals:()=>[...i.all,"meals"],mealsByDate:e=>[...i.meals(),e],activities:()=>[...i.all,"activities"],activitiesByDate:e=>[...i.activities(),e],water:()=>[...i.all,"water"],waterByDate:e=>[...i.water(),e]};function l(e,a){return u({queryKey:i.nutritionalSummary(e,a),queryFn:async()=>{console.log("🔄 useNutritionalSummary: Buscando resumo nutricional...",{dateStart:e,dateEnd:a});try{if(e===a)try{const o=await t.get("dashboard/nutritional-summary",{searchParams:{date:e}});return console.log("🍎 useNutritionalSummary: Dashboard resumo recebido:",o.data),o.data}catch{console.log("Dashboard endpoint failed, trying users endpoint...")}const s=await t.get("users/diary/nutritional_summary",{searchParams:{date_start:e,date_end:a}});return console.log("🍎 useNutritionalSummary: Users resumo recebido:",s.data),s.data}catch(s){throw console.warn("⚠️ useNutritionalSummary: All endpoints failed:",s),s}},staleTime:1e3*60*2,enabled:!!e&&!!a})}function m(e){return u({queryKey:i.entry(e),queryFn:async()=>{console.log("🔄 useDiaryEntry: Buscando entrada do diário...",e);const a=await t.get("users/diary",{searchParams:{date:e}});return console.log("📖 useDiaryEntry: Entrada recebida:",a.data),a.data},staleTime:1e3*60*5,enabled:!!e})}function v(e){return u({queryKey:i.mealsByDate(e),queryFn:async()=>{console.log("🔄 useMealsByDate: Buscando refeições...",e);const a=await t.get("users/diary/meals",{searchParams:{date:e}});return console.log("🍽️ useMealsByDate: Refeições recebidas:",a.data),a.data},staleTime:1e3*60*3,enabled:!!e})}function g(e){return u({queryKey:i.activitiesByDate(e),queryFn:async()=>{console.log("🔄 useActivitiesByDate: Buscando atividades...",e);const a=await t.get("users/diary/activities",{searchParams:{date:e}});return console.log("🏃 useActivitiesByDate: Atividades recebidas:",a.data),a.data},staleTime:1e3*60*3,enabled:!!e})}function p(e){return u({queryKey:i.waterByDate(e),queryFn:async()=>{console.log("🔄 useWaterByDate: Buscando consumo de água...",e);const a=await t.get("users/diary/water",{searchParams:{date:e}});return console.log("💧 useWaterByDate: Consumo recebido:",a.data),a.data},staleTime:1e3*60*5,enabled:!!e})}function A(){const e=c();return d({mutationFn:async({date:a,mealData:s})=>(console.log("🍽️ useAddMeal: Adicionando refeição...",{date:a,mealData:s}),await t.post("users/diary/meals",{date:a,...s})),onSuccess:(a,s)=>{e.invalidateQueries({queryKey:i.mealsByDate(s.date)}),e.invalidateQueries({queryKey:i.entry(s.date)}),e.invalidateQueries({queryKey:i.nutritionalSummary(s.date,s.date)}),n.success("Refeição adicionada com sucesso!",{position:"bottom-right"}),console.log("✅ useAddMeal: Refeição adicionada com sucesso")},onError:a=>{var o,r;const s=((r=(o=a==null?void 0:a.response)==null?void 0:o.payload)==null?void 0:r.message)||"Erro ao adicionar refeição";n.error(s,{position:"bottom-right"}),console.error("❌ useAddMeal: Erro ao adicionar refeição:",a)}})}function B(){const e=c();return d({mutationFn:async({date:a,activityData:s})=>(console.log("🏃 useAddActivity: Adicionando atividade...",{date:a,activityData:s}),await t.post("users/diary/activities",{date:a,...s})),onSuccess:(a,s)=>{e.invalidateQueries({queryKey:i.activitiesByDate(s.date)}),e.invalidateQueries({queryKey:i.entry(s.date)}),e.invalidateQueries({queryKey:i.nutritionalSummary(s.date,s.date)}),n.success("Atividade adicionada com sucesso!",{position:"bottom-right"}),console.log("✅ useAddActivity: Atividade adicionada com sucesso")},onError:a=>{var o,r;const s=((r=(o=a==null?void 0:a.response)==null?void 0:o.payload)==null?void 0:r.message)||"Erro ao adicionar atividade";n.error(s,{position:"bottom-right"}),console.error("❌ useAddActivity: Erro ao adicionar atividade:",a)}})}function q(){const e=c();return d({mutationFn:async({activityId:a,date:s})=>(console.log("🗑️ useRemoveActivity: Removendo atividade...",{activityId:a,date:s}),await t.delete(`users/diary/activities/${a}`)),onSuccess:(a,s)=>{e.invalidateQueries({queryKey:i.activitiesByDate(s.date)}),e.invalidateQueries({queryKey:i.entry(s.date)}),e.invalidateQueries({queryKey:i.nutritionalSummary(s.date,s.date)}),n.success("Atividade removida com sucesso!",{position:"bottom-right"}),console.log("✅ useRemoveActivity: Atividade removida com sucesso")},onError:a=>{var o,r;const s=((r=(o=a==null?void 0:a.response)==null?void 0:o.payload)==null?void 0:r.message)||"Erro ao remover atividade";n.error(s,{position:"bottom-right"}),console.error("❌ useRemoveActivity: Erro ao remover atividade:",a)}})}export{A as a,m as b,v as c,g as d,p as e,B as f,q as g,l as u};
