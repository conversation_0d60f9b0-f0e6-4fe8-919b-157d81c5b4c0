import{t as C,aR as D,r as u,b as d,y as x,j as e,aO as j,H as g,a as I,bi as S}from"./index-PNQkLtwW.js";function A(){const f=C(),{protocolId:n}=D(),[h,w]=u.useState(null),[P,b]=u.useState(!0),[N,y]=u.useState(null);u.useEffect(()=>{n&&M()},[n]);const M=async()=>{var s,p,i,c;try{b(!0),y(null),console.log("Fetching protocol data for ID:",n);try{console.log("🔍 Tentando buscar protocolo ativo...");let o=null,a=null;try{console.log("🔍 Tentando API antiga: users/protocols/diet/active"),a=await d.get("users/protocols/diet/active"),console.log("🔍 Resposta da API antiga:",a),((a==null?void 0:a.status)==="success"&&((s=a==null?void 0:a.data)!=null&&s.has_protocol)||a!=null&&a.data&&a.data.id)&&(o=a.data)}catch(t){console.log("⚠️ API antiga falhou, tentando nova API...");try{console.log("🔍 Tentando nova API: diet/protocol/current"),a=await d.get("diet/protocol/current"),console.log("🔍 Resposta da nova API:",a),o=a}catch(v){console.log("⚠️ Nova API também falhou, tentando buscar por ID...");try{console.log("🔍 Tentando buscar por ID: diet/protocols/"+n),a=await d.get(`diet/protocols/${n}`),console.log("🔍 Resposta da busca por ID:",a),o=a}catch(l){throw console.log("❌ Todas as APIs falharam:",{oldApiError:t,newApiError:v,idApiError:l}),t}}}if(o&&(o.id.toString()===n||!n)){console.log("✅ Protocolo ativo encontrado:",o),console.log("🔍 Estrutura completa do protocolo:",JSON.stringify(o,null,2)),console.log("🔍 Campos de tipo disponíveis:",{dietType:o.dietType,type:o.type,type_id:o.type_id,value_option:o.value_option});let t="cutting";if(o.dietType)t=o.dietType,console.log("📋 Usando dietType do backend:",t);else if(o.value_option)t={Emagrecimento:"cutting","Cutting (Perda de Peso)":"cutting","Bulking (Ganho de Massa)":"bulking",Manutenção:"maintenance","Recomposição Corporal":"recomposition","Performance Esportiva":"performance"}[o.value_option]||"cutting",console.log("📋 Mapeando value_option para dietType:",o.value_option,"->",t);else if(o.type&&o.type!=="diet"&&o.type!=="workout")t=o.type,console.log("📋 Usando type como dietType:",t);else{const l=(o.name||"").toLowerCase(),k=(o.objective||"").toLowerCase();l.includes("emagrecimento")||l.includes("cutting")||k.includes("perda de peso")?t="cutting":l.includes("bulking")||l.includes("ganho")||k.includes("ganho de massa")?t="bulking":(l.includes("manutenção")||l.includes("maintenance"))&&(t="maintenance"),console.log("📋 Inferindo tipo do nome/objetivo:",t)}const v={name:o.name,objective:o.objective,type:t,goals:o.nutritional_goals||o.goals||{calories:0,protein:0,carbs:0,fat:0,water:0},weeklyMeals:o.weeklyMeals||o.meals||{},supplements:o.supplements||[],notes:o.notes||o.general_notes||"",waterCalculationMethod:o.waterCalculationMethod||"weight"};w(v);return}console.log("⚠️ Protocolo ativo não corresponde ao ID solicitado")}catch(o){console.log("❌ Backend não disponível, usando dados mock para teste:",o.message)}if(console.log("💾 Carregando dados mock para protocolo:",n),localStorage.getItem("mock-protocol-deleted")==="true"&&(n==="mock-protocol-id-123"||n==="78")){console.log("🗑️ Protocolo mock foi deletado, redirecionando..."),y("Protocolo não encontrado ou foi removido"),b(!1);return}if(n==="mock-protocol-id-123"||n==="78"){const o=localStorage.getItem("mock-protocol-data");let a;if(o)try{const t=JSON.parse(o);a={name:t.name,objective:t.objective,type:t.type||"cutting",goals:t.goals,weeklyMeals:t.weeklyMeals,supplements:t.supplements,notes:t.notes,waterCalculationMethod:t.waterCalculationMethod},console.log("Loaded saved mock data:",a)}catch{console.log("Error parsing saved data, using default"),a=null}a||(a={name:"Protocolo de Teste para Edição",objective:"Testar funcionalidade de edição de protocolos",type:"cutting",goals:{calories:2e3,protein:150,carbs:200,fat:70,water:2500},weeklyMeals:{monday:[{id:"meal-1",name:"Café da Manhã",time:"08:00",foods:[{id:"food-1",name:"Aveia",quantity:50,unit:"g",calories:190,protein:6.5,carbs:32,fat:3.5},{id:"food-2",name:"Banana",quantity:1,unit:"unidade",calories:105,protein:1.3,carbs:27,fat:.3}]},{id:"meal-2",name:"Almoço",time:"12:00",foods:[{id:"food-3",name:"Peito de Frango",quantity:150,unit:"g",calories:248,protein:46.2,carbs:0,fat:5.4},{id:"food-4",name:"Arroz Integral",quantity:100,unit:"g",calories:123,protein:2.6,carbs:25,fat:.9}]}],tuesday:[{id:"meal-3",name:"Café da Manhã",time:"08:00",foods:[{id:"food-5",name:"Pão Integral",quantity:2,unit:"fatias",calories:160,protein:6,carbs:30,fat:2}]}],wednesday:[{id:"meal-4",name:"Café da Manhã",time:"08:00",foods:[{id:"food-6",name:"Iogurte Grego",quantity:200,unit:"g",calories:130,protein:20,carbs:9,fat:0}]}],thursday:[],friday:[],saturday:[],sunday:[]},supplements:[{name:"Whey Protein",dosage:"30g",supplement_time:"Pós-treino",notes:"Misturar com água"}],notes:"Protocolo de teste para verificar funcionalidade de edição",waterCalculationMethod:"manual"}),console.log("✅ Dados do protocolo carregados:",a),console.log("🔍 Verificando estrutura das refeições:",a.weeklyMeals),console.log("📋 Tipo do protocolo:",a.type),w(a)}else throw new Error("Protocol not found")}catch(r){console.error("Error fetching protocol data:",r);let o="Erro ao carregar dados do protocolo. Verifique se o protocolo existe.";((p=r==null?void 0:r.response)==null?void 0:p.status)===404?o="Protocolo não encontrado. Verifique se o ID está correto.":((i=r==null?void 0:r.response)==null?void 0:i.status)===401?o="Não autorizado. Faça login novamente.":((c=r==null?void 0:r.response)==null?void 0:c.status)===403&&(o="Acesso negado. Você não tem permissão para acessar este protocolo."),y(o),x.error("Erro ao carregar protocolo",{position:"bottom-right"})}finally{b(!1)}},E=async s=>{var p;try{console.log("Saving protocol with data:",s);try{const i={name:s.name,objective:s.objective,notes:s.notes||"",nutritional_goals:s.nutritional_goals||s.goals,weeklyMeals:s.weeklyMeals,supplements:((p=s.supplements)==null?void 0:p.map(r=>({name:r.name,dosage:r.dosage,supplement_time:r.supplement_time||r.time,notes:r.notes})))||[],waterCalculationMethod:s.waterCalculationMethod||"weight",dietType:s.type||"cutting",startDate:s.startDate||new Date().toISOString()};console.log("🔄 Enviando dados para atualização:",i);let c=null;try{c=await d.put(`diet/protocols/${n}`,i)}catch{console.log("First endpoint failed, trying alternative...");try{c=await d.put(`users/protocols/diet/${n}`,i)}catch{console.log("Second endpoint failed, trying create as fallback..."),c=await d.post("users/protocols/diet",{...i,type_id:1})}}if(c){x.success("Protocolo de dieta atualizado com sucesso!",{position:"bottom-right"}),setTimeout(()=>{f("/dashboard/diet")},2e3);return}}catch(i){console.error("❌ Erro ao atualizar protocolo no backend:",i),console.log("💾 Tentando salvar localmente como fallback")}if(n==="mock-protocol-id-123"){const i={id:n,name:s.name,objective:s.objective,goals:s.goals,weeklyMeals:s.weeklyMeals,supplements:s.supplements||[],notes:s.notes||"",waterCalculationMethod:s.waterCalculationMethod||"weight",lastUpdated:new Date().toISOString()};localStorage.setItem("mock-protocol-data",JSON.stringify(i)),console.log("Mock save successful:",i),x.success("Protocolo de dieta atualizado com sucesso! (Modo de teste)",{position:"bottom-right"}),setTimeout(()=>{f("/dashboard/diet")},2e3)}else throw new Error("Failed to update diet protocol")}catch(i){console.error("Error updating diet protocol:",i),x.error("Erro ao atualizar protocolo de dieta",{position:"bottom-right"})}},m=()=>{f("/dashboard/diet")};return P?e.jsxs("div",{className:"min-h-screen bg-snapfit-black text-white",children:[e.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:m,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors",children:e.jsx(j,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(g,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Carregando dados do protocolo..."})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20 flex items-center justify-center min-h-[400px]",children:e.jsxs("div",{className:"text-center",children:[e.jsx(I,{className:"w-8 h-8 text-snapfit-green animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Carregando dados do protocolo..."})]})})})})]}):N?e.jsxs("div",{className:"min-h-screen bg-snapfit-black text-white",children:[e.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:m,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors",children:e.jsx(j,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(g,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Erro ao carregar protocolo"})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-red-500/10 rounded-xl p-6 border border-red-500/20 text-center",children:[e.jsxs("div",{className:"text-red-400 mb-4",children:[e.jsx(g,{className:"w-12 h-12 mx-auto mb-2"}),e.jsx("h3",{className:"text-lg font-medium",children:"Erro ao Carregar Protocolo"})]}),e.jsx("p",{className:"text-gray-400 mb-6",children:N}),e.jsx("button",{onClick:m,className:"px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:"Voltar para Dieta"})]})})})]}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black text-white",children:[e.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:m,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors",children:e.jsx(j,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Dieta"}),e.jsxs("div",{className:"flex items-center gap-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-blue-400 font-medium",children:"Modo Edição"})]})]}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400 mt-1",children:[e.jsx(g,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Modificar protocolo existente"})]})]})]})})}),e.jsx("div",{className:"p-4 pb-32",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"bg-amber-500/10 rounded-xl p-4 border border-amber-500/20 mb-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-amber-500/20 rounded-full flex items-center justify-center border border-amber-500/30 flex-shrink-0 mt-0.5",children:e.jsx(g,{className:"w-4 h-4 text-amber-500"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Editando Protocolo Existente"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"• Modifique objetivos nutricionais conforme necessário"}),e.jsx("p",{children:"• Ajuste refeições e alimentos do protocolo"}),e.jsx("p",{children:"• Atualize suplementos e horários"}),e.jsx("p",{children:"• Salve as alterações para aplicar ao protocolo ativo"})]})]})]})}),e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20",children:h?e.jsxs(e.Fragment,{children:[console.log("🔄 Renderizando DietProtocolManualCreator com dados:",h),e.jsx(S,{onSave:E,onCancel:m,initialData:h})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Carregando dados do protocolo..."})]})})]})})]})}export{A as EditDietProtocolPage,A as default};
