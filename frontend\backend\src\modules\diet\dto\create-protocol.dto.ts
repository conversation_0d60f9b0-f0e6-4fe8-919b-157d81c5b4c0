import { IsString, IsDateString, IsObject, IsArray, ValidateNested, IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { CreateMealDto } from './create-meal.dto';
import { CreateSupplementDto } from './create-supplement.dto';

export class CreateProtocolDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  dietType?: string; // 'cutting', 'bulking', 'maintenance', etc.

  @ApiProperty()
  @IsString()
  objective: string;

  @ApiProperty()
  @IsDateString()
  startDate: string;

  @ApiProperty()
  @IsObject()
  goals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    water: number;
  };

  @ApiProperty()
  @IsEnum(['weight', 'manual'])
  waterCalculationMethod: 'weight' | 'manual';

  @ApiProperty()
  @IsObject()
  weeklyMeals: {
    [key: string]: CreateMealDto[];
  };

  @ApiProperty({ type: [CreateSupplementDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSupplementDto)
  supplements: CreateSupplementDto[];

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  notes?: string;
}