import{t as Q,aR as J,aS as K,R as m,j as e,aM as V,aN as X,aO as Y,ah as T,aq as Z,P as D,aP as P,aQ as I,ad as ee,b as x,y as p}from"./index-BwF2e626.js";function te(){var S,E,_;const h=Q(),{protocolId:f}=J(),{isCoach:b}=K(),u=!!f,v=[{id:1,value_option:"Hipertrofia"},{id:2,value_option:"Força"},{id:3,value_option:"Resistência"},{id:4,value_option:"Funcional"},{id:5,value_option:"Cardio"},{id:6,value_option:"Flexibilidade"}],[q,N]=m.useState(v),[a,l]=m.useState({name:"",type:null,objective:"",startDate:new Date().toISOString().split("T")[0],frequency:4,split:"A-B-C-D",workouts:[],notes:""});m.useEffect(()=>{(async()=>{try{const t=await x.get("admin/select_options/coach_types");N(t.data)}catch{N(v)}})()},[]),m.useEffect(()=>{u&&f&&(async()=>{var t,r,n;try{let o;if(b)o=await x.get(`coach/protocols/${f}`);else if(o=await x.get("users/protocols/workouts/active"),(r=(t=o==null?void 0:o.data)==null?void 0:t.data)!=null&&r.id&&o.data.data.id.toString()!==f){p.error("Protocolo não encontrado ou não é o protocolo ativo"),h("/dashboard/workout",{replace:!0});return}const i=(n=o==null?void 0:o.data)==null?void 0:n.data;if(i){const L=(i.workouts||[]).map(y=>({name:y.name||`Treino ${y.id}`,exercises:(y.exercises||[]).map(d=>({exercise:{id:d.exercise_id||d.id,name:d.name||d.exercise_name,muscle_group:d.muscle_group,equipment:d.equipment,media_url:d.media_url},sets:d.sets||3,reps:d.reps||12,rpe:d.rpe||7,restTime:d.rest_seconds||d.restTime||90,notes:d.notes||""}))}));l({name:i.name||"",type:i.type_id||null,objective:i.objective||"",startDate:i.started_at?new Date(i.started_at).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],frequency:i.frequency||3,split:i.split||"A-B-C",workouts:L,notes:i.general_notes||""})}}catch{p.error("Erro ao carregar dados do protocolo")}})()},[u,f,b]),m.useEffect(()=>{!u&&a.workouts.length===0&&l(s=>({...s,workouts:Array(s.frequency).fill(null).map((t,r)=>({name:`Treino ${String.fromCharCode(65+r)}`,exercises:[]}))}))},[]),m.useEffect(()=>{!u&&a.frequency>0&&a.workouts.length===0&&l(s=>({...s,workouts:Array(s.frequency).fill(null).map((t,r)=>({name:`Treino ${String.fromCharCode(65+r)}`,exercises:[]}))}))},[a.frequency,u]);const[c,k]=m.useState(0),[A,j]=m.useState(!1),[C,g]=m.useState([]),[$,w]=m.useState(!1),W=()=>{l(s=>({...s,workouts:[...s.workouts,{name:`Treino ${String.fromCharCode(65+s.workouts.length)}`,exercises:[]}]}))},F=s=>{window.confirm("Tem certeza que deseja excluir este treino?")&&(l(t=>({...t,workouts:t.workouts.filter((r,n)=>n!==s)})),c>=s&&k(Math.max(0,c-1)))},R=s=>{g(t=>t.some(n=>n.id===s.id)?t.filter(n=>n.id!==s.id):[...t,s])},z=s=>{if(!s||s.length===0){p.error("Selecione pelo menos um exercício",{position:"bottom-right"});return}g(s),j(!1),w(!0)},M=s=>{l(t=>({...t,workouts:t.workouts.map((r,n)=>n===c?{...r,exercises:[...r.exercises,...s]}:r)})),g([]),w(!1)},O=s=>{s!==0&&l(t=>({...t,workouts:t.workouts.map((r,n)=>{if(n===c){const o=[...r.exercises];return[o[s-1],o[s]]=[o[s],o[s-1]],{...r,exercises:o}}return r})}))},B=s=>{const t=a.workouts[c];s!==t.exercises.length-1&&l(r=>({...r,workouts:r.workouts.map((n,o)=>{if(o===c){const i=[...n.exercises];return[i[s],i[s+1]]=[i[s+1],i[s]],{...n,exercises:i}}return n})}))},U=s=>{l(t=>({...t,workouts:t.workouts.map((r,n)=>n===c?{...r,exercises:r.exercises.filter((o,i)=>i!==s)}:r)}))},H=async s=>{if(s.preventDefault(),a.name.trim()===""){p.error("Insira um nome para o protocolo.",{position:"bottom-right"});return}if(a.type===null){p.error("Selecione um tipo de treino.",{position:"bottom-right"});return}if(a.startDate.trim()===""){p.error("Insira uma data de início para o protocolo.",{position:"bottom-right"});return}if(a.workouts.length===0||a.workouts[0].exercises.length===0){p.error("Adicione ao menos um exercício para o treino.",{position:"bottom-right"});return}try{const t={name:a.name,type_id:a.type,split:a.split,frequency:a.frequency,objective:a.objective,general_notes:a.notes,workouts:a.workouts.map(n=>({name:n.name,exercises:n.exercises.map(o=>({exercise_id:o.exercise.id,sets:o.sets,reps:o.reps.toString(),rpe:o.rpe||null,rest_seconds:o.restTime||60,notes:o.notes||null}))}))};let r;if(u&&f?(b?r=await x.put(`coach/protocols/${f}`,t):r=await x.put(`users/protocols/workout/${f}`,t),r&&p.success("Protocolo atualizado com sucesso!",{position:"bottom-right"})):(r=await x.post("users/protocols/workout",t),r&&p.success("Protocolo criado com sucesso!",{position:"bottom-right"})),r)h("/dashboard/workout",{replace:!0});else throw new Error(`Failed to ${u?"update":"create"} protocol`)}catch{p.error(`Erro ao ${u?"atualizar":"salvar"} protocolo`,{position:"bottom-right"})}},G=()=>{h("/dashboard/workout",{replace:!0})};return A?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(V,{onSelect:R,onClose:()=>{j(!1),g([])},multiSelect:!0,selectedExercises:C,onConfirm:z})}):$?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(X,{exercises:C,onConfirm:M,onCancel:()=>{w(!1),g([])}})}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:G,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(Y,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(T,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h1",{className:"text-lg font-medium text-white",children:u?"Editar Protocolo":"Criar Protocolo Manual"})]})]}),e.jsxs("button",{onClick:H,className:"flex items-center gap-1.5 px-4 py-2 text-sm font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:[e.jsx(Z,{className:"w-4 h-4"}),"Salvar"]})]})}),e.jsx("div",{className:"p-4 pb-32",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-lg font-medium text-white",children:"Informações Gerais"}),u&&e.jsxs("div",{className:"flex items-center gap-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-blue-400 font-medium",children:"Modo Edição"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Nome do Protocolo"}),e.jsx("input",{type:"text",value:a.name,onChange:s=>l(t=>({...t,name:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: Protocolo Hipertrofia"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Tipo"}),e.jsxs("select",{value:(a==null?void 0:a.type)||"",onChange:s=>l(t=>({...t,type:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white appearance-none",children:[e.jsx("option",{value:"",children:"Selecione o tipo..."}),(q||[]).map((s,t)=>e.jsx("option",{value:s.id,children:s.value_option},t))]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Divisão de Treino"}),e.jsx("input",{type:"text",value:a.split,onChange:s=>l(t=>({...t,split:s.target.value})),placeholder:"Ex: A-B-C-D",className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Data de Início"}),e.jsx("input",{type:"date",value:a.startDate,onChange:s=>l(t=>({...t,startDate:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Frequência Semanal"}),e.jsx("select",{value:a.frequency,onChange:s=>l(t=>({...t,frequency:Number(s.target.value)})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",children:[2,3,4,5,6].map(s=>e.jsxs("option",{value:s,children:[s,"x por semana"]},s))})]}),e.jsxs("div",{className:"sm:col-span-2 space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Objetivo"}),e.jsx("textarea",{value:a.objective,onChange:s=>l(t=>({...t,objective:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white h-20 resize-none",placeholder:"Descreva o objetivo principal deste protocolo..."})]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-lg font-medium text-white",children:"Treinos"}),e.jsxs("button",{onClick:W,className:"flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(D,{className:"w-4 h-4"}),"Adicionar Treino"]})]}),a.workouts.length>0&&e.jsx("div",{className:"flex gap-2 mb-4 overflow-x-auto",children:a.workouts.map((s,t)=>e.jsxs("button",{onClick:()=>k(t),className:`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg whitespace-nowrap transition-colors ${c===t?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-gray-400 hover:text-white hover:bg-snapfit-dark-gray/80"}`,children:[s.name,a.workouts.length>1&&e.jsx("button",{onClick:r=>{r.stopPropagation(),F(t)},className:"ml-1 p-0.5 text-red-400 hover:text-red-300 rounded",children:e.jsx(P,{className:"w-3 h-3"})})]},t))}),a.workouts.length>0&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Nome do Treino"}),e.jsx("input",{type:"text",value:((S=a.workouts[c])==null?void 0:S.name)||"",onChange:s=>l(t=>({...t,workouts:t.workouts.map((r,n)=>n===c?{...r,name:s.target.value}:r)})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: Treino A - Peito e Tríceps"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-md font-medium text-white",children:"Exercícios"}),e.jsxs("button",{onClick:()=>j(!0),className:"flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(D,{className:"w-4 h-4"}),"Adicionar Exercícios"]})]}),((E=a.workouts[c])==null?void 0:E.exercises.length)===0?e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(T,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),e.jsx("p",{children:"Nenhum exercício adicionado"}),e.jsx("p",{className:"text-sm",children:'Clique em "Adicionar Exercícios" para começar'})]}):e.jsx("div",{className:"space-y-2",children:(_=a.workouts[c])==null?void 0:_.exercises.map((s,t)=>e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-white",children:s.exercise.name}),e.jsxs("div",{className:"flex items-center gap-4 mt-1 text-xs text-gray-400",children:[e.jsxs("span",{children:[s.sets," séries"]}),e.jsxs("span",{children:[s.reps," repetições"]}),e.jsxs("span",{children:["RPE ",s.rpe]}),e.jsxs("span",{children:[s.restTime,"s descanso"]})]}),s.notes&&e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:s.notes})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>O(t),disabled:t===0,className:"p-1 text-gray-400 hover:text-snapfit-green disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(I,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>B(t),disabled:t===a.workouts[c].exercises.length-1,className:"p-1 text-gray-400 hover:text-snapfit-green disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(ee,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>U(t),className:"p-1 text-red-400 hover:text-red-300",children:e.jsx(P,{className:"w-4 h-4"})})]})]})},t))})]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-lg font-medium text-white mb-4",children:"Notas Adicionais"}),e.jsx("textarea",{value:a.notes,onChange:s=>l(t=>({...t,notes:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white h-24 resize-none",placeholder:"Adicione observações, instruções especiais ou comentários sobre o protocolo..."})]})]})})]})}export{te as CreateWorkoutProtocolManualPage};
