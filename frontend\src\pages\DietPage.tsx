import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Apple, Carrot, Egg, Fish, Plus, UtensilsCrossed } from 'lucide-react';
import { apiService } from '../services/api';
import { toast } from 'react-toastify';
import { useActiveDietProtocol, useRemoveDietProtocol, resetMockProtocolDeletion } from '../hooks/useDietProtocol';
import { useNutritionalSummary } from '../hooks/useDiary';
import { useCheckActiveProtocol } from '../hooks/useProtocolManagement';
import { useQuery } from '@tanstack/react-query';
import { ModernChart } from '../components/ModernChart';
import { PieChart } from '../components/PieChart';
import { StatCard } from '../components/StatCard';
import { CircularProgress } from '../components/CircularProgress';
import { ImprovedCalorieDistribution } from '../components/ImprovedCalorieDistribution';
import { DietHistory } from '../components/DietHistory';
import { DietAIGenerator } from '../components/DietAIGenerator';
import { DietProtocolManualCreator } from '../components/DietProtocolManualCreator';
import { FavoriteRecipes } from '../components/FavoriteRecipes';
import { DietProtocolOptions } from '../components/DietProtocolOptions';
import ProtocolWeightEstimation from '../components/ProtocolWeightEstimation';
import MicronutrientsModal from '../components/MicronutrientsModal';
import DietSectionSelector from '../components/DietSectionSelector';
import AddMealModal from '../components/AddMealModal';
import { ConfirmationDialog } from '../components/ConfirmationDialog';

export function DietPage() {
  const navigate = useNavigate();
  const [selectedDay, setSelectedDay] = React.useState<string>('monday');

  // Use hooks for protocol management
  const { data: currentProtocol, isLoading: loadingProtocol } = useActiveDietProtocol();
  const removeProtocolMutation = useRemoveDietProtocol();
  const checkActiveProtocol = useCheckActiveProtocol();

  // Get nutritional summary for today to show current consumption
  const today = new Date().toISOString().split('T')[0];
  const { data: nutritionalSummary } = useNutritionalSummary(today, today);

  // Função para converter dia da semana para data específica
  const getDateForDay = (dayName: string) => {
    const today = new Date();
    const currentDay = today.getDay(); // 0 = domingo, 1 = segunda, etc.

    const dayMap: { [key: string]: number } = {
      'sunday': 0,
      'monday': 1,
      'tuesday': 2,
      'wednesday': 3,
      'thursday': 4,
      'friday': 5,
      'saturday': 6
    };

    const targetDay = dayMap[dayName];
    const diff = targetDay - currentDay;

    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + diff);

    return targetDate.toISOString().split('T')[0];
  };

  // Buscar refeições do dia específico selecionado
  const selectedDate = getDateForDay(selectedDay);
  const {
    data: dailyMealsResponse,
    isLoading: isLoadingDailyMeals,
    error: dailyMealsError
  } = useQuery({
    queryKey: ['diet-page', 'meals', selectedDate],
    queryFn: async () => {
      console.log('🔄 DietPage: Fetching meals for date:', selectedDate);
      console.log('📅 DietPage: Selected day:', selectedDay);
      console.log('🔑 DietPage: Access token:', localStorage.getItem('accessToken') ? 'Present' : 'Missing');

      try {
        const response: any = await apiService.get('users/protocols/diet/meals/active',
          { searchParams: { date: `${selectedDate} 00:00:00` } }
        );
        console.log('🍽️ DietPage: Full response:', response);
        console.log('📋 DietPage: Has protocol:', response?.data?.has_protocol);
        console.log('🥘 DietPage: Meals count:', response?.data?.meals?.length || 0);

        if (!response?.data?.has_protocol) {
          console.warn('⚠️ DietPage: User does not have an active diet protocol');
        }

        return response?.data || { has_protocol: false, meals: [] };
      } catch (error) {
        console.error('❌ DietPage: API error:', error);
        return { has_protocol: false, meals: [] };
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Nutrition goals data - usar dados reais do protocolo ativo e consumo atual
  const nutritionGoals = React.useMemo(() => {
    const protocolGoals = currentProtocol?.nutritional_goals;
    const currentConsumption = nutritionalSummary;

    // CORREÇÃO: Verificar múltiplas estruturas possíveis dos dados
    const getProtocolValue = (field: string, defaultValue: number) => {
      if (protocolGoals) {
        // Tentar diferentes estruturas possíveis
        const value = protocolGoals[field] ||
                     protocolGoals[field + '_target'] ||
                     protocolGoals[field + 's'] ||
                     protocolGoals['target_' + field] ||
                     defaultValue;
        // Garantir que sempre retorna um número
        return typeof value === 'number' ? value : defaultValue;
      }
      return defaultValue;
    };

    const getCurrentValue = (field: string) => {
      const value = currentConsumption?.[field]?.current ||
                   currentConsumption?.[field] ||
                   0;
      // Garantir que sempre retorna um número
      return typeof value === 'number' ? value : 0;
    };

    if (protocolGoals) {
      return {
        protein: {
          current: getCurrentValue('protein'),
          target: getProtocolValue('protein', 150)
        },
        carbs: {
          current: getCurrentValue('carbs'),
          target: getProtocolValue('carbs', 200)
        },
        fat: {
          current: getCurrentValue('fat'),
          target: getProtocolValue('fat', 70)
        },
        calories: {
          current: getCurrentValue('calories'),
          target: getProtocolValue('calories', 2000)
        }
      };
    }

    // Fallback para dados padrão quando não há protocolo
    return {
      protein: { current: getCurrentValue('protein'), target: 150 },
      carbs: { current: getCurrentValue('carbs'), target: 200 },
      fat: { current: getCurrentValue('fat'), target: 70 },
      calories: { current: getCurrentValue('calories'), target: 2000 }
    };
  }, [currentProtocol, nutritionalSummary]);

  // Data for macronutrient distribution pie chart
  const macroDistributionData = [
    { value: (nutritionGoals.protein?.current || 0) * 4, label: 'Proteína', color: '#B9FF43' }, // 4 calories per gram
    { value: (nutritionGoals.carbs?.current || 0) * 4, label: 'Carboidratos', color: '#4CAF50' }, // 4 calories per gram
    { value: (nutritionGoals.fat?.current || 0) * 9, label: 'Gorduras', color: '#FFC107' } // 9 calories per gram
  ];

  // Data for calorie breakdown chart
  const calorieBreakdownData = [
    { value: (nutritionGoals.protein?.current || 0) * 4, label: `Proteína (${Math.round((nutritionGoals.protein?.current || 0) * 4)} kcal)`, color: '#B9FF43' },
    { value: (nutritionGoals.carbs?.current || 0) * 4, label: `Carboidratos (${Math.round((nutritionGoals.carbs?.current || 0) * 4)} kcal)`, color: '#4CAF50' },
    { value: (nutritionGoals.fat?.current || 0) * 9, label: `Gorduras (${Math.round((nutritionGoals.fat?.current || 0) * 9)} kcal)`, color: '#FFC107' }
  ];

  // Dados de estatísticas do protocolo de dieta
  const dietProtocolStats = {
    startDate: '2023-10-15',
    duration: '8 semanas',
    mealsCompleted: 42,
    totalMeals: 56,
    adherenceRate: 75, // porcentagem
    weeklyAdherence: [85, 70, 90, 65, 80, 75, 60, 75], // porcentagem por semana
    waterIntake: {
      average: 2.2, // litros
      goal: 3.0 // litros
    },
    calorieDeficit: {
      daily: 350, // kcal
      weekly: 2450 // kcal
    },
    weightLoss: {
      total: 3.2, // kg
      weekly: [0.5, 0.7, 0.4, 0.3, 0.5, 0.3, 0.2, 0.3] // kg por semana
    }
  };

  // Dados para o gráfico de adesão semanal
  const weeklyAdherenceData = dietProtocolStats.weeklyAdherence.map((value, index) => ({
    value,
    label: `Semana ${index + 1}`,
    color: value >= 80 ? '#4CAF50' : value >= 60 ? '#FFC107' : '#FF5722'
  }));

  // Dados para o gráfico de perda de peso
  const weightLossData = dietProtocolStats.weightLoss.weekly.map((value, index) => ({
    value,
    label: `Semana ${index + 1}`,
    color: '#B9FF43'
  }));

  // Sample meal data
  const meals = {
    monday: [
      {
        id: 'meal-1',
        name: 'Café da Manhã',
        time: '07:30',
        foods: [
          { id: 'food-1', name: 'Ovos', quantity: 3, unit: 'unidades', calories: 210, protein: 18, carbs: 0, fat: 15 },
          { id: 'food-2', name: 'Aveia', quantity: 40, unit: 'g', calories: 150, protein: 5, carbs: 27, fat: 3 },
          { id: 'food-3', name: 'Banana', quantity: 1, unit: 'unidade', calories: 105, protein: 1, carbs: 27, fat: 0 }
        ]
      },
      {
        id: 'meal-2',
        name: 'Almoço',
        time: '12:00',
        foods: [
          { id: 'food-4', name: 'Frango', quantity: 150, unit: 'g', calories: 165, protein: 31, carbs: 0, fat: 3.5 },
          { id: 'food-5', name: 'Arroz', quantity: 100, unit: 'g', calories: 130, protein: 2.7, carbs: 28, fat: 0.3 },
          { id: 'food-6', name: 'Brócolis', quantity: 100, unit: 'g', calories: 55, protein: 3.7, carbs: 11, fat: 0.6 }
        ]
      },
      {
        id: 'meal-3',
        name: 'Lanche',
        time: '16:00',
        foods: [
          { id: 'food-7', name: 'Whey Protein', quantity: 30, unit: 'g', calories: 120, protein: 24, carbs: 3, fat: 2 },
          { id: 'food-8', name: 'Maçã', quantity: 1, unit: 'unidade', calories: 95, protein: 0.5, carbs: 25, fat: 0.3 }
        ]
      }
    ],
    tuesday: [
      {
        id: 'meal-5',
        name: 'Café da Manhã',
        time: '07:30',
        foods: [
          { id: 'food-12', name: 'Iogurte Grego', quantity: 200, unit: 'g', calories: 130, protein: 22, carbs: 8, fat: 0 },
          { id: 'food-13', name: 'Granola', quantity: 30, unit: 'g', calories: 120, protein: 3, carbs: 20, fat: 3 },
          { id: 'food-14', name: 'Morangos', quantity: 100, unit: 'g', calories: 32, protein: 0.7, carbs: 7.7, fat: 0.3 }
        ]
      }
    ],
    wednesday: [
      {
        id: 'meal-9',
        name: 'Café da Manhã',
        time: '07:30',
        foods: [
          { id: 'food-20', name: 'Panquecas Proteicas', quantity: 2, unit: 'unidades', calories: 250, protein: 20, carbs: 25, fat: 8 }
        ]
      }
    ],
    thursday: [
      {
        id: 'meal-13',
        name: 'Café da Manhã',
        time: '07:30',
        foods: [
          { id: 'food-25', name: 'Omelete', quantity: 1, unit: 'porção', calories: 220, protein: 18, carbs: 5, fat: 16 }
        ]
      }
    ],
    friday: [
      {
        id: 'meal-17',
        name: 'Café da Manhã',
        time: '07:30',
        foods: [
          { id: 'food-30', name: 'Smoothie Proteico', quantity: 1, unit: 'copo', calories: 280, protein: 25, carbs: 30, fat: 8 }
        ]
      }
    ],
    saturday: [
      {
        id: 'meal-21',
        name: 'Café da Manhã',
        time: '08:30',
        foods: [
          { id: 'food-35', name: 'Pão Integral', quantity: 2, unit: 'fatias', calories: 160, protein: 8, carbs: 30, fat: 2 }
        ]
      }
    ],
    sunday: [
      {
        id: 'meal-25',
        name: 'Café da Manhã',
        time: '09:00',
        foods: [
          { id: 'food-40', name: 'Tapioca', quantity: 1, unit: 'unidade', calories: 130, protein: 3, carbs: 28, fat: 0.5 }
        ]
      }
    ]
  };

  // State for showing AI protocol reader and protocol options
  const [showProtocolReader, setShowProtocolReader] = React.useState(false);
  const [showProtocolOptions, setShowProtocolOptions] = React.useState(false);
  const [showManualCreator, setShowManualCreator] = React.useState(false);
  const [protocolMode, setProtocolMode] = React.useState<'form' | 'upload' | null>(null);
  const [showMicronutrientsModal, setShowMicronutrientsModal] = React.useState(false);
  const [micronutrientsInitialTab, setMicronutrientsInitialTab] = React.useState<'analysis' | 'bloodtests' | 'supplements' | 'evolution'>('analysis');
  const [showAddMealModal, setShowAddMealModal] = React.useState(false);

  // State for active protocol confirmation dialog
  const [showActiveProtocolConfirmation, setShowActiveProtocolConfirmation] = React.useState(false);
  const [pendingProtocolAction, setPendingProtocolAction] = React.useState<(() => void) | null>(null);

  // Function to check for active protocol before showing options (similar to WorkoutPage)
  const handleShowProtocolOptions = async () => {
    try {
      console.log('🔍 Checking for active diet protocol before showing options...');
      const activeProtocol = await checkActiveProtocol.mutateAsync('diet');

      if (activeProtocol && activeProtocol.id) {
        console.log('⚠️ Active diet protocol found, showing confirmation dialog');
        setPendingProtocolAction(() => () => setShowProtocolOptions(true));
        setShowActiveProtocolConfirmation(true);
      } else {
        console.log('✅ No active diet protocol found, proceeding directly');
        setShowProtocolOptions(true);
      }
    } catch (error) {
      console.log('ℹ️ No active diet protocol found (error), proceeding directly');
      setShowProtocolOptions(true);
    }
  };



  // Handle confirmation to proceed with protocol creation (without premature finalization)
  const handleConfirmFinalizeAndCreate = async () => {
    try {
      console.log('✅ User confirmed diet protocol creation, proceeding without premature finalization');

      // Now proceed with the pending action (show protocol options)
      if (pendingProtocolAction) {
        pendingProtocolAction();
        setPendingProtocolAction(null);
      }
      setShowActiveProtocolConfirmation(false);
    } catch (error) {
      console.error('Error in confirmation action:', error);
      setShowActiveProtocolConfirmation(false);
      setPendingProtocolAction(null);
    }
  };

  // Debug log para verificar o protocolo carregado
  React.useEffect(() => {
    console.log('🔍 DietPage - currentProtocol atualizado:', currentProtocol);
    console.log('🔍 DietPage - loadingProtocol:', loadingProtocol);
    console.log('🍽️ DietPage - currentProtocol.meals:', currentProtocol?.meals);
    console.log('🎯 DietPage - nutritional_goals:', currentProtocol?.nutritional_goals);
    console.log('📊 DietPage - nutritionalSummary:', nutritionalSummary);
    console.log('🥗 DietPage - nutritionGoals calculado - type:', typeof nutritionGoals);
    console.log('📅 DietPage - selectedDay:', selectedDay);
    console.log('🥘 DietPage - meals for selected day:', currentProtocol?.meals?.[selectedDay]);

    // Add development helper to window object
    if (process.env.NODE_ENV === 'development') {
      (window as any).resetMockProtocolDeletion = resetMockProtocolDeletion;
    }
  }, [currentProtocol, loadingProtocol, nutritionalSummary, nutritionGoals]);

  // Debug log para selectedDay
  React.useEffect(() => {
    console.log('📅 DietPage - selectedDay changed to:', selectedDay);
    console.log('🥘 DietPage - meals for new selected day:', currentProtocol?.meals?.[selectedDay]);
  }, [selectedDay, currentProtocol]);

  // Handle protocol deletion
  const handleRemoveProtocol = async () => {
    const confirm = window.confirm('Tem certeza que deseja remover este protocolo?');
    if (!confirm) return;

    console.log('🔍 Debug - currentProtocol:', currentProtocol);
    console.log('🔍 Debug - currentProtocol?.id:', currentProtocol?.id);
    console.log('🔍 Debug - typeof currentProtocol?.id:', typeof currentProtocol?.id);

    if (!currentProtocol?.id) {
      console.error('❌ ID do protocolo não encontrado:', currentProtocol);
      toast.error('Erro: ID do protocolo não encontrado');
      return;
    }

    try {
      console.log('🗑️ Removendo protocolo com ID:', currentProtocol.id);
      await removeProtocolMutation.mutateAsync(currentProtocol.id);
      // No need to reload the page, the query will automatically update
      console.log('✅ Protocolo removido com sucesso, UI será atualizada automaticamente');
    } catch (error) {
      console.error('Erro ao remover protocolo:', error);
      toast.error('Erro ao remover protocolo. Tente novamente.');
    }
  };



  // Render the component
  return (
    <div className="space-y-6">
      <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">Dieta</h1>

      {showProtocolOptions && (
        <DietProtocolOptions
          onGenerateAI={() => {
            setShowProtocolOptions(false);
            // Navigate directly since active protocol check was already done
            navigate('/dashboard/diet/create-protocol/ai');
          }}
          onCreateManual={() => {
            setShowProtocolOptions(false);
            // Navigate directly since active protocol check was already done
            navigate('/dashboard/diet/create-protocol/manual');
          }}
          onImportNutritionist={() => {
            setShowProtocolOptions(false);
            // Navigate directly since active protocol check was already done
            navigate('/dashboard/diet/create-protocol/import');
          }}
          onReadProtocol={() => {
            setShowProtocolOptions(false);
            // Navigate directly since active protocol check was already done
            navigate('/dashboard/diet/create-protocol/import');
          }}
          onClose={() => setShowProtocolOptions(false)}
        />
      )}

      {/* Show no protocol state if protocol is null */}
      {!currentProtocol && !loadingProtocol && (
        <div className="card p-6">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <UtensilsCrossed className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
              Nenhum protocolo ativo
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Você não possui um protocolo de dieta ativo. Crie um novo protocolo para começar.
            </p>
            <button
              className="btn-primary flex items-center justify-center gap-2 mx-auto"
              onClick={handleShowProtocolOptions}
            >
              <Plus className="w-4 h-4" />
              Criar Protocolo
            </button>
          </div>
        </div>
      )}

      {/* Show protocol content if protocol exists */}
      {currentProtocol && (
        <React.Fragment>
        <div className="card p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-white">
                {currentProtocol?.name || 'Protocolo de Cutting'}
              </h2>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30">
              <span className="text-xs sm:text-sm font-medium text-snapfit-green">
                Cutting
              </span>
            </div>
            {currentProtocol?.lastUpdated && (
              <div className="flex items-center gap-1 px-2 py-1 bg-blue-500/20 rounded-lg border border-blue-500/30">
                <span className="text-xs font-medium text-blue-400">
                  Editado
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                if (currentProtocol?.id) {
                  console.log('🔧 Navegando para edição do protocolo:', currentProtocol.id);
                  navigate(`/dashboard/diet/edit-protocol/${currentProtocol.id}`);
                } else {
                  console.error('❌ Nenhum protocolo ativo para editar');
                  toast.error('Nenhum protocolo ativo para editar');
                }
              }}
              disabled={!currentProtocol}
              className="flex items-center gap-1.5 px-3 py-2 text-sm font-medium text-snapfit-green bg-snapfit-green/10 border border-snapfit-green/20 rounded-lg hover:bg-snapfit-green/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <UtensilsCrossed className="w-4 h-4" />
              Editar Protocolo
            </button>
          </div>
          <div className="flex gap-2">
            <button
              className="btn-primary flex items-center justify-center gap-2 text-sm"
              onClick={handleShowProtocolOptions}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 5v14"></path>
                <path d="M5 12h14"></path>
              </svg>
              Criar Protocolo
            </button>
          </div>
        </div>

        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Perda de gordura mantendo massa muscular
        </p>

        <div className="bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10 mb-6">
          <h3 className="font-bold text-white mb-3">Metas Nutricionais do Protocolo</h3>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div>
              <div className="text-xs text-gray-400">Calorias</div>
              <div className="text-base font-bold text-snapfit-green">{nutritionGoals.calories?.target || 0} kcal</div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Proteínas</div>
              <div className="text-base font-bold text-snapfit-green">
                {nutritionGoals.protein?.target || 0}g ({Math.round(((nutritionGoals.protein?.target || 0) * 4 / (nutritionGoals.calories?.target || 1)) * 100)}%)
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Carboidratos</div>
              <div className="text-base font-bold text-snapfit-green">
                {nutritionGoals.carbs?.target || 0}g ({Math.round(((nutritionGoals.carbs?.target || 0) * 4 / (nutritionGoals.calories?.target || 1)) * 100)}%)
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-400">Gorduras</div>
              <div className="text-base font-bold text-snapfit-green">
                {nutritionGoals.fat?.target || 0}g ({Math.round(((nutritionGoals.fat?.target || 0) * 9 / (nutritionGoals.calories?.target || 1)) * 100)}%)
              </div>
            </div>
          </div>
        </div>

        {/* Estimativa de Perda/Ganho de Peso */}
        <div className="mb-6">
          <ProtocolWeightEstimation
            protocolGoals={{
              calories: nutritionGoals.calories?.target || 0,
              protein: nutritionGoals.protein?.target || 0,
              carbs: nutritionGoals.carbs?.target || 0,
              fat: nutritionGoals.fat?.target || 0,
              water: 2500
            }}
          />
        </div>



        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-white">
            Refeições
          </h2>
          <button
            className="btn-primary flex items-center justify-center gap-2"
            onClick={() => setShowAddMealModal(true)}
          >
            <Plus className="w-4 h-4" />
            Nova Refeição
          </button>
        </div>

        <div className="flex overflow-x-auto gap-2 pb-2 mb-4 hide-scrollbar">
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'monday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('monday')}
          >
            Seg
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'tuesday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('tuesday')}
          >
            Ter
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'wednesday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('wednesday')}
          >
            Qua
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'thursday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('thursday')}
          >
            Qui
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'friday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('friday')}
          >
            Sex
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'saturday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('saturday')}
          >
            Sáb
          </button>
          <button
            className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${selectedDay === 'sunday' ? 'bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30' : 'bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30'}`}
            onClick={() => setSelectedDay('sunday')}
          >
            Dom
          </button>
        </div>

        <div className="space-y-4">
          {/* CORREÇÃO: Usar dados reais da API em vez de dados do protocolo template */}
          {(() => {
            // Priorizar dados da API (refeições do dia específico)
            const apiMeals = dailyMealsResponse?.meals || [];
            const fallbackMeals = meals[selectedDay as keyof typeof meals];
            const mealsToShow = apiMeals.length > 0 ? apiMeals : fallbackMeals || [];

            console.log('🍽️ DietPage - Meals to show:', {
              selectedDay,
              selectedDate,
              apiMealsCount: apiMeals?.length || 0,
              fallbackMealsCount: fallbackMeals?.length || 0,
              usingAPI: apiMeals.length > 0,
              mealsToShow: mealsToShow.length,
              hasProtocol: dailyMealsResponse?.has_protocol
            });

            return mealsToShow;
          })()?.map((meal) => {
            // CORREÇÃO: Usar dados nutricionais da API quando disponíveis
            const foods = meal.foods || [];

            // Se a refeição tem dados nutricionais calculados da API, usar esses
            const hasApiNutrients = meal.nutrients && (
              meal.nutrients.calories > 0 ||
              meal.nutrients.protein > 0 ||
              meal.nutrients.carbs > 0 ||
              meal.nutrients.fat > 0
            );

            let totalCalories, totalProtein, totalCarbs, totalFat;

            if (hasApiNutrients) {
              // Usar dados nutricionais da API (já calculados)
              totalCalories = Math.round(meal.nutrients.calories || 0);
              totalProtein = Math.round((meal.nutrients.protein || 0) * 10) / 10;
              totalCarbs = Math.round((meal.nutrients.carbs || 0) * 10) / 10;
              totalFat = Math.round((meal.nutrients.fat || 0) * 10) / 10;
            } else {
              // Fallback: calcular manualmente dos alimentos (dados mock)
              totalCalories = foods.reduce((sum, food) => {
                const calories = food.calories || food.kcal || 0;
                return sum + (typeof calories === 'number' ? calories : 0);
              }, 0);

              totalProtein = foods.reduce((sum, food) => {
                const protein = food.protein || food.proteins || 0;
                return sum + (typeof protein === 'number' ? protein : 0);
              }, 0);

              totalCarbs = foods.reduce((sum, food) => {
                const carbs = food.carbs || food.carbohydrates || food.carb || 0;
                return sum + (typeof carbs === 'number' ? carbs : 0);
              }, 0);

              totalFat = foods.reduce((sum, food) => {
                const fat = food.fat || food.fats || food.lipids || 0;
                return sum + (typeof fat === 'number' ? fat : 0);
              }, 0);
            }

            return (
              <div key={meal.id} className="bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-bold text-white">{meal.name}</h3>
                  <span className="text-sm text-gray-400">{meal.meal_time || meal.time}</span>
                </div>

                <div className="space-y-2">
                  {foods.map((food, foodIndex) => {
                    // Para dados da API, os valores nutricionais podem não estar nos alimentos individuais
                    // mas sim calculados no total da refeição
                    const quantity = food.quantity || food.amount || 0;
                    const unit = food.unit || food.measure || 'g';

                    return (
                      <div key={food.id || foodIndex} className="flex justify-between items-center py-2 border-b border-snapfit-gray/20 last:border-0">
                        <div>
                          <div className="text-white">{food.name || 'Alimento'}</div>
                          <div className="text-xs text-gray-400">{quantity} {unit}</div>
                        </div>
                        {/* Para dados da API, não mostrar valores individuais dos alimentos pois podem estar zerados */}
                        {!hasApiNutrients && (
                          <div className="text-right">
                            <div className="text-snapfit-green">{Math.round(food.calories || food.kcal || 0)} kcal</div>
                            <div className="text-xs text-gray-400">
                              P: {Math.round((food.protein || food.proteins || 0) * 10) / 10}g • C: {Math.round((food.carbs || food.carbohydrates || food.carb || 0) * 10) / 10}g • G: {Math.round((food.fat || food.fats || food.lipids || 0) * 10) / 10}g
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>

                {/* Totais da refeição */}
                <div className="mt-4 pt-3 border-t border-snapfit-gray/20">
                  <div className="flex justify-between items-center">
                    <div className="text-sm font-medium text-white">Total da Refeição</div>
                    <div className="text-right">
                      <div className="text-snapfit-green font-bold">{totalCalories} kcal</div>
                      <div className="text-xs text-gray-400">
                        P: {totalProtein.toFixed(1)}g • C: {totalCarbs.toFixed(1)}g • G: {totalFat.toFixed(1)}g
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="card p-6">
        <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4">
          Estatísticas do Protocolo
        </h2>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
            <div className="text-xs text-gray-500 dark:text-gray-400">Início</div>
            <div className="text-base font-bold text-gray-800 dark:text-white">{new Date(dietProtocolStats.startDate).toLocaleDateString('pt-BR')}</div>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
            <div className="text-xs text-gray-500 dark:text-gray-400">Duração</div>
            <div className="text-base font-bold text-gray-800 dark:text-white">{dietProtocolStats.duration}</div>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
            <div className="text-xs text-gray-500 dark:text-gray-400">Refeições Realizadas</div>
            <div className="text-base font-bold text-gray-800 dark:text-white">{dietProtocolStats.mealsCompleted}/{dietProtocolStats.totalMeals}</div>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
            <div className="text-xs text-gray-500 dark:text-gray-400">Adesão</div>
            <div className="text-base font-bold text-gray-800 dark:text-white">{dietProtocolStats.adherenceRate}%</div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="card-glass p-4">
            <h3 className="text-base font-bold mb-3">Adesão ao Protocolo</h3>
            <div className="space-y-4">
              {/* Adesão Geral */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium">Adesão Geral</span>
                  <span>{dietProtocolStats.adherenceRate}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                  <div className={`h-2.5 rounded-full ${
                    dietProtocolStats.adherenceRate >= 80 ? 'bg-snapfit-green' :
                    dietProtocolStats.adherenceRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`} style={{ width: `${dietProtocolStats.adherenceRate}%` }}></div>
                </div>
              </div>

              {/* Refeições Completadas */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium">Refeições Completadas</span>
                  <span>{dietProtocolStats.mealsCompleted}/{dietProtocolStats.totalMeals}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                  <div className="bg-snapfit-green h-2.5 rounded-full"
                    style={{ width: `${(dietProtocolStats.mealsCompleted / dietProtocolStats.totalMeals) * 100}%` }}></div>
                </div>
              </div>

              {/* Consumo de Água */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium">Consumo de Água</span>
                  <span>{dietProtocolStats.waterIntake.average}L / {dietProtocolStats.waterIntake.goal}L</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                  <div className="bg-blue-500 h-2.5 rounded-full"
                    style={{ width: `${(dietProtocolStats.waterIntake.average / dietProtocolStats.waterIntake.goal) * 100}%` }}></div>
                </div>
              </div>

              {/* Déficit Calórico */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium">Déficit Calórico</span>
                  <span>{dietProtocolStats.calorieDeficit.daily} kcal/dia</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                  <div className="bg-orange-500 h-2.5 rounded-full"
                    style={{ width: `${Math.min((dietProtocolStats.calorieDeficit.daily / 500) * 100, 100)}%` }}></div>
                </div>
              </div>
            </div>
          </div>

          <div className="card-glass p-4">
            <h3 className="text-base font-bold mb-3">Progresso</h3>
            <div className="flex flex-col items-center justify-center h-full">
              <div className="mb-4">
                <CircularProgress
                  value={dietProtocolStats.weightLoss.total}
                  max={5}
                  label="Perda de Peso"
                  sublabel={`${dietProtocolStats.weightLoss.total}kg`}
                  color="#B9FF43"
                  size={120}
                />
              </div>
              <div className="text-center mt-2">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Meta: 5kg em {dietProtocolStats.duration}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Progresso: {Math.round((dietProtocolStats.weightLoss.total / 5) * 100)}% concluído
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Improved Calorie Distribution Chart */}
        <ImprovedCalorieDistribution
          nutritionGoals={nutritionGoals}
          className="mt-6"
        />
      </div>

      <div className="card p-6">
        <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4">
          Suplementos
        </h2>

        <div className="space-y-3">
          <div className="bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10">
            <h3 className="font-bold text-white mb-1">Whey Protein</h3>
            <div className="text-sm text-gray-400">30g • Após treino</div>
            <div className="text-xs text-gray-500 mt-2">Tomar imediatamente após o treino</div>
          </div>

          <div className="bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10">
            <h3 className="font-bold text-white mb-1">Creatina</h3>
            <div className="text-sm text-gray-400">5g • Diariamente</div>
            <div className="text-xs text-gray-500 mt-2">Tomar em qualquer momento do dia</div>
          </div>

          <div className="bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10">
            <h3 className="font-bold text-white mb-1">Multivitamínico</h3>
            <div className="text-sm text-gray-400">1 cápsula • Diariamente</div>
            <div className="text-xs text-gray-500 mt-2">Tomar com o café da manhã</div>
          </div>
        </div>
      </div>

      <div className="card p-6">
        <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4">
          Observações Gerais
        </h2>

        <p className="text-gray-600 dark:text-gray-300">
          Manter boa hidratação durante o dia. Evitar alimentos processados e com açúcar adicionado. Priorizar proteínas magras e carboidratos complexos.
        </p>
      </div>

      <div className="flex justify-end">
        <button
          className="flex justify-between items-center gap-1 text-sm mb-4 text-slate-500 hover:text-red-500 transition-colors disabled:opacity-50"
          onClick={handleRemoveProtocol}
          disabled={removeProtocolMutation.isPending || !currentProtocol}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
          <span>Remover Protocolo</span>
        </button>
      </div>
        </React.Fragment>
      )}

      {showProtocolReader && (
        <DietAIGenerator
          initialMode={protocolMode}
          onProtocolGenerated={(protocol) => {
            console.log('Protocolo de dieta gerado pela IA:', protocol);
            alert(`Protocolo de dieta "${protocol.name}" gerado com sucesso!`);
            setShowProtocolReader(false);
            setProtocolMode(null);
          }}
          onClose={() => {
            setShowProtocolReader(false);
            setProtocolMode(null);
          }}
        />
      )}

      {showManualCreator && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-snapfit-gray rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20">
            <DietProtocolManualCreator
              onSave={(protocol) => {
                console.log('Protocolo manual criado:', protocol);
                alert(`Protocolo "${protocol.name}" criado com sucesso!`);
                setShowManualCreator(false);
              }}
              onCancel={() => setShowManualCreator(false)}
            />
          </div>
        </div>
      )}

      {/* Seções Organizadas */}
      <DietSectionSelector
        onOpenMicronutrientsModal={(tab) => {
          setMicronutrientsInitialTab(tab);
          setShowMicronutrientsModal(true);
        }}
        onReuseProtocol={async (protocol) => {
          if (protocol.edit) {
            // Navegar para a página de edição
            console.log('Editando protocolo:', protocol);

            // First, try to get the active protocol ID
            try {
              const activeProtocol: any = await apiService.get('users/protocols/diet/active');
              // Fix: apiService.get() returns JSON directly, not wrapped in response.data
              if (activeProtocol?.data?.id) {
                navigate(`/dashboard/diet/edit-protocol/${activeProtocol.data.id}`);
              } else {
                // Fallback: use mock ID for testing
                navigate('/dashboard/diet/edit-protocol/mock-protocol-id-123');
              }
            } catch (error) {
              console.error('Error getting active protocol:', error);
              // Fallback: use mock ID for testing
              navigate('/dashboard/diet/edit-protocol/mock-protocol-id-123');
            }
          } else {
            // Usar o protocolo diretamente
            console.log('Reutilizando protocolo:', protocol);
            try {
              // Here you would implement the logic to set this protocol as active
              // For now, just show success message
              alert(`Protocolo ${protocol.name} reutilizado com sucesso!`);
            } catch (error) {
              console.error('Error reusing protocol:', error);
              alert('Erro ao reutilizar protocolo');
            }
          }
        }}
      />

      {/* Modal de Micronutrientes */}
      <MicronutrientsModal
        isOpen={showMicronutrientsModal}
        onClose={() => setShowMicronutrientsModal(false)}
        initialTab={micronutrientsInitialTab}
      />

      {/* Modal de Nova Refeição */}
      <AddMealModal
        isOpen={showAddMealModal}
        onClose={() => setShowAddMealModal(false)}
        selectedDate={new Date().toISOString().split('T')[0]}
      />

      {/* Confirmation Dialog for Active Protocol */}
      <ConfirmationDialog
        isOpen={showActiveProtocolConfirmation}
        onClose={() => {
          setShowActiveProtocolConfirmation(false);
          setPendingProtocolAction(null);
        }}
        onConfirm={handleConfirmFinalizeAndCreate}
        title="Protocolo Ativo Encontrado"
        message="Ao criar um novo, o protocolo atual será finalizado. Deseja continuar?"
        confirmText="Sim, Continuar"
        cancelText="Cancelar"
        type="warning"
        isLoading={checkActiveProtocol.isPending}
      />
    </div>
  );
}
