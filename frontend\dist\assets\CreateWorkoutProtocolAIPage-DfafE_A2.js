import{t as v,r as w,j as e,aD as N,aO as y,B as g,aT as k,aU as I,b as A,y as a}from"./index-PNQkLtwW.js";import{w as P}from"./workoutService-CC7qnO2S.js";function S(){const o=v(),[h,t]=w.useState(!1),f=async b=>{var l,d,p,m,x;t(!0);try{const s=await A.post("users/protocols/workout/ai",b,{timeout:6e5});if((p=(d=(l=s==null?void 0:s.data)==null?void 0:l.data)==null?void 0:d.protocol)!=null&&p.id){const r=s.data.data.protocol.id;a.success("Protocolo gerado com sucesso pela IA!",{position:"bottom-right"});let n=0;const u=15,i=setInterval(async()=>{n++;try{await P.getProtocolById(r)?(clearInterval(i),o("/dashboard/workout")):n>=u&&(clearInterval(i),a.success("Protocolo gerado com sucesso! Aguarde alguns segundos para aparecer na lista.",{position:"bottom-right",autoClose:5e3}),t(!1),o("/dashboard/workout"))}catch{n>=u&&(clearInterval(i),a.success("Protocolo gerado! Verifique na lista de protocolos.",{position:"bottom-right",autoClose:4e3}),o("/dashboard/workout"))}},4e3)}else throw new Error("Falha na geração do protocolo")}catch(s){const r=((x=(m=s==null?void 0:s.response)==null?void 0:m.data)==null?void 0:x.message)||(s==null?void 0:s.message)||"Erro ao gerar protocolo com IA";a.error(r,{position:"bottom-right"})}finally{t(!1)}},c=()=>{o("/dashboard/workout")};return h?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(N,{type:"workout",message:"Gerando seu protocolo de treino personalizado..."})}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-between p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:c,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(y,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(g,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-medium text-white",children:"Gerar Protocolo com IA"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(k,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Protocolo personalizado baseado em seus dados"})]})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsx(I,{onGenerate:f,onCancel:c}),e.jsx("div",{className:"bg-gradient-to-r from-snapfit-green/10 to-blue-500/10 rounded-xl p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5",children:e.jsx(g,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Como funciona a IA do SnapFit"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"• Analisa seus dados corporais e objetivos"}),e.jsx("p",{children:"• Considera seu nível de experiência e equipamentos disponíveis"}),e.jsx("p",{children:"• Gera um protocolo personalizado com exercícios específicos"}),e.jsx("p",{children:"• Ajusta séries, repetições e intensidade automaticamente"})]})]})]})})]})})]})}export{S as CreateWorkoutProtocolAIPage};
