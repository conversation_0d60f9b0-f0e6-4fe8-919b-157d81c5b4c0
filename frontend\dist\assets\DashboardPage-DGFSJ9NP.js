import{c as te,R as k,j as e,a4 as re,S as ne,C as ie,a5 as de,t as le,H as Q,a6 as oe,F as ce,Z as xe,r as V,v as ge,z as me,s as he,h as Y,a as Z,a7 as y,D as ue,a8 as fe,B as be,G as J,b as X}from"./index-BwF2e626.js";import{C as pe}from"./CircularProgress-D-mzTCDI.js";import{M as je,a as Ne,A as ye}from"./MealCheckModal-BLOQ1Imp.js";import{u as ve}from"./useDiary-Cu3EvJmA.js";import{u as we}from"./useDashboardCache-TdBNPxgt.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]],Ce=te("chart-line",ke);function De({progress:c,onAddProgress:n}){const[u,m]=k.useState(!1),[i,x]=k.useState({date:new Date().toISOString().split("T")[0],weight:0}),g=t=>{t.preventDefault(),i.weight&&i.date&&(n(i),m(!1),x({date:new Date().toISOString().split("T")[0],weight:0}))};return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Ce,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Progresso"}),e.jsx(re,{title:"Metodologia de Acompanhamento",description:"O acompanhamento do progresso segue protocolos científicos de avaliação corporal e diretrizes de saúde para monitoramento seguro."})]}),e.jsx("button",{onClick:()=>m(!u),className:"px-4 py-2 text-sm font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:"Adicionar Medida"})]}),u&&e.jsxs("form",{onSubmit:g,className:"mb-6 p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Data"}),e.jsx("input",{type:"date",value:i.date,onChange:t=>x({...i,date:t.target.value}),className:"w-full p-2 border border-snapfit-green/30 rounded-lg bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Peso (kg)"}),e.jsx("input",{type:"number",step:"0.1",value:i.weight||"",onChange:t=>x({...i,weight:parseFloat(t.target.value)}),className:"w-full p-2 border border-snapfit-green/30 rounded-lg bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green",required:!0})]})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{type:"button",onClick:()=>m(!1),className:"px-4 py-2 text-sm font-medium text-gray-400 hover:bg-snapfit-dark-gray/80 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"}),e.jsx("button",{type:"submit",className:"px-4 py-2 text-sm font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:"Salvar"})]})]}),e.jsx("div",{className:"space-y-4",children:c.map(t=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/10 rounded-full flex items-center justify-center border border-snapfit-green/20",children:e.jsx(ne,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:new Date(t.date).toLocaleDateString()}),e.jsxs("div",{className:"text-sm text-gray-400",children:[t.weight," kg",t.bodyFat&&` • ${t.bodyFat}% gordura`]})]})]}),t.photos&&t.photos.length>0&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-500/10 rounded-full flex items-center justify-center border border-blue-500/20",children:e.jsx(ie,{className:"w-3 h-3 text-blue-400"})}),e.jsxs("span",{className:"text-sm text-gray-400",children:[t.photos.length," fotos"]})]})]},t.date))}),e.jsx(de,{context:"acompanhamento"})]})}function Pe({caloriesConsumed:c=0,caloriesGoal:n=2e3,caloriesRemaining:u=0,caloriesBurned:m=0,protein:i={current:0,goal:150},carbs:x={current:0,goal:220},fat:g={current:0,goal:70}}){const t=le(),C=[{current:i.current,target:i.goal,percentage:i.current/i.goal*100,emoji:"🥩",color:"#FF4757",name:"Proteína"},{current:g.current,target:g.goal,percentage:g.current/g.goal*100,emoji:"🥑",color:"#F59E0B",name:"Gordura"},{current:x.current,target:x.goal,percentage:x.current/x.goal*100,emoji:"🍚",color:"#3B82F6",name:"Carboidrato"}];return e.jsxs("div",{className:"bg-snapfit-gray backdrop-blur-sm rounded-xl sm:rounded-3xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Q,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-sm sm:text-base font-bold text-white",children:"Resumo Nutricional"}),e.jsx(oe,{className:"ml-auto"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-8 mb-4 sm:mb-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 w-full sm:w-auto",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/10 rounded-full flex items-center justify-center mb-1",children:e.jsx(Q,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("div",{className:"text-lg sm:text-xl font-bold text-snapfit-green",children:c}),e.jsx("div",{className:"text-[10px] sm:text-xs text-gray-400",children:"Consumidas"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"w-8 h-8 bg-red-400/10 rounded-full flex items-center justify-center mb-1",children:e.jsx(ce,{className:"w-4 h-4 text-red-400"})}),e.jsx("div",{className:"text-lg sm:text-xl font-bold text-red-400",children:m}),e.jsx("div",{className:"text-[10px] sm:text-xs text-gray-400",children:"Gastas"})]})]}),e.jsx("div",{className:"relative flex items-center justify-center flex-shrink-0",children:e.jsx(pe,{value:c,max:n,size:120,className:"sm:size-[140px]",strokeWidth:12,color:"#B9FF43",label:"🔥",sublabel:"kcal restantes",centerText:`${Math.round(u)}`})}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10 w-full sm:w-auto",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-snapfit-green/10 rounded-full flex items-center justify-center",children:e.jsx(xe,{className:"w-3.5 h-3.5 text-snapfit-green"})}),e.jsx("span",{className:"text-xs font-medium text-white",children:"Balanço Calórico"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Meta"}),e.jsxs("div",{className:"text-sm font-medium text-white",children:[n," kcal"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Consumido"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:["+",c," kcal"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gasto"}),e.jsxs("div",{className:"text-sm font-medium text-red-400",children:["-",m," kcal"]})]}),e.jsxs("div",{className:"mt-2 pt-2 border-t border-snapfit-green/10 flex justify-between items-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Restante"}),e.jsxs("div",{className:"text-sm font-bold text-white",children:[u," kcal"]})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx("div",{className:"grid grid-cols-3 gap-4",children:C.map((d,v)=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx("span",{className:"text-lg",children:d.emoji}),e.jsx("span",{className:"text-xs font-medium text-white",children:d.name})]}),e.jsxs("span",{className:"text-[10px] text-gray-400",children:[Math.round(d.percentage),"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-lg font-bold text-white",children:[Math.round(d.current),"g"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["/ ",d.target,"g"]})]}),e.jsx("div",{className:"mt-2 w-full bg-snapfit-gray rounded-full h-1.5",children:e.jsx("div",{className:"h-1.5 rounded-full transition-all duration-300",style:{width:`${Math.min(100,d.percentage)}%`,backgroundColor:d.color}})})]},v))})}),e.jsx("div",{className:"mt-4 text-center",children:e.jsx("button",{onClick:()=>t("/dashboard/scientific-sources"),className:"text-xs text-gray-500 hover:text-blue-400 transition-colors underline decoration-dotted",children:"*cálculos baseados em diretrizes nutricionais oficiais"})})]})}function Re(){var A,M,T,L,I,S,_,E,R,q,O,G,B,z,H,W,$,K;const[c]=V.useState("week"),n=new Date().toISOString().split("T")[0],{queryKeys:u,prefetchDashboardData:m,cleanupStaleCache:i,onProgressAdded:x}=we(),{data:g,isLoading:t,error:C}=ge(c),{data:d,isLoading:v,error:Fe}=me(c),{data:a,isLoading:D,error:Ae}=ve(n,n),{dailyHydration:P}=he(),{data:j,isLoading:ee,error:Me}=Y({queryKey:u.assessments(),queryFn:async()=>{console.log("🔄 Fetching progress data for dashboard...");try{const s=await X.get("users/assessments");return console.log("📊 Progress response:",s),s.data||null}catch(s){return console.warn("⚠️ Progress API error:",s),null}},staleTime:1e3*60*10,refetchOnWindowFocus:!1,retry:1}),{data:h,isLoading:F,error:Te}=Y({queryKey:["dashboard","meals",n],queryFn:async()=>{var s,N,f,b;console.log("🔄 Dashboard: Fetching meals for today:",n),console.log("📅 Dashboard: Date being sent:",`${n} 00:00:00`),console.log("🔑 Dashboard: Access token:",localStorage.getItem("accessToken")?"Present":"Missing");try{const r=await X.get("users/protocols/diet/meals/active",{searchParams:{date:`${n} 00:00:00`}});return console.log("🍽️ Dashboard: Full response:",r),console.log("📋 Dashboard: Has protocol:",(s=r==null?void 0:r.data)==null?void 0:s.has_protocol),console.log("🥘 Dashboard: Meals count:",((f=(N=r==null?void 0:r.data)==null?void 0:N.meals)==null?void 0:f.length)||0),(b=r==null?void 0:r.data)!=null&&b.has_protocol||console.warn("⚠️ Dashboard: User does not have an active diet protocol"),(r==null?void 0:r.data)||{has_protocol:!1,meals:[]}}catch(r){return console.error("❌ Dashboard: API error:",r),{has_protocol:!1,meals:[]}}},staleTime:1e3*60*5,refetchOnWindowFocus:!1,retry:1}),l=(h==null?void 0:h.meals)||[],o=h!=null&&h.has_protocol?{has_protocol:!0,name:h.protocol_name||"Protocolo Ativo",meals:l}:null;k.useEffect(()=>{console.log("🔍 Dashboard - dailyMealsResponse:",h),console.log("🍽️ Dashboard - dailyMeals:",l),console.log("📊 Dashboard - dailyMealsData:",o),console.log("📅 Dashboard - today:",n)},[h,l,o,n]);const se=t||v||D||ee;V.useEffect(()=>(m(c),()=>{i()}),[c,m,i]);const ae=s=>{console.log("Progress added:",s),x()};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:"Dashboard"}),(j==null?void 0:j.name)&&e.jsxs("span",{className:"text-lg text-gray-600 dark:text-gray-300",children:["Olá, ",j.name," 👋"]})]}),e.jsxs("div",{className:"p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:[e.jsx("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:"Debug Info:"}),e.jsxs("div",{className:"text-xs text-yellow-700 dark:text-yellow-300 space-y-1",children:[e.jsxs("div",{children:["🔑 Token: ",localStorage.getItem("accessToken")?"Present":"Missing"]}),e.jsxs("div",{children:["🍽️ Meals Loading: ",F?"Yes":"No"]}),e.jsxs("div",{children:["📋 Has Active Protocol: ",o!=null&&o.has_protocol?"Yes":"No"]}),e.jsxs("div",{children:["🍽️ Meals Count: ",(l==null?void 0:l.length)||0]}),e.jsxs("div",{children:["📅 Today: ",n]}),e.jsx("div",{children:"🔗 API Endpoint: /users/protocols/diet/meals/active"})]})]}),se&&e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx(Z,{className:"w-8 h-8 text-indigo-600 animate-spin"}),e.jsx("span",{className:"ml-2 text-gray-600 dark:text-gray-300",children:"Carregando dados..."})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{className:"w-5 h-5 text-indigo-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-white",children:"Resumo Nutricional"})]}),a?e.jsx(Pe,{caloriesConsumed:((A=a.calories)==null?void 0:A.current)||0,caloriesGoal:((M=a.calories)==null?void 0:M.target)||2e3,caloriesRemaining:((T=a.calories)==null?void 0:T.remaining)||0,caloriesBurned:((L=a.calories)==null?void 0:L.burned)||0,protein:{current:((I=a.protein)==null?void 0:I.current)||0,goal:((S=a.protein)==null?void 0:S.target)||150},carbs:{current:((_=a.carbs)==null?void 0:_.current)||0,goal:((E=a.carbs)==null?void 0:E.target)||220},fat:{current:((R=a.fat)==null?void 0:R.current)||0,goal:((q=a.fat)==null?void 0:q.target)||70}}):e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center",children:D?"Carregando resumo nutricional...":"Nenhum dado nutricional disponível hoje"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"w-5 h-5 text-blue-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-white",children:"Hidratação"})]}),e.jsx(je,{day:n,goal:P.goal,current:P.current,onAdd:()=>{},loadDailyData:()=>{}})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{className:"w-5 h-5 text-green-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-white",children:"Refeições do Dia"})]}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:F?e.jsxs("div",{className:"flex items-center justify-center py-4",children:[e.jsx(Z,{className:"w-6 h-6 text-indigo-600 animate-spin"}),e.jsx("span",{className:"ml-2 text-gray-600 dark:text-gray-300",children:"Carregando refeições..."})]}):o!=null&&o.has_protocol&&l&&l.length>0?e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsxs("span",{className:"text-sm font-medium text-green-700 dark:text-green-300",children:["Protocolo Ativo: ",o.name]})]})}),l.map((s,N)=>{var f,b,r,U;return e.jsxs("div",{className:`p-3 rounded-lg border ${s.completed?"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800":"bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"font-medium text-gray-800 dark:text-white",children:s.name}),s.completed&&e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:s.meal_time}),s.completed?e.jsx("button",{className:"p-1.5 rounded-full bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 border border-green-200 dark:border-green-800",title:"Refeição concluída",children:e.jsx(fe,{className:"w-4 h-4"})}):e.jsx(Ne,{id:s.id,dateInit:n,timeInit:s.meal_time,onConfirm:(p,w)=>{console.log("Meal checked:",p,w)},onCancel:()=>console.log("Meal check cancelled")})]})]}),s.foods&&s.foods.length>0&&e.jsx("div",{className:"space-y-1 mb-3",children:s.foods.map((p,w)=>e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[p.quantity,p.unit," ",p.name]},w))}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("span",{className:"text-gray-600 dark:text-gray-300",children:[Math.round(((f=s.nutrients)==null?void 0:f.calories)||0)," kcal"]}),e.jsxs("span",{className:"text-gray-600 dark:text-gray-300",children:["P: ",Math.round(((b=s.nutrients)==null?void 0:b.protein)||0),"g • C: ",Math.round(((r=s.nutrients)==null?void 0:r.carbs)||0),"g • G: ",Math.round(((U=s.nutrients)==null?void 0:U.fat)||0),"g"]})]})]},s.id||N)})]}):o!=null&&o.has_protocol?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(y,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Nenhuma refeição programada para hoje"}),e.jsx("p",{className:"text-sm text-gray-400 dark:text-gray-500 mt-1",children:"Verifique seu protocolo na aba Dieta"})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(y,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Nenhum protocolo de dieta ativo"}),e.jsx("p",{className:"text-sm text-gray-400 dark:text-gray-500 mt-1",children:"Crie um protocolo na aba Dieta para ver suas refeições aqui"})]})}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx(be,{className:"w-5 h-5 text-purple-600"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white",children:"Sugestões de IA"})]}),e.jsx(ye,{caloriesRemaining:((O=a==null?void 0:a.calories)==null?void 0:O.remaining)||0,proteinRemaining:Math.max(0,(((G=a==null?void 0:a.protein)==null?void 0:G.target)||0)-(((B=a==null?void 0:a.protein)==null?void 0:B.current)||0)),carbsRemaining:Math.max(0,(((z=a==null?void 0:a.carbs)==null?void 0:z.target)||0)-(((H=a==null?void 0:a.carbs)==null?void 0:H.current)||0)),fatRemaining:Math.max(0,(((W=a==null?void 0:a.fat)==null?void 0:W.target)||0)-((($=a==null?void 0:a.fat)==null?void 0:$.current)||0)),mealsEaten:(l==null?void 0:l.map(s=>({name:s.name,time:s.meal_time})))||[],currentTime:new Date().toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",hour12:!1}),sleepQuality:"good"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(J,{className:"w-5 h-5 text-indigo-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-white",children:"Progresso Corporal"})]}),e.jsx(De,{progress:((K=g==null?void 0:g.history)==null?void 0:K.map(s=>({date:s.date,weight:s.weight,bodyFat:s.bodyFat||void 0,photos:s.photos||void 0,measurements:s.measurements||void 0})))||[],onAddProgress:ae})]}),d&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(J,{className:"w-5 h-5 text-green-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800 dark:text-white",children:"Visão Geral"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"Tendência de Peso"}),e.jsx("p",{className:"text-2xl font-bold text-gray-800 dark:text-white capitalize",children:d.weightTrend})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"Aderência Calórica"}),e.jsx("p",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:d.calorieAdherence})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"Consistência de Treino"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:[d.workoutConsistency,"%"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"Ganhos de Força"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-800 dark:text-white",children:["+",d.strengthGains,"%"]})]})]})]})]})}export{Re as DashboardPage};
