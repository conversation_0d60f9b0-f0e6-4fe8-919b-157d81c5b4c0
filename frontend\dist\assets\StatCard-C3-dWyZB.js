import{c as n,j as e,aK as p,M as a}from"./index-Dwgh6cj0.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],u=n("arrow-down",f);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],g=n("arrow-up",j);function y({title:d,value:i,icon:s,change:t,changeLabel:x="vs. last week",className:o="",onClick:r,showScientificBadge:l=!1}){const c=()=>t?t>0?"text-green-500":t<0?"text-red-500":"text-gray-500 dark:text-gray-400":"text-gray-500 dark:text-gray-400",m=()=>t?t>0?e.jsx(g,{className:"w-3 h-3"}):t<0?e.jsx(u,{className:"w-3 h-3"}):e.jsx(a,{className:"w-3 h-3"}):e.jsx(a,{className:"w-3 h-3"});return e.jsxs("div",{className:`bg-snapfit-gray rounded-2xl shadow-lg p-4 sm:p-5 flex flex-col items-center justify-center text-center ${o} ${r?"cursor-pointer":""}`,onClick:r,children:[s&&e.jsx("div",{className:"w-12 h-12 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green mb-3 border border-snapfit-green/30",children:s}),e.jsx("div",{className:"text-2xl sm:text-3xl font-bold text-snapfit-green",children:i}),e.jsx("div",{className:"text-sm font-medium text-white mt-1",children:d}),t!==void 0&&e.jsxs("div",{className:`flex items-center gap-1 mt-2 px-2 py-1 rounded-full bg-snapfit-dark-gray text-xs ${c()} border border-snapfit-gray/30`,children:[m(),e.jsxs("span",{children:[Math.abs(t),"%"]}),e.jsx("span",{className:"text-gray-400",children:x})]}),l&&e.jsx("div",{className:"mt-2",children:e.jsx(p,{})})]})}export{y as S};
