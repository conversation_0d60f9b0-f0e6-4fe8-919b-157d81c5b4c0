import{t as H,r as o,j as e,aD as O,aO as q,U as C,ae as I,ag as M,l as V,aj as U}from"./index-Dwgh6cj0.js";import{D as c}from"./download-DV8kSc7X.js";function X(){var S;const f=H(),[A,D]=o.useState(!0),[h,j]=o.useState(!1),[g,v]=o.useState([]),[N,y]=o.useState([]),[n,w]=o.useState("available"),[t,m]=o.useState(null),[d,u]=o.useState(null),[p,B]=o.useState(null);o.useEffect(()=>{R(),F()},[]);const R=async()=>{try{const s=await fetch("https://api.mysnapfit.com.br/professional-protocols/available",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(s.ok){const r=await s.json();v(r.filter(a=>a.type==="workout"))}}catch(s){console.error("Error fetching available protocols:",s),v([{id:"1",name:"Protocolo de Hipertrofia Avançado",objective:"Ganho de massa muscular com foco em hipertrofia e força",createdBy:{id:"coach-1",name:"Prof. <PERSON> Santos",photo:"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-2*24*60*60*1e3).toISOString(),notes:"Protocolo personalizado baseado em sua avaliação física. Inclui 4 treinos semanais com foco em grandes grupos musculares.",duration:8,frequency:"4x/semana",split:"A-B-C-D",workouts:[{id:"treino-a",name:"Treino A",exercises:[{id:"e1",name:"Supino Reto",sets:4,reps:"8-10",rpe:8,restTime:120,notes:"Foco na execução"},{id:"e2",name:"Supino Inclinado",sets:3,reps:"10-12",rpe:7,restTime:90},{id:"e3",name:"Crucifixo",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e4",name:"Tríceps Testa",sets:3,reps:"10-12",rpe:8,restTime:90},{id:"e5",name:"Tríceps Corda",sets:3,reps:"12-15",rpe:7,restTime:60}]},{id:"treino-b",name:"Treino B",exercises:[{id:"e6",name:"Puxada Frontal",sets:4,reps:"8-10",rpe:8,restTime:120},{id:"e7",name:"Remada Curvada",sets:3,reps:"10-12",rpe:7,restTime:90},{id:"e8",name:"Remada Unilateral",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e9",name:"Rosca Direta",sets:3,reps:"10-12",rpe:8,restTime:90},{id:"e10",name:"Rosca Martelo",sets:3,reps:"12-15",rpe:7,restTime:60}]},{id:"treino-c",name:"Treino C",exercises:[{id:"e11",name:"Agachamento",sets:4,reps:"8-10",rpe:8,restTime:120},{id:"e12",name:"Leg Press",sets:3,reps:"12-15",rpe:7,restTime:90},{id:"e13",name:"Stiff",sets:3,reps:"10-12",rpe:7,restTime:90},{id:"e14",name:"Panturrilha",sets:4,reps:"15-20",rpe:7,restTime:60}]},{id:"treino-d",name:"Treino D",exercises:[{id:"e15",name:"Desenvolvimento",sets:4,reps:"8-10",rpe:8,restTime:120},{id:"e16",name:"Elevação Lateral",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e17",name:"Elevação Posterior",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e18",name:"Encolhimento",sets:3,reps:"12-15",rpe:7,restTime:60}]}]},{id:"2",name:"Treino de Definição Muscular",objective:"Redução de gordura corporal mantendo massa muscular",createdBy:{id:"coach-1",name:"Prof. Carlos Santos",photo:"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-1*24*60*60*1e3).toISOString(),notes:"Protocolo de cutting com exercícios compostos e cardio HIIT integrado.",duration:6,frequency:"5x/semana",split:"A-B-A-B-C",workouts:[{id:"treino-a",name:"Treino A",exercises:[{id:"e19",name:"Agachamento",sets:4,reps:"12-15",rpe:8,restTime:60},{id:"e20",name:"Supino",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e21",name:"Puxada",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e22",name:"Desenvolvimento",sets:3,reps:"12-15",rpe:7,restTime:60}]},{id:"treino-b",name:"Treino B",exercises:[{id:"e23",name:"Stiff",sets:4,reps:"12-15",rpe:8,restTime:60},{id:"e24",name:"Remada",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e25",name:"Inclinado",sets:3,reps:"12-15",rpe:7,restTime:60},{id:"e26",name:"Rosca",sets:3,reps:"12-15",rpe:7,restTime:60}]},{id:"treino-c",name:"Treino C",exercises:[{id:"e27",name:"HIIT Esteira",sets:1,reps:"15 min",restTime:0,notes:"30s sprint / 30s caminhada"},{id:"e28",name:"Prancha",sets:3,reps:"45s",rpe:7,restTime:60},{id:"e29",name:"Mountain Climber",sets:3,reps:"30s",rpe:8,restTime:60},{id:"e30",name:"Burpee",sets:3,reps:"10",rpe:8,restTime:90}]}]},{id:"3",name:"Protocolo Funcional",objective:"Melhoria da capacidade funcional e mobilidade",createdBy:{id:"coach-2",name:"Dra. Ana Silva",photo:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop"},createdAt:new Date().toISOString(),notes:"Treino funcional adaptado para suas necessidades específicas de mobilidade e força.",duration:4,frequency:"3x/semana",split:"A-B-C",workouts:[{id:"treino-a",name:"Treino A",exercises:[{id:"e31",name:"Agachamento Livre",sets:3,reps:"15-20",rpe:6,restTime:45},{id:"e32",name:"Flexão",sets:3,reps:"10-15",rpe:7,restTime:60},{id:"e33",name:"Prancha",sets:3,reps:"30-60s",rpe:7,restTime:60},{id:"e34",name:"Alongamento",sets:1,reps:"10 min",restTime:0,notes:"Foco em mobilidade"}]},{id:"treino-b",name:"Treino B",exercises:[{id:"e35",name:"Afundo",sets:3,reps:"12 cada perna",rpe:6,restTime:60},{id:"e36",name:"Remada com Elástico",sets:3,reps:"15-20",rpe:6,restTime:45},{id:"e37",name:"Prancha Lateral",sets:3,reps:"20-30s cada lado",rpe:7,restTime:60},{id:"e38",name:"Mobilidade Articular",sets:1,reps:"10 min",restTime:0}]},{id:"treino-c",name:"Treino C",exercises:[{id:"e39",name:"Burpee",sets:3,reps:"8-12",rpe:8,restTime:90},{id:"e40",name:"Mountain Climber",sets:3,reps:"20s",rpe:7,restTime:60},{id:"e41",name:"Polichinelo",sets:3,reps:"30s",rpe:6,restTime:45},{id:"e42",name:"Relaxamento",sets:1,reps:"5 min",restTime:0,notes:"Respiração e relaxamento"}]}]}])}finally{D(!1)}},F=async()=>{try{const s=await fetch("https://api.mysnapfit.com.br/professional-protocols/history?type=workout",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(s.ok){const r=await s.json();y(r)}}catch(s){console.error("Error fetching protocol history:",s),y([{id:"hist-1",name:"Protocolo de Força - Concluído",objective:"Desenvolvimento de força e potência muscular",createdBy:{id:"coach-1",name:"Prof. Carlos Santos",photo:"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-30*24*60*60*1e3).toISOString(),notes:"Protocolo de 8 semanas focado em força máxima. Resultados excelentes!"},{id:"hist-2",name:"Iniciante - Adaptação",objective:"Adaptação inicial ao treinamento resistido",createdBy:{id:"coach-1",name:"Prof. Carlos Santos",photo:"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=50&h=50&fit=crop"},createdAt:new Date(Date.now()-60*24*60*60*1e3).toISOString(),notes:"Primeiro protocolo personalizado. Base para progressões futuras."}])}},$=()=>{f("/dashboard/workout",{replace:!0})},E=s=>{var r,a;m(s),T(((a=(r=s.workouts)==null?void 0:r[0])==null?void 0:a.id)||null)},[b,T]=o.useState(null),k=async s=>{u(s),j(!0);try{if((await fetch(`https://api.mysnapfit.com.br/professional-protocols/import/${s}`,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok){const a=g.find(l=>l.id===s);B((a==null?void 0:a.name)||"Protocolo"),setTimeout(()=>{f("/dashboard/workout",{replace:!0})},2e3)}else u(null)}catch{u(null)}finally{j(!1)}},z=s=>new Date(s).toLocaleDateString("pt-BR",{day:"numeric",month:"short",year:"numeric"});if(h)return e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(O,{type:"workout",message:"Importando protocolo de treino..."})});const P=n==="available"?g:N;return e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-between p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:$,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(q,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center border border-orange-500/30",children:e.jsx(c,{className:"w-4 h-4 text-orange-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-medium text-white",children:"Importar Protocolo de Treino"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(C,{className:"w-3 h-3 text-orange-400"}),e.jsx("span",{children:"Protocolos do seu coach"})]})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-1 border border-snapfit-green/20",children:e.jsxs("div",{className:"grid grid-cols-2 gap-1",children:[e.jsxs("button",{onClick:()=>w("available"),className:`flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${n==="available"?"bg-snapfit-green text-black":"text-gray-400 hover:text-white"}`,children:[e.jsx(c,{className:"w-4 h-4"}),"Disponíveis (",g.length,")"]}),e.jsxs("button",{onClick:()=>w("history"),className:`flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${n==="history"?"bg-snapfit-green text-black":"text-gray-400 hover:text-white"}`,children:[e.jsx(I,{className:"w-4 h-4"}),"Histórico (",N.length,")"]})]})}),A?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-8 border border-snapfit-green/20 text-center",children:[e.jsx("div",{className:"w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-orange-500/30",children:e.jsx(c,{className:"w-6 h-6 text-orange-400 animate-pulse"})}),e.jsx("p",{className:"text-white font-medium mb-2",children:"Carregando protocolos..."}),e.jsx("p",{className:"text-sm text-gray-400",children:"Buscando protocolos do seu coach"})]}):P.length===0?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-8 border border-snapfit-green/20 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4",children:n==="available"?e.jsx(c,{className:"w-8 h-8 text-gray-400"}):e.jsx(I,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:n==="available"?"Nenhum protocolo disponível":"Nenhum protocolo no histórico"}),e.jsx("p",{className:"text-sm text-gray-400 mb-4",children:n==="available"?"Seu coach ainda não criou protocolos para você":"Você ainda não importou nenhum protocolo"}),n==="available"&&e.jsx("div",{className:"bg-orange-500/10 rounded-lg p-4 border border-orange-500/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(M,{className:"w-5 h-5 text-orange-400 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-left",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-1",children:"Como receber protocolos?"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"1. Certifique-se de estar sincronizado com seu coach"}),e.jsx("p",{children:"2. Seu coach criará protocolos personalizados"}),e.jsx("p",{children:"3. Você receberá uma notificação quando estiver disponível"})]})]})]})})]}):e.jsx("div",{className:"space-y-4",children:P.map(s=>e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-1",children:s.name}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:s.objective}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(C,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:s.createdBy.name})]}),e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(V,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:z(s.createdAt)})]})]})]}),e.jsx("div",{className:"flex items-center gap-2 ml-4",children:e.jsx("img",{src:s.createdBy.photo||"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=40&h=40&fit=crop",alt:s.createdBy.name,className:"w-10 h-10 rounded-full object-cover border border-snapfit-green/30"})})]}),s.notes&&e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 mb-4 border border-snapfit-green/10",children:e.jsx("p",{className:"text-sm text-gray-300",children:s.notes})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>E(s),className:"flex-1 px-4 py-2.5 text-sm font-medium text-gray-400 border border-gray-600 rounded-lg hover:text-white hover:border-gray-500 transition-colors",children:"Visualizar"}),e.jsx("button",{onClick:()=>k(s.id),disabled:h,className:`flex-1 flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${p&&d===s.id?"bg-green-500 text-white":d===s.id?"bg-orange-500 text-black":"bg-snapfit-green text-black hover:bg-snapfit-green/90"} disabled:opacity-50`,children:p&&d===s.id?e.jsxs(e.Fragment,{children:[e.jsx(Check,{className:"w-4 h-4"}),"Importado!"]}):d===s.id?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"}),"Importando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"w-4 h-4"}),n==="available"?"Importar":"Reimportar"]})})]})]},s.id))})]})}),t&&e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:t.name}),e.jsx("p",{className:"text-sm text-gray-400 mt-1",children:t.objective})]}),e.jsx("button",{onClick:()=>m(null),className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(U,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Duração"}),e.jsxs("div",{className:"text-lg font-medium text-white",children:[t.duration," semanas"]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Frequência"}),e.jsx("div",{className:"text-lg font-medium text-white",children:t.frequency})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Divisão"}),e.jsx("div",{className:"text-lg font-medium text-white",children:t.split})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Criado por"}),e.jsx("div",{className:"text-lg font-medium text-white",children:t.createdBy.name})]})]}),t.notes&&e.jsxs("div",{className:"bg-orange-500/10 rounded-lg p-4 mb-6 border border-orange-500/20",children:[e.jsx("h3",{className:"text-sm font-medium text-orange-400 mb-2",children:"Observações do Coach"}),e.jsx("p",{className:"text-sm text-gray-300",children:t.notes})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Treinos do Protocolo"}),e.jsx("div",{className:"flex overflow-x-auto scrollbar-hide gap-1 sm:gap-2 border-b border-gray-700 pb-2",children:(S=t.workouts)==null?void 0:S.map((s,r)=>{const a=["text-snapfit-green border-snapfit-green","text-blue-400 border-blue-400","text-purple-400 border-purple-400","text-orange-400 border-orange-400"],l=a[r%a.length],x=b===s.id;return e.jsx("button",{onClick:()=>T(s.id),className:`px-3 py-2 text-sm font-medium border-b-2 transition-colors whitespace-nowrap flex-shrink-0 ${x?l:"text-gray-400 border-transparent hover:text-gray-300"}`,children:s.name},s.id)})}),b&&t.workouts&&e.jsx("div",{className:"space-y-3",children:t.workouts.filter(s=>s.id===b).map(s=>{var x;const r=["border-snapfit-green/30 bg-snapfit-green/5","border-blue-500/30 bg-blue-500/5","border-purple-500/30 bg-purple-500/5","border-orange-500/30 bg-orange-500/5"],a=((x=t.workouts)==null?void 0:x.findIndex(i=>i.id===s.id))||0,l=r[a%r.length];return e.jsx("div",{className:`border rounded-lg p-4 ${l}`,children:e.jsx("div",{className:"space-y-3",children:s.exercises.map((i,L)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-black/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center text-snapfit-green text-sm font-medium",children:L+1}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:i.name}),i.notes&&e.jsx("div",{className:"text-xs text-gray-400",children:i.notes})]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-white font-medium",children:i.sets}),e.jsx("div",{children:"séries"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-white font-medium",children:i.reps}),e.jsx("div",{children:"reps"})]}),i.rpe&&e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-white font-medium",children:i.rpe}),e.jsx("div",{children:"RPE"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-white font-medium",children:[i.restTime,"s"]}),e.jsx("div",{children:"descanso"})]})]})]},i.id))})},s.id)})})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-6 border-t border-snapfit-green/20",children:[e.jsx("button",{onClick:()=>m(null),className:"px-4 py-2.5 text-sm font-medium text-gray-400 border border-gray-600 rounded-lg hover:text-white hover:border-gray-500 transition-colors",children:"Fechar"}),e.jsx("button",{onClick:()=>{k(t.id),m(null)},disabled:h,className:`flex items-center gap-2 px-4 py-2.5 text-sm font-medium rounded-lg transition-colors ${p&&d===t.id?"bg-green-500 text-white":d===t.id?"bg-orange-500 text-black":"bg-snapfit-green text-black hover:bg-snapfit-green/90"} disabled:opacity-50`,children:p&&d===t.id?e.jsxs(e.Fragment,{children:[e.jsx(Check,{className:"w-4 h-4"}),"Importado!"]}):d===t.id?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"}),"Importando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"w-4 h-4"}),"Importar Protocolo"]})})]})]})})]})}export{X as CreateWorkoutProtocolImportPage};
