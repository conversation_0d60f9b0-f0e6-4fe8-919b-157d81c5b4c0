import{b as l,t as M,r as h,j as e,I as _,H as C,aj as R,l as v,f as T,ac as q,az as O}from"./index-Dwgh6cj0.js";class k{static async getProtocolHistory(t={}){try{const r=new URLSearchParams;return t.type&&r.append("type",t.type),t.status&&r.append("status",t.status),t.page&&r.append("page",t.page.toString()),t.limit&&r.append("limit",t.limit.toString()),(await l.get(`protocols/history?${r.toString()}`)).data}catch(r){throw console.error("Error fetching protocol history:",r),r}}static async getWorkoutProgramsHistory(t={}){try{const r=new URLSearchParams;return t.page&&r.append("page",t.page.toString()),t.limit&&r.append("limit",t.limit.toString()),t.status&&r.append("status",t.status),(await l.get(`workouts/programs/history?${r.toString()}`)).data}catch(r){throw console.error("Error fetching workout programs history:",r),r}}static async getDietProtocolsHistory(t={}){try{const r=new URLSearchParams;return t.page&&r.append("page",t.page.toString()),t.limit&&r.append("limit",t.limit.toString()),t.status&&r.append("status",t.status),(await l.get(`diet/protocols/history?${r.toString()}`)).data}catch(r){throw console.error("Error fetching diet protocols history:",r),r}}static async getProtocolDetails(t){try{return(await l.get(`protocols/${t}`)).data}catch(r){throw console.error("Error fetching protocol details:",r),r}}static async duplicateProtocol(t,r){try{return(await l.post(`protocols/${t}/duplicate`,{name:r})).data}catch(o){throw console.error("Error duplicating protocol:",o),o}}static async duplicateWorkoutProgram(t,r){try{return(await l.post(`workouts/programs/${t}/duplicate`,{name:r})).data}catch(o){throw console.error("Error duplicating workout program:",o),o}}static async duplicateDietProtocol(t,r){try{return(await l.post(`diet/protocols/${t}/duplicate`,{name:r})).data}catch(o){throw console.error("Error duplicating diet protocol:",o),o}}static async completeProtocol(t){try{return(await l.post(`protocols/${t}/complete`)).data}catch(r){throw console.error("Error completing protocol:",r),r}}static async archiveProtocol(t){try{return(await l.post(`protocols/${t}/archive`)).data}catch(r){throw console.error("Error archiving protocol:",r),r}}}function H({protocolId:c,type:t,isOpen:r,onClose:o,onReuseProtocol:m}){var j;const b=M(),[s,D]=h.useState(null),[P,u]=h.useState(!1);h.useEffect(()=>{console.log("🔍 ProtocolDetailsModal: useEffect triggered - isOpen:",r,"protocolId:",c),r&&c?(console.log("🔍 ProtocolDetailsModal: Fetching protocol details..."),S()):console.log("🔍 ProtocolDetailsModal: Not fetching - conditions not met")},[r,c]);const S=async()=>{var i,n,p,g;u(!0);try{console.log("🔍 ProtocolDetailsModal: Fetching protocol details for ID:",c,"Type:",t);let d;t==="workout"?d=await l.get(`users/protocols/workout/${c}`):d=await l.get(`users/protocols/diet/${c}`),console.log("📋 ProtocolDetailsModal: Protocol data received:",d);const a=((i=d.data)==null?void 0:i.data)||d.data,$={id:((n=a.id)==null?void 0:n.toString())||c,name:a.name||"Protocolo sem nome",type:a.objective||(t==="workout"?"hypertrophy":"weight_loss"),objective:a.objective||"Objetivo não especificado",startDate:a.started_at?new Date(a.started_at).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],endDate:a.ended_at?new Date(a.ended_at).toISOString().split("T")[0]:void 0,status:a.ended_at?"completed":"active",createdAt:a.created_at||new Date().toISOString(),updatedAt:a.updated_at||new Date().toISOString(),split:a.split||"Não especificado",frequency:((p=a.frequency)==null?void 0:p.toString())||"3",completedWorkouts:a.workouts_completed||0,notes:a.notes||a.general_notes||"Nenhuma observação",content:{workouts:((g=a.workouts)==null?void 0:g.map(y=>{var w;return{name:y.name,exercises:((w=y.exercises)==null?void 0:w.map(N=>N.name||N.exercise_name))||[]}}))||[]},goals:t==="diet"?{calories:a.calories||0,protein:a.protein||0,carbs:a.carbs||0,fat:a.fat||0,water:a.water||0}:void 0,waterCalculationMethod:a.water_calculation_method||"manual"};D($),console.log("✅ ProtocolDetailsModal: Protocol details loaded successfully")}catch(d){console.error("Error fetching protocol details:",d)}finally{u(!1)}},f=async(i=!1)=>{if(s){if(i){b(t==="diet"?`/dashboard/diet/edit-protocol/${s.id}`:`/dashboard/workout/edit-protocol/${s.id}`),o();return}if(m)try{let n;t==="workout"?n=await k.duplicateWorkoutProgram(s.id,`${s.name} (Reutilizado)`):n=await k.duplicateDietProtocol(s.id,`${s.name} (Reutilizado)`),m(n,!1),o()}catch(n){console.error("Error reusing protocol:",n),alert("Erro ao reutilizar protocolo. Tente novamente.")}}},x=i=>new Date(i).toLocaleDateString("pt-BR"),E=i=>{switch(i){case"active":return"bg-green-100 text-green-800 border-green-200";case"completed":return"bg-blue-100 text-blue-800 border-blue-200";case"archived":return"bg-gray-100 text-gray-800 border-gray-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return console.log("🔍 ProtocolDetailsModal: Render check - isOpen:",r,"protocolId:",c),r?(console.log("🔍 ProtocolDetailsModal: Rendering modal!"),e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:t==="workout"?e.jsx(_,{className:"w-5 h-5 text-snapfit-green"}):e.jsx(C,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-bold text-white",children:["Detalhes do ",t==="workout"?"Treino":"Protocolo de Dieta"]}),s&&e.jsx("p",{className:"text-sm text-gray-400",children:s.name})]})]}),e.jsx("button",{onClick:o,className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(R,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:P?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"})}):s?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Nome"}),e.jsx("p",{className:"text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:s.name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Objetivo"}),e.jsx("p",{className:"text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:s.objective})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Status"}),e.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${E(s.status)}`,children:s.status==="active"?"Ativo":s.status==="completed"?"Concluído":"Arquivado"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Data de Início"}),e.jsxs("div",{className:"flex items-center gap-2 text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:[e.jsx(v,{className:"w-4 h-4 text-snapfit-green"}),x(s.startDate)]})]}),s.endDate&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Data de Término"}),e.jsxs("div",{className:"flex items-center gap-2 text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:[e.jsx(v,{className:"w-4 h-4 text-snapfit-green"}),x(s.endDate)]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Criado em"}),e.jsxs("div",{className:"flex items-center gap-2 text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:[e.jsx(T,{className:"w-4 h-4 text-snapfit-green"}),x(s.createdAt)]})]})]})]}),t==="workout"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white border-b border-snapfit-green/20 pb-2",children:"Detalhes do Treino"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.split&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Divisão"}),e.jsx("p",{className:"text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:s.split})]}),s.frequency&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Frequência"}),e.jsxs("p",{className:"text-white bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-green/10",children:[s.frequency,"x por semana"]})]})]})]}),t==="diet"&&s.goals&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white border-b border-snapfit-green/20 pb-2",children:"Metas Nutricionais"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsx("p",{className:"text-2xl font-bold text-snapfit-green",children:s.goals.calories}),e.jsx("p",{className:"text-sm text-gray-400",children:"Calorias"})]}),e.jsxs("div",{className:"text-center bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("p",{className:"text-2xl font-bold text-blue-400",children:[s.goals.protein,"g"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Proteína"})]}),e.jsxs("div",{className:"text-center bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("p",{className:"text-2xl font-bold text-yellow-400",children:[s.goals.carbs,"g"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Carboidratos"})]}),e.jsxs("div",{className:"text-center bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("p",{className:"text-2xl font-bold text-orange-400",children:[s.goals.fat,"g"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Gorduras"})]})]})]}),t==="workout"&&((j=s.content)==null?void 0:j.workouts)&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white border-b border-snapfit-green/20 pb-2",children:"Treinos do Protocolo"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.content.workouts.map((i,n)=>e.jsxs("div",{className:"bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"font-medium text-white mb-2",children:i.name}),e.jsx("ul",{className:"space-y-1",children:i.exercises.map((p,g)=>e.jsxs("li",{className:"text-sm text-gray-300 flex items-center gap-2",children:[e.jsx("span",{className:"w-1.5 h-1.5 bg-snapfit-green rounded-full"}),p]},g))})]},n))})]}),t==="diet"&&s.weeklyMeals&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white border-b border-snapfit-green/20 pb-2",children:"Refeições do Protocolo"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.weeklyMeals.map((i,n)=>e.jsxs("div",{className:"bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"font-medium text-white mb-2",children:i.name}),e.jsx("ul",{className:"space-y-1",children:i.foods.map((p,g)=>e.jsxs("li",{className:"text-sm text-gray-300 flex items-center gap-2",children:[e.jsx("span",{className:"w-1.5 h-1.5 bg-snapfit-green rounded-full"}),p]},g))})]},n))})]}),t==="diet"&&s.supplements&&s.supplements.length>0&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white border-b border-snapfit-green/20 pb-2",children:"Suplementação"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.supplements.map((i,n)=>e.jsxs("div",{className:"bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"font-medium text-white",children:i.name}),e.jsxs("p",{className:"text-sm text-gray-300",children:["Dosagem: ",i.dosage]}),e.jsxs("p",{className:"text-sm text-gray-300",children:["Horário: ",i.timing]})]},n))})]}),s.notes&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Observações"}),e.jsx("div",{className:"bg-snapfit-dark-gray p-4 rounded-lg border border-snapfit-green/10",children:e.jsx("p",{className:"text-white whitespace-pre-wrap",children:s.notes})})]})]}):e.jsx("div",{className:"text-center py-12 text-gray-400",children:e.jsx("p",{children:"Erro ao carregar detalhes do protocolo"})})}),s&&m&&e.jsxs("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-snapfit-green/20",children:[e.jsx("button",{onClick:o,className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Fechar"}),e.jsxs("button",{onClick:()=>f(!1),className:"flex items-center gap-2 px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg border border-blue-500/30 hover:bg-blue-500/30 transition-colors",children:[e.jsx(q,{className:"w-4 h-4"}),"Reutilizar"]}),e.jsxs("button",{onClick:()=>f(!0),className:"flex items-center gap-2 px-4 py-2 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors",children:[e.jsx(O,{className:"w-4 h-4"}),"Editar e Usar"]})]})]})})):(console.log("🔍 ProtocolDetailsModal: Not rendering - isOpen is false"),null)}export{H as P};
