import{c as Ae,h as Y,u as _e,at as G,y as R,b as q,r as C,j as e,aV as cs,aW as ds,D as ms,n as es,V as xs,aX as gs,aY as us,aZ as hs,a0 as He,a_ as ps,Y as fs,_ as bs,$ as js,a$ as Je,B as Te,ah as Se,al as ss,b0 as vs,b1 as ys,b2 as Ns,b3 as ws,ap as ne,a4 as ks,ab as he,G as le,b4 as Cs,a5 as Ss,ao as qe,l as ze,b5 as Ee,f as pe,b6 as ie,a7 as ue,aj as ae,b7 as Ms,R as B,a6 as Ps,b8 as Ds,t as as,af as As,H as ee,b9 as ts,ba as $e,bb as rs,au as De,bc as Fs,bd as Rs,P as se,aP as Ze,a8 as Is,az as Ts,be as qs,ae as Es,bf as $s,bg as U,aA as Bs,bh as _s,bi as zs,aJ as Ls}from"./index-Dwgh6cj0.js";import{a as Os,u as Vs}from"./useDiary-a_UM12Ud.js";import{C as Ks}from"./CircularProgress-DvDWUz0F.js";import{D as ns}from"./download-DV8kSc7X.js";import{P as Qs}from"./ProtocolDetailsModal-CL-OPKW3.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Us=[["path",{d:"M17 21a1 1 0 0 0 1-1v-5.35c0-.457.316-.844.727-1.041a4 4 0 0 0-2.134-7.589 5 5 0 0 0-9.186 0 4 4 0 0 0-2.134 7.588c.411.198.727.585.727 1.041V20a1 1 0 0 0 1 1Z",key:"1qvrer"}],["path",{d:"M6 17h12",key:"1jwigz"}]],Gs=Ae("chef-hat",Us);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=[["path",{d:"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z",key:"wa1lgi"}],["path",{d:"m8.5 8.5 7 7",key:"rvfmvr"}]],Me=Ae("pill",Ws);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hs=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],Js=Ae("shopping-cart",Hs);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zs=[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]],Be=Ae("utensils",Zs),Pe={protocols:()=>["diet","protocols"],activeProtocol:()=>["diet","protocols","active"],protocol:s=>["diet","protocols",s]};function Xs(){return Y({queryKey:Pe.activeProtocol(),queryFn:async()=>{var s;console.log("🔍 useActiveDietProtocol: Buscando protocolo ativo...");try{const r=await q.get("users/protocols/diet/active");return console.log("✅ useActiveDietProtocol: Resposta completa:",r),(r==null?void 0:r.status)==="success"&&((s=r==null?void 0:r.data)!=null&&s.has_protocol)?(console.log("✅ useActiveDietProtocol: Protocolo ativo encontrado:",r.data),localStorage.removeItem("mock-protocol-deleted"),r.data):(console.log("⚠️ useActiveDietProtocol: Nenhum protocolo ativo encontrado no backend"),console.log("💾 useActiveDietProtocol: Nenhum protocolo ativo, retornando null"),null)}catch(r){return console.log("❌ useActiveDietProtocol: Erro ao buscar protocolo:",r),console.log("💾 useActiveDietProtocol: Erro ao buscar protocolo, retornando null"),null}},staleTime:5*60*1e3,retry:!1})}function Ys(){const s=_e();return G({mutationFn:async r=>{if(console.log("🗑️ useRemoveDietProtocol: Removendo protocolo...",r,"Tipo:",typeof r),r==="mock-protocol-id-123"||String(r)==="mock-protocol-id-123")return localStorage.removeItem("mock-protocol-data"),localStorage.setItem("mock-protocol-deleted","true"),console.log("✅ useRemoveDietProtocol: Dados mock removidos e flag de deleção definida"),{success:!0,message:"Protocolo mock removido"};console.log("🌐 useRemoveDietProtocol: Fazendo chamada para API...");const o=await q.delete(`users/protocols/diet/${r}`);return console.log("✅ useRemoveDietProtocol: Resposta da API:",o),o.data||o},onSuccess:(r,o)=>{s.invalidateQueries({queryKey:Pe.protocols()}),s.invalidateQueries({queryKey:Pe.activeProtocol()}),s.removeQueries({queryKey:Pe.protocol(o)}),R.success("Protocolo removido com sucesso!",{position:"bottom-right"}),console.log("✅ useRemoveDietProtocol: Protocolo removido com sucesso")},onError:r=>{console.error("❌ useRemoveDietProtocol: Erro ao remover protocolo:",r),R.error("Erro ao remover protocolo. Tente novamente.",{position:"bottom-right"})}})}function ea({nutritionGoals:s,className:r=""}){var f,g,u,n,a,d,m,v,F,P,Q,I,k,z,te,W,re,H;const[o,t]=C.useState("pie"),l=[{name:"Proteínas",current:((f=s.protein)==null?void 0:f.current)||0,target:((g=s.protein)==null?void 0:g.target)||0,calories:(((u=s.protein)==null?void 0:u.current)||0)*4,percentage:Math.round((((n=s.protein)==null?void 0:n.current)||0)*4/Math.max(((a=s.calories)==null?void 0:a.current)||1,1)*100),color:"#B9FF43",icon:e.jsx(cs,{className:"w-4 h-4"})},{name:"Carboidratos",current:((d=s.carbs)==null?void 0:d.current)||0,target:((m=s.carbs)==null?void 0:m.target)||0,calories:(((v=s.carbs)==null?void 0:v.current)||0)*4,percentage:Math.round((((F=s.carbs)==null?void 0:F.current)||0)*4/Math.max(((P=s.calories)==null?void 0:P.current)||1,1)*100),color:"#4CAF50",icon:e.jsx(ds,{className:"w-4 h-4"})},{name:"Gorduras",current:((Q=s.fat)==null?void 0:Q.current)||0,target:((I=s.fat)==null?void 0:I.target)||0,calories:(((k=s.fat)==null?void 0:k.current)||0)*9,percentage:Math.round((((z=s.fat)==null?void 0:z.current)||0)*9/Math.max(((te=s.calories)==null?void 0:te.current)||1,1)*100),color:"#FFC107",icon:e.jsx(ms,{className:"w-4 h-4"})}],x=l.map(w=>({name:w.name,value:w.calories,percentage:w.percentage,color:w.color})),p=l.map(w=>({name:w.name.substring(0,4),current:w.current,target:w.target,color:w.color})),b=l.reduce((w,E)=>w+E.calories,0),y=Math.round(b/Math.max(((W=s.calories)==null?void 0:W.target)||1,1)*100),j=(((re=s.calories)==null?void 0:re.target)||0)-b,c=({active:w,payload:E})=>{if(w&&E&&E.length){const J=E[0];return e.jsxs("div",{className:"bg-snapfit-dark-gray border border-snapfit-green/30 rounded-lg p-3 shadow-lg",children:[e.jsx("p",{className:"text-white font-medium",children:J.name}),e.jsxs("p",{className:"text-snapfit-green",children:[J.value," kcal (",J.payload.percentage,"%)"]})]})}return null};return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20 ${r}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(es,{className:"w-5 h-5 text-snapfit-green"}),e.jsx("h3",{className:"text-base sm:text-lg font-bold text-white",children:"Distribuição Calórica"})]}),e.jsxs("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20",children:[e.jsx("button",{onClick:()=>t("pie"),className:`px-3 py-1 text-xs rounded-md transition-colors ${o==="pie"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white"}`,children:"Pizza"}),e.jsx("button",{onClick:()=>t("bar"),className:`px-3 py-1 text-xs rounded-md transition-colors ${o==="bar"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white"}`,children:"Barras"})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-3 mb-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 text-center border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Consumidas"}),e.jsx("div",{className:"text-lg font-bold text-snapfit-green",children:b}),e.jsx("div",{className:"text-xs text-gray-500",children:"kcal"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 text-center border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Meta"}),e.jsx("div",{className:"text-lg font-bold text-white",children:((H=s.calories)==null?void 0:H.target)||0}),e.jsx("div",{className:"text-xs text-gray-500",children:"kcal"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 text-center border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Restantes"}),e.jsx("div",{className:`text-lg font-bold ${j>=0?"text-blue-400":"text-red-400"}`,children:Math.abs(j)}),e.jsx("div",{className:"text-xs text-gray-500",children:"kcal"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Progresso Calórico"}),e.jsxs("span",{className:"text-white font-medium",children:[y,"%"]})]}),e.jsx("div",{className:"w-full bg-snapfit-dark-gray rounded-full h-2 border border-snapfit-green/20",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-500 ${y<=100?"bg-snapfit-green":"bg-red-400"}`,style:{width:`${Math.min(y,100)}%`}})})]}),e.jsx("div",{className:"h-[250px] mb-6",children:e.jsx(xs,{width:"100%",height:"100%",children:o==="pie"?e.jsxs(gs,{children:[e.jsx(us,{data:x,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:2,stroke:"transparent",children:x.map((w,E)=>e.jsx(hs,{fill:w.color,filter:"drop-shadow(0 0 3px rgba(185, 255, 67, 0.3))"},E))}),e.jsx(He,{content:e.jsx(c,{})})]}):e.jsxs(ps,{data:p,margin:{top:5,right:5,bottom:5,left:5},children:[e.jsx(fs,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),e.jsx(bs,{dataKey:"name",fontSize:12,stroke:"rgba(255,255,255,0.5)"}),e.jsx(js,{fontSize:12,stroke:"rgba(255,255,255,0.5)"}),e.jsx(He,{contentStyle:{backgroundColor:"#1E1E1E",border:"1px solid rgba(185, 255, 67, 0.3)",borderRadius:"8px",color:"white"}}),e.jsx(Je,{dataKey:"current",name:"Atual",fill:"#B9FF43",radius:[2,2,0,0]}),e.jsx(Je,{dataKey:"target",name:"Meta",fill:"rgba(255,255,255,0.2)",radius:[2,2,0,0]})]})})}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:l.map((w,E)=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-6 h-6 rounded-full flex items-center justify-center",style:{backgroundColor:`${w.color}20`},children:e.jsx("div",{style:{color:w.color},children:w.icon})}),e.jsx("span",{className:"text-sm font-medium text-white",children:w.name})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"Atual"}),e.jsxs("span",{className:"text-white font-medium",children:[w.current,"g"]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"Meta"}),e.jsxs("span",{className:"text-gray-300",children:[w.target,"g"]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"Calorias"}),e.jsxs("span",{style:{color:w.color},className:"font-medium",children:[w.calories," kcal"]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"% Total"}),e.jsxs("span",{style:{color:w.color},className:"font-medium",children:[w.percentage,"%"]})]})]}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-1.5",children:e.jsx("div",{className:"h-1.5 rounded-full transition-all duration-500",style:{width:`${Math.min(w.current/w.target*100,100)}%`,backgroundColor:w.color}})})})]},E))})]})}function sa({onGenerateAI:s,onCreateManual:r,onImportNutritionist:o,onReadProtocol:t,onClose:l}){return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Novo Protocolo de Dieta"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("button",{onClick:s,className:`\r
            w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10`,children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Te,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Gerar com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gerar protocolo personalizado baseado no seu perfil"})]})]}),e.jsxs("button",{onClick:r,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Se,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Criar Manualmente"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Monte seu próprio protocolo selecionando alimentos"})]})]}),t&&e.jsxs("button",{onClick:t,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(ss,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Ler seu protocolo atual com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Faça upload de um PDF ou imagem do seu protocolo atual"})]})]}),e.jsxs("button",{onClick:o,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(ns,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Importar do Nutricionista"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Importar protocolo criado pelo seu nutricionista"})]})]})]}),e.jsx("button",{onClick:l,className:"w-full mt-6 px-4 py-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"})]})})}function aa({protocolGoals:s,className:r=""}){const{getAssessmentForWeightEstimation:o,hasAssessment:t,getAssessmentSummary:l}=vs(),x=o()||ys(),p=l();return!s.calories||s.calories<=0?null:e.jsxs("div",{className:`space-y-2 ${r}`,children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Estimativa:"}),e.jsx(Ns,{targetCalories:s.calories,userData:x}),e.jsx(ws,{})]}),t&&p?e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[e.jsx("span",{className:"text-snapfit-green",children:"📊"}),e.jsxs("span",{children:["Baseado na avaliação física de ",p.formattedDate,p.isRecent?e.jsx("span",{className:"text-snapfit-green ml-1",children:"(recente)"}):e.jsxs("span",{className:"text-yellow-400 ml-1",children:["(",p.daysSince," dias atrás)"]})]})]}):e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[e.jsx("span",{className:"text-yellow-400",children:"⚠️"}),e.jsxs("span",{children:["Baseado em dados padrão -",e.jsx("button",{className:"text-snapfit-green hover:underline ml-1",children:"faça uma avaliação física para maior precisão"})]})]})]})}const oe=[{id:"vitamin_d",name:"Vitamina D",unit:"μg",dailyValue:15,category:"vitamin",description:"Essencial para saúde óssea e função imunológica",sources:["Exposição solar","Peixes gordurosos","Ovos","Cogumelos"],deficiencySymptoms:["Fadiga","Dores ósseas","Fraqueza muscular","Depressão"]},{id:"vitamin_b12",name:"Vitamina B12",unit:"μg",dailyValue:2.4,category:"vitamin",description:"Crucial para função neurológica e formação de glóbulos vermelhos",sources:["Carnes","Peixes","Laticínios","Ovos"],deficiencySymptoms:["Fadiga","Anemia","Problemas neurológicos","Depressão"]},{id:"iron",name:"Ferro",unit:"mg",dailyValue:14,category:"mineral",description:"Essencial para transporte de oxigênio no sangue",sources:["Carnes vermelhas","Feijão","Espinafre","Quinoa"],deficiencySymptoms:["Fadiga","Anemia","Palidez","Falta de ar"]},{id:"magnesium",name:"Magnésio",unit:"mg",dailyValue:400,category:"mineral",description:"Importante para função muscular e óssea",sources:["Nozes","Sementes","Vegetais verdes","Grãos integrais"],deficiencySymptoms:["Cãibras","Fadiga","Irritabilidade","Insônia"]},{id:"zinc",name:"Zinco",unit:"mg",dailyValue:11,category:"mineral",description:"Essencial para sistema imunológico e cicatrização",sources:["Carnes","Frutos do mar","Sementes","Nozes"],deficiencySymptoms:["Imunidade baixa","Cicatrização lenta","Perda de apetite"]},{id:"vitamin_c",name:"Vitamina C",unit:"mg",dailyValue:90,category:"vitamin",description:"Antioxidante poderoso, essencial para colágeno",sources:["Frutas cítricas","Morango","Kiwi","Pimentão"],deficiencySymptoms:["Fadiga","Imunidade baixa","Sangramento gengival"]}],ta=s=>{switch(s){case"deficient":return"text-red-500";case"low":return"text-orange-500";case"adequate":return"text-green-500";case"high":return"text-yellow-500";case"toxic":return"text-red-600";default:return"text-gray-500"}},ra=s=>{switch(s){case"deficient":return"❌";case"low":return"⚠️";case"adequate":return"✅";case"high":return"🟡";case"toxic":return"🔴";default:return"❓"}},Fe=()=>{const s=_e(),{data:r,isLoading:o,error:t}=Y({queryKey:["micronutrients","analysis"],queryFn:async()=>{var g,u;console.log("🔄 Fetching nutritional analysis...");try{const n=await q.get("users/progress/nutritional_analysis");if((u=(g=n==null?void 0:n.data)==null?void 0:g.weekly_averages)!=null&&u.macronutrient_distribution){console.log("✅ Success with nutritional analysis endpoint",n.data);const a=n.data.weekly_averages.macronutrient_distribution,d=na(a);return{date:new Date().toISOString(),totalIntake:d,deficiencies:d.filter(m=>m.status==="deficient"),excesses:d.filter(m=>m.status==="excess"),recommendations:is(d),overallScore:ls(d),improvementAreas:["Vitamina D","Ferro","Vitamina B12"],macronutrients:a}}return console.log("⚠️ No nutritional data found, using mock data"),Xe()}catch(n){return console.error("❌ Failed to fetch nutritional analysis:",n),console.log("🔄 Using fallback mock data due to API error"),Xe()}},staleTime:1e3*60*10,refetchOnWindowFocus:!1,retry:1,throwOnError:!1}),{data:l=[],isLoading:x,error:p}=Y({queryKey:["micronutrients","bloodtests"],queryFn:async()=>{console.log("🔄 Fetching blood tests...");try{const g=await q.get("users/blood-tests");return g!=null&&g.data?(console.log("✅ Success with blood tests endpoint",g.data),Array.isArray(g.data)?g.data:[]):[]}catch(g){return console.error("❌ Failed to fetch blood tests:",g),[]}},staleTime:1e3*60*15,refetchOnWindowFocus:!1,retry:2,throwOnError:!1}),b=o||x;console.log("🔍 useMicronutrients debug:",{analysis:!!r,bloodTests:!!l,isLoadingAnalysis:o,isLoadingBloodTests:x,analysisError:!!t,bloodTestsError:!!p});const y=!r&&t||!l&&p,j=C.useMemo(()=>{if(!r||!r.totalIntake||!Array.isArray(r.totalIntake))return{total:0,adequate:0,deficient:0,adequatePercentage:0,criticalDeficiencies:[],overallScore:0,hasRecentBloodTest:!1};const g=r.totalIntake.length,u=r.totalIntake.filter(d=>d.status==="adequate").length,n=r.totalIntake.filter(d=>d.status==="deficient"||d.status==="low").length,a=r.totalIntake.filter(d=>d.status==="deficient").map(d=>{var m;return((m=oe.find(v=>v.id===d.micronutrientId))==null?void 0:m.name)||d.name}).filter(Boolean);return{total:g,adequate:u,deficient:n,adequatePercentage:g>0?Math.round(u/g*100):0,criticalDeficiencies:a,overallScore:r.overallScore||0,hasRecentBloodTest:l.length>0&&new Date(l[0].date)>new Date(Date.now()-90*24*60*60*1e3)}},[r,l]),c=G({mutationFn:async g=>{console.log("📤 Uploading blood test file:",g.name);const u=new FormData;return u.append("file",g),(await q.post("users/blood-tests/upload",u)).data},onSuccess:()=>{s.invalidateQueries({queryKey:["micronutrients","bloodtests"]}),s.invalidateQueries({queryKey:["micronutrients","analysis"]})}}),f=G({mutationFn:async g=>(console.log("💊 Generating supplement plan for:",g),(await q.post("users/supplements/plan",{recommendations:g})).data)});return{analysis:r,bloodTests:l,summary:j,isLoading:b,error:y,uploadBloodTest:c.mutateAsync,generateSupplementPlan:f.mutateAsync,isUploadingBloodTest:c.isPending,isGeneratingPlan:f.isPending,micronutrientsData:oe}};function na(s){const{calories:r,protein:o,carbs:t,fat:l}=s;return[{micronutrientId:"vitamin_d",name:"Vitamina D",currentIntake:Math.max(5,Math.min(15,r/200)),recommendedIntake:10,unit:"mcg",status:r>1800?"adequate":"deficient",percentage:Math.min(100,r/2e3*80)},{micronutrientId:"iron",name:"Ferro",currentIntake:Math.max(8,Math.min(18,o/10)),recommendedIntake:14,unit:"mg",status:o>100?"adequate":"low",percentage:Math.min(100,o/120*90)},{micronutrientId:"vitamin_b12",name:"Vitamina B12",currentIntake:Math.max(1.5,Math.min(3,o/50)),recommendedIntake:2.4,unit:"mcg",status:o>80?"adequate":"deficient",percentage:Math.min(100,o/100*85)},{micronutrientId:"calcium",name:"Cálcio",currentIntake:Math.max(600,Math.min(1200,r*.5)),recommendedIntake:1e3,unit:"mg",status:r>2e3?"adequate":"low",percentage:Math.min(100,r/2200*95)},{micronutrientId:"vitamin_c",name:"Vitamina C",currentIntake:Math.max(40,Math.min(120,t/2)),recommendedIntake:90,unit:"mg",status:t>150?"adequate":"low",percentage:Math.min(100,t/180*88)},{micronutrientId:"omega_3",name:"Ômega-3",currentIntake:Math.max(.8,Math.min(2.5,l/30)),recommendedIntake:1.6,unit:"g",status:l>50?"adequate":"deficient",percentage:Math.min(100,l/60*92)}]}function is(s){return s.filter(t=>t.status==="deficient"||t.status==="low").map(t=>({micronutrientId:t.micronutrientId,recommendation:`Aumentar consumo de ${t.name}`,foods:ia(t.micronutrientId),priority:t.status==="deficient"?"high":"medium"}))}function ia(s){return{vitamin_d:["Salmão","Sardinha","Ovos","Cogumelos"],iron:["Carne vermelha","Feijão","Espinafre","Lentilha"],vitamin_b12:["Carne","Peixe","Ovos","Laticínios"],calcium:["Leite","Queijo","Iogurte","Brócolis"],vitamin_c:["Laranja","Morango","Kiwi","Pimentão"],omega_3:["Salmão","Sardinha","Nozes","Linhaça"]}[s]||["Alimentos variados"]}function ls(s){const r=s.reduce((o,t)=>o+t.percentage,0);return Math.round(r/s.length)}function Xe(){const s=[{micronutrientId:"vitamin_d",name:"Vitamina D",currentIntake:8,recommendedIntake:10,unit:"mcg",status:"low",percentage:80},{micronutrientId:"iron",name:"Ferro",currentIntake:12,recommendedIntake:14,unit:"mg",status:"adequate",percentage:86},{micronutrientId:"vitamin_b12",name:"Vitamina B12",currentIntake:1.8,recommendedIntake:2.4,unit:"mcg",status:"deficient",percentage:75}];return{date:new Date().toISOString(),totalIntake:s,deficiencies:s.filter(r=>r.status==="deficient"),excesses:[],recommendations:is(s),overallScore:ls(s),improvementAreas:["Vitamina D","Vitamina B12"],macronutrients:{calories:0,protein:0,carbs:0,fat:0}}}function la({data:s}){const x=s.map(a=>{const d=oe.find(m=>m.id===a.micronutrientId);return{name:(d==null?void 0:d.name)||a.micronutrientId,value:Math.min(a.percentage,200),status:a.status}}),p=2*Math.PI/x.length,b=(a,d)=>{const m=150+d*Math.cos(a-Math.PI/2),v=150+d*Math.sin(a-Math.PI/2);return{x:m,y:v}},y=x.map((a,d)=>{const m=d*p,v=a.value/100*120;return b(m,v)}),j=`M ${y.map(a=>`${a.x},${a.y}`).join(" L ")} Z`,c=Array.from({length:5},(a,d)=>{const m=(d+1)/5*120;return e.jsx("circle",{cx:150,cy:150,r:m,fill:"none",stroke:"rgba(34, 197, 94, 0.2)",strokeWidth:"1"},d)}),f=x.map((a,d)=>{const m=d*p,v=b(m,120);return e.jsx("line",{x1:150,y1:150,x2:v.x,y2:v.y,stroke:"rgba(34, 197, 94, 0.2)",strokeWidth:"1"},d)}),g=x.map((a,d)=>{const m=d*p,F=b(m,140);return e.jsx("text",{x:F.x,y:F.y,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs fill-gray-300",fontSize:"11",children:a.name},d)}),u=Array.from({length:5},(a,d)=>{const m=(d+1)/5*100,v=(d+1)/5*120;return e.jsxs("text",{x:155,y:150-v,className:"text-xs fill-gray-400",fontSize:"10",children:[m,"%"]},d)}),n=y.map((a,d)=>{const m=x[d],v=m.status==="adequate"?"#22c55e":m.status==="low"?"#f59e0b":m.status==="deficient"?"#ef4444":m.status==="high"?"#eab308":"#dc2626";return e.jsxs("g",{children:[e.jsx("circle",{cx:a.x,cy:a.y,r:"4",fill:v,stroke:"white",strokeWidth:"2"}),e.jsx("circle",{cx:a.x,cy:a.y,r:"8",fill:"transparent",className:"cursor-pointer",children:e.jsx("title",{children:`${m.name}: ${m.value.toFixed(1)}%`})})]},d)});return e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsxs("svg",{width:300,height:300,className:"overflow-visible",children:[c,f,e.jsx("path",{d:j,fill:"rgba(34, 197, 94, 0.1)",stroke:"rgba(34, 197, 94, 0.6)",strokeWidth:"2"}),n,g,u]}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Adequado (≥80%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Alto (>120%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Baixo (50-80%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Deficiente (<50%)"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-md text-center",children:[e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-green-400",children:x.filter(a=>a.status==="adequate").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Adequados"})]}),e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-orange-400",children:x.filter(a=>a.status==="low").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Baixos"})]}),e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-red-400",children:x.filter(a=>a.status==="deficient").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Deficientes"})]}),e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-yellow-400",children:x.filter(a=>a.status==="high").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Altos"})]})]})]})}function Ye(){const{analysis:s,summary:r,isLoading:o,error:t}=Fe();return o?e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded"}),e.jsx("div",{className:"space-y-2",children:[1,2,3].map(l=>e.jsx("div",{className:"h-16 bg-gray-600 rounded"},l))})]})}):t||!s||!r?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(ne,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Erro ao carregar análise"}),e.jsx("p",{className:"text-gray-400",children:t||"Dados não disponíveis"})]}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Análise de Micronutrientes"}),e.jsx(ks,{title:"Base Científica da Análise",description:"A análise de micronutrientes segue as Ingestões Diárias Recomendadas (IDR) da ANVISA e diretrizes internacionais da OMS para avaliação nutricional."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center",children:e.jsx(he,{className:"w-5 h-5 text-green-400"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:r.adequate}),e.jsx("div",{className:"text-sm text-gray-400",children:"Adequados"})]})]})}),e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center",children:e.jsx(ne,{className:"w-5 h-5 text-orange-400"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-orange-400",children:r.deficient}),e.jsx("div",{className:"text-sm text-gray-400",children:"Precisam atenção"})]})]})}),e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center",children:e.jsx(le,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:r.overallScore}),e.jsx("div",{className:"text-sm text-gray-400",children:"Score geral"})]})]})})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx("span",{children:"📊"}),"Visão Geral dos Micronutrientes"]}),e.jsx(la,{data:s.totalIntake})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx("span",{children:"📋"}),"Análise Detalhada"]}),e.jsx("div",{className:"space-y-4",children:s.totalIntake.map(l=>{const x=oe.find(p=>p.id===l.micronutrientId);return x?e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-lg",children:ra(l.status)}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:x.name}),e.jsx("p",{className:"text-sm text-gray-400",children:x.description})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`text-sm font-medium ${ta(l.status)}`,children:[l.percentage,"% da IDR"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[l.amount.toFixed(1)," ",x.unit]})]})]}),e.jsx("div",{className:"mb-3",children:e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${l.status==="adequate"?"bg-green-500":l.status==="low"?"bg-orange-500":l.status==="deficient"?"bg-red-500":l.status==="high"?"bg-yellow-500":"bg-red-600"}`,style:{width:`${Math.min(l.percentage,100)}%`}})})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Principais fontes:"}),e.jsx("div",{className:"text-gray-300",children:x.sources.slice(0,3).join(", ")})]}),(l.status==="deficient"||l.status==="low")&&e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Sintomas de deficiência:"}),e.jsx("div",{className:"text-orange-300",children:x.deficiencySymptoms.slice(0,2).join(", ")})]})]})]},l.micronutrientId):null})})]}),s.improvementAreas.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Cs,{className:"w-5 h-5 text-snapfit-green"}),"Áreas de Melhoria"]}),e.jsx("div",{className:"space-y-2",children:s.improvementAreas.map((l,x)=>e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("span",{className:"text-snapfit-green",children:"•"}),e.jsx("span",{className:"text-gray-300",children:l})]},x))})]}),e.jsx(Ss,{context:"análise de micronutrientes"})]})}function oa(){const{bloodTests:s,isLoading:r,uploadBloodTest:o}=Fe(),[t,l]=C.useState(!1),[x,p]=C.useState(!1),b=async g=>{if(!g.type.includes("pdf")&&!g.type.includes("image")){alert("Por favor, envie apenas arquivos PDF ou imagens");return}try{p(!0),await o(g)}catch(u){console.error("Erro no upload:",u)}finally{p(!1)}},y=g=>{g.preventDefault(),l(!1);const u=Array.from(g.dataTransfer.files);u.length>0&&b(u[0])},j=g=>{const u=g.target.files;u&&u.length>0&&b(u[0])},c=g=>{switch(g){case"low":return e.jsx(Ee,{className:"w-4 h-4 text-red-400"});case"high":return e.jsx(le,{className:"w-4 h-4 text-orange-400"});default:return e.jsx(he,{className:"w-4 h-4 text-green-400"})}},f=g=>{switch(g){case"low":return"text-red-400";case"high":return"text-orange-400";default:return"text-green-400"}};return r?e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded"})]})}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(qe,{className:"w-5 h-5 text-snapfit-green"}),"Upload de Exames"]}),e.jsx("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${t?"border-snapfit-green bg-snapfit-green/5":"border-gray-600 hover:border-snapfit-green/50"}`,onDrop:y,onDragOver:g=>g.preventDefault(),onDragEnter:()=>l(!0),onDragLeave:()=>l(!1),children:x?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto animate-pulse",children:e.jsx(Te,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Analisando exame com IA..."}),e.jsx("div",{className:"text-sm text-gray-400",children:"Isso pode levar alguns segundos"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(qe,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Arraste seus exames aqui ou clique para selecionar"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suportamos PDF e imagens (JPG, PNG)"})]}),e.jsx("input",{type:"file",accept:".pdf,image/*",onChange:j,className:"hidden",id:"file-upload"}),e.jsxs("label",{htmlFor:"file-upload",className:"inline-flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors cursor-pointer",children:[e.jsx(Se,{className:"w-4 h-4"}),"Selecionar Arquivo"]})]})}),e.jsx("div",{className:"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(Te,{className:"w-5 h-5 text-blue-400 mt-0.5"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("div",{className:"text-blue-400 font-medium mb-1",children:"IA Avançada"}),e.jsx("div",{className:"text-gray-300",children:"Nossa IA analisa automaticamente seus exames e identifica valores relacionados aos micronutrientes, correlacionando com sua alimentação atual para gerar recomendações personalizadas."})]})]})})]}),s.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Se,{className:"w-5 h-5 text-snapfit-green"}),"Histórico de Exames"]}),e.jsx("div",{className:"space-y-4",children:s.map(g=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ze,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-white font-medium",children:new Date(g.date).toLocaleDateString("pt-BR")}),g.analyzedByAI&&e.jsx("span",{className:"px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full",children:"Analisado por IA"})]}),g.uploadedFile&&e.jsx("span",{className:"text-xs text-gray-400",children:g.uploadedFile})]}),e.jsx("div",{className:"grid gap-3",children:g.results.map(u=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-dark-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[c(u.status),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:u.testName}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Referência: ",u.referenceRange.min," - ",u.referenceRange.max," ",u.unit]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`font-medium ${f(u.status)}`,children:[u.value," ",u.unit]}),e.jsx("div",{className:"text-xs text-gray-400 capitalize",children:u.status==="normal"?"Normal":u.status==="low"?"Baixo":"Alto"})]})]},u.id))}),g.notes&&e.jsxs("div",{className:"mt-3 p-3 bg-gray-700/50 rounded text-sm text-gray-300",children:[e.jsx("strong",{children:"Observações:"})," ",g.notes]})]},g.id))})]}),s.length===0&&!r&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(Se,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum exame encontrado"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Faça upload dos seus exames de sangue para uma análise mais precisa dos micronutrientes"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsx("div",{children:"✓ Análise automática por IA"}),e.jsx("div",{children:"✓ Correlação com sua alimentação"}),e.jsx("div",{children:"✓ Recomendações personalizadas"})]})]})]})}function ca(){const{analysis:s,generateSupplementPlan:r,isLoading:o}=Fe(),[t,l]=C.useState([]),[x,p]=C.useState(null),[b,y]=C.useState(!1),j=a=>{l(d=>d.includes(a)?d.filter(m=>m!==a):[...d,a])},c=async()=>{if(t.length!==0)try{y(!0);const a=await r(t);p(a)}catch(a){console.error("Erro ao gerar plano:",a)}finally{y(!1)}},f=a=>{switch(a){case"high":return"text-red-400 bg-red-500/20";case"medium":return"text-yellow-400 bg-yellow-500/20";case"low":return"text-green-400 bg-green-500/20"}},g=a=>{switch(a){case"high":return"Alta";case"medium":return"Média";case"low":return"Baixa"}},u=a=>{switch(a){case"morning":return"🌅";case"afternoon":return"☀️";case"evening":return"🌙";case"with_meal":return"🍽️";case"empty_stomach":return"⏰"}},n=a=>{switch(a){case"morning":return"Manhã";case"afternoon":return"Tarde";case"evening":return"Noite";case"with_meal":return"Com refeição";case"empty_stomach":return"Estômago vazio"}};return o?e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),[1,2,3].map(a=>e.jsx("div",{className:"h-32 bg-gray-600 rounded"},a))]})}):!s||s.recommendations.length===0?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(Me,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhuma recomendação"}),e.jsx("p",{className:"text-gray-400",children:"Seus níveis de micronutrientes estão adequados! Continue com sua alimentação balanceada."})]}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-2 flex items-center gap-2",children:[e.jsx(Me,{className:"w-5 h-5 text-snapfit-green"}),"Recomendações de Suplementos"]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Baseado na sua análise nutricional e exames de sangue, recomendamos os seguintes suplementos:"})]}),e.jsx("div",{className:"space-y-4",children:s.recommendations.map(a=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("input",{type:"checkbox",checked:t.includes(a.id),onChange:()=>j(a.id),className:"mt-1 w-4 h-4 text-snapfit-green bg-gray-700 border-gray-600 rounded focus:ring-snapfit-green"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h4",{className:"text-white font-medium",children:a.name}),e.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${f(a.priority)}`,children:g(a.priority)})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:a.reason})]})]}),a.cost&&e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:["R$ ",a.cost.toFixed(2)]}),e.jsx("div",{className:"text-xs text-gray-400",children:"por mês"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(Me,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-gray-400",children:"Dosagem:"}),e.jsxs("span",{className:"text-white",children:[a.dosage," ",a.unit]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(pe,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-gray-400",children:"Horário:"}),e.jsxs("span",{className:"text-white",children:[u(a.timing)," ",n(a.timing)]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Duração:"}),e.jsx("span",{className:"text-white",children:a.duration})]})]}),(a.interactions.length>0||a.sideEffects.length>0)&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[a.interactions.length>0&&e.jsxs("div",{children:[e.jsxs("div",{className:"text-yellow-400 mb-1 flex items-center gap-1",children:[e.jsx(ne,{className:"w-3 h-3"}),"Interações:"]}),e.jsx("ul",{className:"text-gray-300 space-y-1",children:a.interactions.map((d,m)=>e.jsxs("li",{className:"text-xs",children:["• ",d]},m))})]}),a.sideEffects.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Efeitos colaterais:"}),e.jsx("ul",{className:"text-gray-300 space-y-1",children:a.sideEffects.map((d,m)=>e.jsxs("li",{className:"text-xs",children:["• ",d]},m))})]})]})]},a.id))}),t.length>0&&!x&&e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"text-white font-medium mb-1",children:[t.length," suplemento(s) selecionado(s)"]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Gere um plano personalizado com cronograma e estimativa de custos"})]}),e.jsx("button",{onClick:c,disabled:b,className:"flex items-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"}),"Gerando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ie,{className:"w-4 h-4"}),"Gerar Plano"]})})]})}),x&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(he,{className:"w-5 h-5 text-green-400"}),"Seu Plano de Suplementação"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:["R$ ",x.totalCost.toFixed(2)]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Custo total mensal"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:x.duration}),e.jsx("div",{className:"text-sm text-gray-400",children:"Duração recomendada"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:x.recommendations.length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suplementos"})]})]}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsx("h4",{className:"text-white font-medium",children:"Melhorias esperadas:"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:x.expectedImprovements.map((a,d)=>e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-300",children:[e.jsx(he,{className:"w-4 h-4 text-green-400"}),a]},d))})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(Js,{className:"w-4 h-4"}),"Comprar Suplementos"]}),e.jsx("button",{onClick:()=>p(null),className:"px-4 py-3 text-gray-400 border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors",children:"Modificar"})]})]})]})}function da(){const[s,r]=C.useState("3months"),[o,t]=C.useState("all"),{data:l,isLoading:x,error:p}=Y({queryKey:["micronutrients","evolution",s],queryFn:async()=>(await q.get(`users/micronutrients/evolution?period=${s}`)).data,staleTime:1e3*60*10}),y=l||{vitamin_d:{timeline:[{date:"2024-01-01",value:8.5,percentage:57},{date:"2024-01-15",value:12.2,percentage:81},{date:"2024-02-01",value:15.8,percentage:105},{date:"2024-02-15",value:18.3,percentage:122}],trend:"improving",targetReached:!0},vitamin_b12:{timeline:[{date:"2024-01-01",value:1.2,percentage:50},{date:"2024-01-15",value:1.8,percentage:75},{date:"2024-02-01",value:2.1,percentage:88},{date:"2024-02-15",value:2.4,percentage:100}],trend:"improving",targetReached:!0},iron:{timeline:[{date:"2024-01-01",value:12.8,percentage:91},{date:"2024-01-15",value:13.2,percentage:94},{date:"2024-02-01",value:12.5,percentage:89},{date:"2024-02-15",value:13.8,percentage:99}],trend:"stable",targetReached:!1}},j=[{value:"1month",label:"1 mês"},{value:"3months",label:"3 meses"},{value:"6months",label:"6 meses"},{value:"1year",label:"1 ano"}];if(x)return e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),e.jsx("div",{className:"h-32 bg-gray-600 rounded"}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[1,2,3].map(n=>e.jsx("div",{className:"h-64 bg-gray-600 rounded"},n))})]})});if(p)return e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(Ee,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Erro ao carregar evolução"}),e.jsx("p",{className:"text-gray-400",children:"Não foi possível carregar os dados de evolução nutricional."})]});const c=n=>{switch(n){case"improving":return e.jsx(le,{className:"w-4 h-4 text-green-400"});case"declining":return e.jsx(Ee,{className:"w-4 h-4 text-red-400"});default:return e.jsx("div",{className:"w-4 h-4 bg-yellow-400 rounded-full"})}},f=n=>{switch(n){case"improving":return"text-green-400";case"declining":return"text-red-400";default:return"text-yellow-400"}},g=n=>{switch(n){case"improving":return"Melhorando";case"declining":return"Piorando";default:return"Estável"}},u=n=>{const a=Math.max(...n.timeline.map(m=>m.percentage)),d=n.timeline.map((m,v)=>{const F=v/(n.timeline.length-1)*300,P=100-m.percentage/a*80;return`${F},${P}`}).join(" ");return e.jsxs("svg",{width:"300",height:"100",className:"w-full h-24",children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[e.jsx("stop",{offset:"0%",stopColor:"rgba(34, 197, 94, 0.3)"}),e.jsx("stop",{offset:"100%",stopColor:"rgba(34, 197, 94, 0.1)"})]})}),e.jsx("line",{x1:"0",y1:"20",x2:"300",y2:"20",stroke:"rgba(75, 85, 99, 0.3)",strokeWidth:"1"}),e.jsx("line",{x1:"0",y1:"50",x2:"300",y2:"50",stroke:"rgba(75, 85, 99, 0.3)",strokeWidth:"1"}),e.jsx("line",{x1:"0",y1:"80",x2:"300",y2:"80",stroke:"rgba(75, 85, 99, 0.3)",strokeWidth:"1"}),e.jsx("polygon",{points:`0,100 ${d} 300,100`,fill:"url(#gradient)"}),e.jsx("polyline",{points:d,fill:"none",stroke:"rgb(34, 197, 94)",strokeWidth:"2"}),n.timeline.map((m,v)=>{const F=v/(n.timeline.length-1)*300,P=100-m.percentage/a*80;return e.jsx("circle",{cx:F,cy:P,r:"3",fill:"rgb(34, 197, 94)",stroke:"white",strokeWidth:"2"},v)})]})};return e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(le,{className:"w-5 h-5 text-snapfit-green"}),"Evolução Temporal"]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx("select",{value:s,onChange:n=>r(n.target.value),className:"px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white text-sm focus:ring-1 focus:ring-snapfit-green",children:j.map(n=>e.jsx("option",{value:n.value,children:n.label},n.value))}),e.jsxs("select",{value:o,onChange:n=>t(n.target.value),className:"px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white text-sm focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"all",children:"Todos os micronutrientes"}),oe.map(n=>e.jsx("option",{value:n.id,children:n.name},n.id))]}),e.jsxs("button",{className:"flex items-center gap-2 px-3 py-2 text-sm text-snapfit-green border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/10 transition-colors",children:[e.jsx(ns,{className:"w-4 h-4"}),"Exportar"]})]})]})}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:Object.entries(y).map(([n,a])=>{const d=oe.find(P=>P.id===n);if(!d)return null;const m=a.timeline[a.timeline.length-1],v=a.timeline[0],F=m.percentage-v.percentage;return e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:d.name}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[c(a.trend),e.jsx("span",{className:f(a.trend),children:g(a.trend)}),a.targetReached&&e.jsx("span",{className:"px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full",children:"Meta atingida"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-white font-medium",children:[m.percentage,"%"]}),e.jsxs("div",{className:`text-sm ${F>=0?"text-green-400":"text-red-400"}`,children:[F>=0?"+":"",F.toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-4",children:u(a)}),e.jsx("div",{className:"space-y-2",children:a.timeline.slice(-3).map((P,Q)=>e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:new Date(P.date).toLocaleDateString("pt-BR")}),e.jsxs("span",{className:"text-white",children:[P.value.toFixed(1)," ",d.unit," (",P.percentage,"%)"]})]},Q))})]},n)})}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(es,{className:"w-5 h-5 text-snapfit-green"}),"Resumo do Progresso"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:"2"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Metas atingidas"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:"1"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Em progresso"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:"+28%"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Melhoria média"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:"45"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Dias de acompanhamento"})]})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"💡 Insights da Evolução"}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-green-400",children:"✓"}),e.jsx("span",{className:"text-gray-300",children:"Sua Vitamina D melhorou 65% desde o início da suplementação"})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-green-400",children:"✓"}),e.jsx("span",{className:"text-gray-300",children:"B12 atingiu níveis adequados após 6 semanas de suplementação"})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-yellow-400",children:"⚠"}),e.jsx("span",{className:"text-gray-300",children:"Ferro mantém-se estável, considere aumentar fontes alimentares"})]})]})]})]})}const ma=[{id:"analysis",label:"Micronutrientes",icon:Ms,description:"Análise detalhada dos micronutrientes"},{id:"bloodtests",label:"Exames",icon:qe,description:"Upload e análise de exames de sangue"},{id:"supplements",label:"Suplementos",icon:Me,description:"Recomendações personalizadas"},{id:"evolution",label:"Evolução",icon:le,description:"Acompanhamento temporal"}];function xa({isOpen:s,onClose:r,initialTab:o="analysis"}){const[t,l]=C.useState(o);if(!s)return null;const x=()=>{switch(t){case"analysis":return e.jsx(Ye,{});case"bloodtests":return e.jsx(oa,{});case"supplements":return e.jsx(ca,{});case"evolution":return e.jsx(da,{});default:return e.jsx(Ye,{})}};return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ue,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Análise Nutricional Completa"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Micronutrientes, exames e recomendações personalizadas"})]})]}),e.jsx("button",{onClick:r,className:"p-2 hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsx("div",{className:"border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex overflow-x-auto",children:ma.map(p=>{const b=p.icon,y=t===p.id;return e.jsxs("button",{onClick:()=>l(p.id),className:`flex items-center gap-2 px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200 ${y?"text-snapfit-green border-snapfit-green bg-snapfit-green/5":"text-gray-400 border-transparent hover:text-white hover:bg-snapfit-green/5"}`,children:[e.jsx(b,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline",children:p.label}),e.jsx("span",{className:"sm:hidden",children:p.label.split(" ")[0]})]},p.id)})})}),e.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-140px)]",children:x()})]})})}function ga({onOpenAnalysis:s,className:r=""}){const{summary:o,isLoading:t,error:l}=Fe(),[x,p]=B.useState(!1);if(console.log("🔍 MicronutrientsCard debug:",{summary:!!o,isLoading:t,error:!!l,summaryType:typeof o}),B.useEffect(()=>{if(t){const j=setTimeout(()=>{p(!0)},1e4);return()=>clearTimeout(j)}else p(!1)},[t]),t&&!x)return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 ${r}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center animate-pulse",children:e.jsx(ue,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-600 rounded animate-pulse mb-2"}),e.jsx("div",{className:"h-3 bg-gray-700 rounded animate-pulse w-2/3"})]})]})});if(t&&x)return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-yellow-500/20 ${r}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ne,{className:"w-5 h-5 text-yellow-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm text-yellow-400 font-medium",children:"Carregamento demorado"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Usando dados de fallback..."})]})]})});if(l&&!o)return e.jsxs("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-red-500/20 ${r}`,children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ne,{className:"w-5 h-5 text-red-400"}),e.jsx("div",{className:"text-sm text-red-400",children:"Erro ao carregar análise nutricional"})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:l instanceof Error?l.message:"Erro desconhecido"})]});if(!o)return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-gray-500/20 ${r}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-500/20 rounded-full flex items-center justify-center",children:e.jsx(ue,{className:"w-5 h-5 text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm text-gray-400 font-medium",children:"Análise Nutricional"}),e.jsx("div",{className:"text-xs text-gray-500",children:"Dados não disponíveis"})]})]})});const b=j=>j>=80?"text-green-400":j>=60?"text-yellow-400":"text-red-400",y=j=>j>=80?"🎯":j>=60?"⚠️":"🔴";return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 hover:border-snapfit-green/40 transition-all duration-200 ${r}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ue,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Análise Nutricional"}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-400",children:[e.jsxs("span",{className:b(o.overallScore),children:[y(o.overallScore)," Score: ",o.overallScore,"/100"]}),o.hasRecentBloodTest&&e.jsx("span",{className:"text-snapfit-green",children:"• Exame recente"})]})]}),e.jsx(Ps,{title:"Base Científica da Análise",description:"A análise de micronutrientes é baseada nas Ingestões Diárias Recomendadas (IDR) estabelecidas pela ANVISA e diretrizes internacionais da OMS."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Micronutrientes adequados:"}),e.jsxs("span",{className:"text-green-400 font-medium",children:[o.adequate,"/",o.total," (",o.adequatePercentage,"%)"]})]}),o.criticalDeficiencies.length>0&&e.jsxs("div",{className:"flex items-start gap-2 text-sm",children:[e.jsx(ne,{className:"w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-orange-400",children:"Precisam atenção: "}),e.jsx("span",{className:"text-gray-300",children:o.criticalDeficiencies.join(", ")})]})]}),o.deficient===0&&e.jsxs("div",{className:"flex items-center gap-2 text-sm text-green-400",children:[e.jsx(he,{className:"w-4 h-4"}),e.jsx("span",{children:"Todos os micronutrientes em níveis adequados!"})]})]}),e.jsx("div",{children:e.jsxs("button",{onClick:s,className:"w-full flex items-center justify-center gap-2 px-4 py-2 text-sm text-white bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-all duration-200",children:[e.jsx(le,{className:"w-4 h-4"}),e.jsx("span",{children:"Ver Análise Completa"})]})}),e.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[e.jsx("span",{className:"text-gray-500",children:"Status rápido:"}),e.jsxs("div",{className:"flex gap-1",children:[o.criticalDeficiencies.slice(0,3).map((j,c)=>e.jsx("span",{className:"text-red-400",children:"❌"},c)),Array.from({length:Math.min(3,o.adequate)}).map((j,c)=>e.jsx("span",{className:"text-green-400",children:"✅"},`adequate-${c}`)),o.total>6&&e.jsxs("span",{className:"text-gray-400",children:["+",o.total-6]})]})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2 opacity-75",children:"📊 Dados demonstrativos - Análise completa em breve"}),e.jsx(Ds,{})]})})}function ua({date_start:s,date_end:r,onReuseProtocol:o,showProtocolHistory:t=!1}){const l=as(),[x,p]=C.useState(null),[b,y]=C.useState(!1),j=u=>{p(u.id),y(!0)},c=(u,n)=>{n?l(`/dashboard/diet/edit-protocol/${u.id}`):o&&o({...u,edit:n})},f=u=>{console.log("🔄 Duplicando protocolo:",u),o&&o({...u,edit:!1})},g=u=>{console.log("🏁 Protocolo finalizado:",u)};return t?e.jsxs(e.Fragment,{children:[e.jsx(As,{protocolType:"diet",onProtocolSelect:j,onProtocolDuplicate:f,onProtocolFinish:g}),x&&e.jsx(Qs,{protocolId:x,type:"diet",isOpen:b,onClose:()=>{y(!1),p(null)},onReuseProtocol:c})]}):e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ze,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Protocolos"})]}),e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ee,{className:"w-12 h-12 mx-auto mb-4 text-gray-400 opacity-50"}),e.jsx("p",{className:"text-gray-400 mb-4",children:'Para ver o histórico completo de protocolos, use a seção "Histórico" nas ferramentas de análise.'}),e.jsx("button",{onClick:()=>l("/dashboard/diet"),className:"px-4 py-2 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors",children:"Ir para Histórico Completo"})]})]})}function ha({filters:s,onFiltersChange:r,onClose:o}){var j;const[t,l]=C.useState(s),x=[{value:"breakfast",label:"Café da Manhã"},{value:"lunch",label:"Almoço"},{value:"dinner",label:"Jantar"},{value:"snack",label:"Lanche"},{value:"dessert",label:"Sobremesa"},{value:"drink",label:"Bebida"}],p=[{value:"easy",label:"Fácil"},{value:"medium",label:"Médio"},{value:"hard",label:"Difícil"}],b=()=>{r(t),o&&o()},y=()=>{const c={limit:20};l(c),r(c)};return e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[e.jsx(ts,{className:"w-5 h-5"}),"Filtros de Busca"]}),o&&e.jsx("button",{onClick:o,className:"p-1 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Buscar por nome ou descrição"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:t.search||"",onChange:c=>l({...t,search:c.target.value}),placeholder:"Digite o nome da receita...",className:"w-full pl-10 pr-4 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"}),e.jsx($e,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Categoria"}),e.jsxs("select",{value:t.category||"",onChange:c=>l({...t,category:c.target.value||void 0}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"",children:"Todas as categorias"}),x.map(c=>e.jsx("option",{value:c.value,children:c.label},c.value))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Dificuldade"}),e.jsxs("select",{value:t.difficulty||"",onChange:c=>l({...t,difficulty:c.target.value||void 0}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"",children:"Todas as dificuldades"}),p.map(c=>e.jsx("option",{value:c.value,children:c.label},c.value))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Tempo máximo de preparo (minutos)"}),e.jsx("input",{type:"number",value:t.maxPrepTime||"",onChange:c=>l({...t,maxPrepTime:c.target.value?Number(c.target.value):void 0}),placeholder:"Ex: 30",min:"0",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Avaliação mínima"}),e.jsxs("select",{value:t.minRating||"",onChange:c=>l({...t,minRating:c.target.value?Number(c.target.value):void 0}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"",children:"Qualquer avaliação"}),e.jsx("option",{value:"4",children:"4+ estrelas"}),e.jsx("option",{value:"3",children:"3+ estrelas"}),e.jsx("option",{value:"2",children:"2+ estrelas"}),e.jsx("option",{value:"1",children:"1+ estrela"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Tags (separadas por vírgula)"}),e.jsx("input",{type:"text",value:((j=t.tags)==null?void 0:j.join(", "))||"",onChange:c=>l({...t,tags:c.target.value?c.target.value.split(",").map(f=>f.trim()).filter(Boolean):void 0}),placeholder:"Ex: proteína, baixa gordura, vegetariano",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Tipo de receita"}),e.jsxs("select",{value:t.isPublic===void 0?"all":t.isPublic?"public":"private",onChange:c=>{const f=c.target.value;l({...t,isPublic:f==="all"?void 0:f==="public"})},className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"all",children:"Todas as receitas"}),e.jsx("option",{value:"public",children:"Receitas públicas"}),e.jsx("option",{value:"private",children:"Minhas receitas"})]})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{onClick:b,className:"flex-1 bg-snapfit-green text-white py-2 px-4 rounded-lg hover:bg-snapfit-green/80 transition-colors",children:"Aplicar Filtros"}),e.jsx("button",{onClick:y,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Limpar"})]})]})}function pa({recipe:s,onClose:r,onAddToFavorites:o,onRemoveFromFavorites:t,onRate:l,isFavorite:x=!1,isAddingToFavorites:p=!1,isRemovingFromFavorites:b=!1,isRating:y=!1}){const[j,c]=C.useState(!1),[f,g]=C.useState(!1),[u,n]=C.useState(0),[a,d]=C.useState(""),m=k=>{switch(k){case"easy":return"text-green-400";case"medium":return"text-yellow-400";case"hard":return"text-red-400";default:return"text-gray-400"}},v=k=>{switch(k){case"easy":return"Fácil";case"medium":return"Médio";case"hard":return"Difícil";default:return k}},F=k=>({breakfast:"Café da Manhã",lunch:"Almoço",dinner:"Jantar",snack:"Lanche",dessert:"Sobremesa",drink:"Bebida"})[k]||k,P=()=>{u>0&&l&&(l(s.id,u,a||void 0),g(!1),n(0),d(""))},Q=()=>{x&&t?t(s):!x&&o&&o(s.id)},I={calories:Math.round(s.totalCalories/s.servings),protein:Math.round(s.totalProtein/s.servings*10)/10,carbs:Math.round(s.totalCarbs/s.servings*10)/10,fat:Math.round(s.totalFat/s.servings*10)/10,fiber:s.totalFiber?Math.round(s.totalFiber/s.servings*10)/10:0,sugar:s.totalSugar?Math.round(s.totalSugar/s.servings*10)/10:0,sodium:s.totalSodium?Math.round(s.totalSodium/s.servings*10)/10:0};return e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[e.jsx("div",{className:"sticky top-0 bg-snapfit-dark-gray border-b border-snapfit-green/20 p-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:s.name}),e.jsx("p",{className:"text-gray-400 mb-4",children:s.description}),e.jsxs("div",{className:"flex items-center gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(pe,{className:"w-4 h-4 text-gray-400"}),e.jsxs("span",{className:"text-gray-300",children:[s.prepTime," min"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(rs,{className:"w-4 h-4 text-gray-400"}),e.jsxs("span",{className:"text-gray-300",children:[s.servings," porções"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Gs,{className:`w-4 h-4 ${m(s.difficulty)}`}),e.jsx("span",{className:m(s.difficulty),children:v(s.difficulty)})]}),e.jsx("span",{className:"px-2 py-1 bg-snapfit-green/10 text-snapfit-green rounded-full text-xs",children:F(s.category)}),s.averageRating>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ie,{className:"w-4 h-4 text-yellow-400"}),e.jsx("span",{className:"text-gray-300",children:s.averageRating.toFixed(1)}),e.jsxs("span",{className:"text-gray-500",children:["(",s.ratingsCount,")"]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[e.jsx("button",{onClick:Q,disabled:p||b,className:`p-2 rounded-full transition-colors disabled:opacity-50 ${x?"bg-red-500/20 text-red-400 hover:bg-red-500/30":"bg-snapfit-green/20 text-snapfit-green hover:bg-snapfit-green/30"}`,title:x?"Remover dos favoritos":"Adicionar aos favoritos",children:e.jsx(De,{className:`w-5 h-5 ${x?"fill-current":""}`})}),e.jsx("button",{onClick:()=>g(!0),className:"p-2 bg-yellow-500/20 text-yellow-400 rounded-full hover:bg-yellow-500/30 transition-colors",title:"Avaliar receita",children:e.jsx(ie,{className:"w-5 h-5"})}),e.jsx("button",{onClick:()=>{var k;return(k=navigator.share)==null?void 0:k.call(navigator,{title:s.name,text:s.description})},className:"p-2 bg-blue-500/20 text-blue-400 rounded-full hover:bg-blue-500/30 transition-colors",title:"Compartilhar",children:e.jsx(Fs,{className:"w-5 h-5"})}),e.jsx("button",{onClick:r,className:"p-2 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Informações Nutricionais"}),e.jsxs("button",{onClick:()=>c(!j),className:"flex items-center gap-2 text-snapfit-green hover:text-snapfit-green/80 transition-colors",children:[e.jsx(Rs,{className:"w-4 h-4"}),j?"Ocultar detalhes":"Ver detalhes"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:I.calories}),e.jsx("div",{className:"text-sm text-gray-400",children:"Calorias"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:[I.protein,"g"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Proteínas"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-green-500",children:[I.carbs,"g"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Carboidratos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-yellow-500",children:[I.fat,"g"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gorduras"})]})]}),j&&e.jsxs("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-snapfit-green/20",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-semibold text-white",children:[I.fiber,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Fibras"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-semibold text-white",children:[I.sugar,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Açúcares"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-semibold text-white",children:[I.sodium,"mg"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Sódio"})]})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2 text-center",children:"* Valores por porção"})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center gap-2",children:[e.jsx(ss,{className:"w-5 h-5"}),"Ingredientes"]}),e.jsx("div",{className:"space-y-2",children:s.ingredients.map((k,z)=>e.jsxs("div",{className:"flex justify-between items-center p-3 bg-snapfit-gray rounded-lg",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:k.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[k.quantity," ",k.unit]})]}),e.jsxs("div",{className:"text-right text-sm text-gray-400",children:[k.calories," cal • ",k.protein,"g prot"]})]},k.id||z))})]}),s.instructions&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Modo de Preparo"}),e.jsx("div",{className:"bg-snapfit-gray rounded-lg p-4",children:e.jsx("p",{className:"text-gray-300 whitespace-pre-wrap",children:s.instructions})})]}),s.tags&&s.tags.length>0&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map((k,z)=>e.jsx("span",{className:"px-3 py-1 bg-snapfit-green/10 text-snapfit-green rounded-full text-sm",children:k},z))})]})]}),f&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-60 p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-6 max-w-md w-full",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Avaliar Receita"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Sua avaliação"}),e.jsx("div",{className:"flex gap-1",children:[1,2,3,4,5].map(k=>e.jsx("button",{onClick:()=>n(k),className:`p-1 transition-colors ${k<=u?"text-yellow-400":"text-gray-600"}`,children:e.jsx(ie,{className:`w-6 h-6 ${k<=u?"fill-current":""}`})},k))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Comentário (opcional)"}),e.jsx("textarea",{value:a,onChange:k=>d(k.target.value),placeholder:"Compartilhe sua experiência com esta receita...",className:"w-full p-3 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white resize-none",rows:3})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:P,disabled:u===0||y,className:"flex-1 bg-snapfit-green text-white py-2 px-4 rounded-lg hover:bg-snapfit-green/80 transition-colors disabled:opacity-50",children:y?"Enviando...":"Enviar Avaliação"}),e.jsx("button",{onClick:()=>g(!1),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"})]})]})})]})})}function fa({recipe:s,onClose:r,onAddToMealPlan:o}){const[t,l]=C.useState(new Date().toISOString().split("T")[0]),[x,p]=C.useState("breakfast"),[b,y]=C.useState(1),[j,c]=C.useState(""),f=[{value:"breakfast",label:"Café da Manhã",icon:"🌅"},{value:"lunch",label:"Almoço",icon:"☀️"},{value:"dinner",label:"Jantar",icon:"🌙"},{value:"snack",label:"Lanche",icon:"🍎"}],g=()=>{const a={recipeId:s.id,date:t,mealType:x,servings:b,notes:j||void 0};o(a),r()},u={calories:Math.round(s.totalCalories/s.servings),protein:Math.round(s.totalProtein/s.servings*10)/10,carbs:Math.round(s.totalCarbs/s.servings*10)/10,fat:Math.round(s.totalFat/s.servings*10)/10},n={calories:u.calories*b,protein:Math.round(u.protein*b*10)/10,carbs:Math.round(u.carbs*b*10)/10,fat:Math.round(u.fat*b*10)/10};return e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl max-w-md w-full",children:[e.jsxs("div",{className:"flex justify-between items-center p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold text-white",children:"Adicionar ao Planejamento"}),e.jsx("p",{className:"text-sm text-gray-400",children:s.name})]}),e.jsx("button",{onClick:r,className:"p-2 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:[e.jsx(ze,{className:"w-4 h-4 inline mr-2"}),"Data"]}),e.jsx("input",{type:"date",value:t,onChange:a=>l(a.target.value),min:new Date().toISOString().split("T")[0],className:"w-full p-3 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:[e.jsx(Be,{className:"w-4 h-4 inline mr-2"}),"Tipo de Refeição"]}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:f.map(a=>e.jsxs("button",{onClick:()=>p(a.value),className:`p-3 rounded-lg border transition-colors ${x===a.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-lg mb-1",children:a.icon}),e.jsx("div",{className:"text-sm font-medium",children:a.label})]},a.value))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:[e.jsx(rs,{className:"w-4 h-4 inline mr-2"}),"Número de Porções"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>y(Math.max(1,b-1)),className:"w-10 h-10 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white hover:bg-snapfit-green/20 transition-colors",children:"-"}),e.jsxs("div",{className:"flex-1 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:b}),e.jsx("div",{className:"text-xs text-gray-400",children:"porções"})]}),e.jsx("button",{onClick:()=>y(b+1),className:"w-10 h-10 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white hover:bg-snapfit-green/20 transition-colors",children:"+"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Informações Nutricionais"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-white",children:n.calories}),e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-snapfit-green",children:[n.protein,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-green-500",children:[n.carbs,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Carboidratos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-yellow-500",children:[n.fat,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx(pe,{className:"w-4 h-4 text-blue-400"}),e.jsxs("div",{className:"text-sm text-blue-300",children:["Tempo de preparo: ",e.jsxs("span",{className:"font-medium",children:[s.prepTime," minutos"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Observações (opcional)"}),e.jsx("textarea",{value:j,onChange:a=>c(a.target.value),placeholder:"Ex: Preparar na noite anterior, dobrar a receita...",className:"w-full p-3 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white resize-none focus:outline-none focus:border-snapfit-green/50",rows:3})]})]}),e.jsxs("div",{className:"flex gap-3 p-6 border-t border-snapfit-green/20",children:[e.jsx("button",{onClick:r,className:"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"}),e.jsxs("button",{onClick:g,className:"flex-1 bg-snapfit-green text-white py-2 px-4 rounded-lg hover:bg-snapfit-green/80 transition-colors flex items-center justify-center gap-2",children:[e.jsx(se,{className:"w-4 h-4"}),"Adicionar ao Plano"]})]})]})})}const ba=()=>{const s=_e(),r=(c={})=>Y({queryKey:["recipes","search",c],queryFn:async()=>{const f=new URLSearchParams;return Object.entries(c).forEach(([u,n])=>{n!=null&&n!==""&&(Array.isArray(n)?f.append(u,n.join(",")):f.append(u,n.toString()))}),(await q.get(`users/recipes/search?${f.toString()}`)).data},staleTime:1e3*60*5}),o=()=>Y({queryKey:["recipes","favorites"],queryFn:async()=>(await q.get("users/recipes/favorites")).data,staleTime:1e3*60*5}),t=c=>Y({queryKey:["recipes",c],queryFn:async()=>(await q.get(`users/recipes/${c}`)).data,enabled:!!c,staleTime:1e3*60*10}),l=G({mutationFn:async c=>(await q.post("users/recipes",c)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes"]})}}),x=G({mutationFn:async({id:c,data:f})=>(await q.put(`users/recipes/${c}`,f)).data,onSuccess:(c,f)=>{s.invalidateQueries({queryKey:["recipes"]}),s.invalidateQueries({queryKey:["recipes",f.id]})}}),p=G({mutationFn:async c=>(await q.delete(`users/recipes/${c}`)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes"]})}}),b=G({mutationFn:async({recipeId:c,notes:f})=>(await q.post("users/recipes/favorites",{recipeId:c,notes:f})).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes","favorites"]}),s.invalidateQueries({queryKey:["recipes","search"]})}}),y=G({mutationFn:async c=>(await q.delete(`users/recipes/favorites/${c}`)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes","favorites"]}),s.invalidateQueries({queryKey:["recipes","search"]})}}),j=G({mutationFn:async({recipeId:c,rating:f,comment:g})=>(await q.post("users/recipes/rate",{recipeId:c,rating:f,comment:g})).data,onSuccess:(c,f)=>{s.invalidateQueries({queryKey:["recipes",f.recipeId]}),s.invalidateQueries({queryKey:["recipes","search"]})}});return{useSearchRecipes:r,useFavoriteRecipes:o,useRecipeById:t,createRecipe:l.mutateAsync,updateRecipe:x.mutateAsync,deleteRecipe:p.mutateAsync,addToFavorites:b.mutateAsync,removeFromFavorites:y.mutateAsync,rateRecipe:j.mutateAsync,isCreating:l.isPending,isUpdating:x.isPending,isDeleting:p.isPending,isAddingToFavorites:b.isPending,isRemovingFromFavorites:y.isPending,isRating:j.isPending}};function ja(){var xe,ge;const{useFavoriteRecipes:s,useSearchRecipes:r,createRecipe:o,deleteRecipe:t,addToFavorites:l,removeFromFavorites:x,rateRecipe:p,isDeleting:b,isAddingToFavorites:y,isRemovingFromFavorites:j,isRating:c}=ba(),{data:f,isLoading:g,error:u}=s(),n=f||[],[a,d]=C.useState({limit:20}),{data:m,isLoading:v}=r(a),[F,P]=C.useState(""),[Q,I]=C.useState(!1),[k,z]=C.useState(!1),[te,W]=C.useState(!1),[re,H]=C.useState(!1),[w,E]=C.useState(null),[J,Z]=C.useState(null),[N,L]=C.useState({name:"",description:"",ingredients:[],servings:1,prepTime:0,difficulty:"easy",category:"breakfast",isPublic:!0}),[Re,Ie]=C.useState(null),[ce,fe]=C.useState(100),be=n.filter(i=>i.name.toLowerCase().includes(F.toLowerCase())),je=i=>{if(!i||!ce)return;const T=ce/100,X={id:`new-${Date.now()}`,name:i.name,quantity:ce,unit:"g",protein:Math.round((i.protein||0)*T*10)/10,carbs:Math.round((i.carbs||0)*T*10)/10,fat:Math.round((i.fat||0)*T*10)/10,calories:Math.round((i.calories||0)*T)};L({...N,ingredients:[...N.ingredients||[],X]}),z(!1),Ie(null),fe(100)},ve=i=>{L({...N,ingredients:(N.ingredients||[]).filter(T=>T.id!==i)})},ye=async()=>{if(!(!N.name||!(N.ingredients&&N.ingredients.length>0)))try{const i={name:N.name,description:N.description||"",ingredients:N.ingredients,servings:N.servings||1,prepTime:N.prepTime||0,cookTime:N.cookTime,difficulty:N.difficulty||"easy",category:N.category||"breakfast",isPublic:N.isPublic??!0,tags:N.tags};await o(i),R.success("Receita criada com sucesso!"),I(!1),L({name:"",description:"",ingredients:[],servings:1,prepTime:0,difficulty:"easy",category:"breakfast",isPublic:!0})}catch(i){console.error("Error creating recipe:",i),R.error("Erro ao criar receita. Tente novamente.")}},de=async i=>{if(i.id)try{await x(i.id),R.success("Receita removida dos favoritos!")}catch(T){console.error("Error removing from favorites:",T),R.error("Erro ao remover dos favoritos. Tente novamente.")}},Ne=async i=>{if(!(!i.id||!window.confirm("Tem certeza que deseja excluir esta receita?")))try{await t(i.id),R.success("Receita excluída com sucesso!")}catch(X){console.error("Error deleting recipe:",X),R.error("Erro ao excluir receita. Tente novamente.")}},me=async i=>{try{await l({recipeId:i}),R.success("Receita adicionada aos favoritos!")}catch(T){console.error("Error adding to favorites:",T),R.error("Erro ao adicionar aos favoritos. Tente novamente.")}},we=async(i,T,X)=>{try{await p({recipeId:i,rating:T,comment:X}),R.success("Avaliação enviada com sucesso!")}catch(Ce){console.error("Error rating recipe:",Ce),R.error("Erro ao enviar avaliação. Tente novamente.")}},ke=async i=>{try{console.log("Adding to meal plan:",i),R.success("Receita adicionada ao planejamento de refeições!"),Z(null)}catch(T){console.error("Error adding to meal plan:",T),R.error("Erro ao adicionar ao planejamento. Tente novamente.")}};return g?e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(i=>e.jsx("div",{className:"h-64 bg-gray-600 rounded"},i))})]})}):u?e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ee,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Erro ao carregar receitas"}),e.jsx("p",{className:"text-gray-400",children:"Não foi possível carregar suas receitas favoritas."})]})}):e.jsxs("div",{className:"card p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Minhas Receitas Favoritas"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Buscar receitas...",value:F,onChange:i=>P(i.target.value),className:"pl-9 pr-4 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-full text-sm text-white focus:outline-none focus:border-snapfit-green/50"}),e.jsx($e,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"})]}),e.jsxs("button",{onClick:()=>W(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx($e,{className:"w-4 h-4"}),"Explorar"]}),e.jsxs("button",{onClick:()=>I(!0),className:"btn-primary flex items-center gap-2",children:[e.jsx(se,{className:"w-4 h-4"}),"Nova Receita"]})]})]}),Q?e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-4 border border-snapfit-green/20 mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-base font-bold text-white",children:"Nova Receita"}),e.jsx("button",{onClick:()=>I(!1),className:"p-1 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Nome da Receita"}),e.jsx("input",{type:"text",value:N.name,onChange:i=>L({...N,name:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",placeholder:"Ex: Omelete de Claras"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Descrição"}),e.jsx("textarea",{value:N.description,onChange:i=>L({...N,description:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",placeholder:"Descreva brevemente sua receita",rows:2})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Categoria"}),e.jsxs("select",{value:N.category,onChange:i=>L({...N,category:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",children:[e.jsx("option",{value:"breakfast",children:"Café da Manhã"}),e.jsx("option",{value:"lunch",children:"Almoço"}),e.jsx("option",{value:"dinner",children:"Jantar"}),e.jsx("option",{value:"snack",children:"Lanche"}),e.jsx("option",{value:"dessert",children:"Sobremesa"}),e.jsx("option",{value:"drink",children:"Bebida"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Dificuldade"}),e.jsxs("select",{value:N.difficulty,onChange:i=>L({...N,difficulty:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",children:[e.jsx("option",{value:"easy",children:"Fácil"}),e.jsx("option",{value:"medium",children:"Médio"}),e.jsx("option",{value:"hard",children:"Difícil"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Porções"}),e.jsx("input",{type:"number",value:N.servings,onChange:i=>L({...N,servings:Number(i.target.value)}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",min:"1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Tempo de Preparo (min)"}),e.jsx("input",{type:"number",value:N.prepTime,onChange:i=>L({...N,prepTime:Number(i.target.value)}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",min:"0"})]})]})]}),e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Ingredientes"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[(N.ingredients||[]).map(i=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:i.name}),e.jsxs("div",{className:"text-xs text-gray-400",children:[i.quantity," ",i.unit," • P: ",i.protein,"g • C: ",i.carbs,"g • G: ",i.fat,"g •",i.calories," kcal"]})]}),e.jsx("button",{onClick:()=>ve(i.id),className:"p-1 hover:bg-snapfit-dark-gray rounded-full transition-colors",children:e.jsx(Ze,{className:"w-4 h-4 text-red-400"})})]},i.id)),e.jsx("div",{className:"p-3 bg-snapfit-gray rounded-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("button",{onClick:()=>z(!0),className:"flex items-center gap-2 px-4 py-3 bg-snapfit-green/20 text-snapfit-green rounded-lg text-sm border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors mx-auto",children:[e.jsx(se,{className:"w-4 h-4"}),"Adicionar Ingrediente"]}),e.jsx("p",{className:"text-xs text-gray-400 mt-2",children:"Selecione alimentos da nossa base de dados com macros calculados automaticamente"})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>I(!1),className:"px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors",children:"Cancelar"}),e.jsxs("button",{onClick:ye,className:"flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-full text-sm font-medium hover:bg-snapfit-green/90 transition-colors",disabled:!N.name||!(N.ingredients&&N.ingredients.length>0),children:[e.jsx(Is,{className:"w-4 h-4"}),"Salvar Receita"]})]})]}):null,e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:be.map(i=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-bold text-white",children:i.name}),e.jsx("p",{className:"text-sm text-gray-400",children:i.description})]}),e.jsxs("div",{className:"flex gap-2",children:[i.createdBy&&e.jsx("button",{className:"p-1.5 bg-snapfit-green/10 rounded-full hover:bg-snapfit-green/20 transition-colors",title:"Editar receita",children:e.jsx(Ts,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("button",{onClick:()=>de(i),disabled:j,className:"p-1.5 bg-red-500/10 rounded-full hover:bg-red-500/20 transition-colors disabled:opacity-50",title:"Remover dos favoritos",children:e.jsx(De,{className:"w-4 h-4 text-red-400"})}),i.createdBy&&e.jsx("button",{onClick:()=>Ne(i),disabled:b,className:"p-1.5 bg-red-500/10 rounded-full hover:bg-red-500/20 transition-colors disabled:opacity-50",title:"Excluir receita",children:e.jsx(Ze,{className:"w-4 h-4 text-red-400"})})]})]}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400 mb-3",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ee,{className:"w-3 h-3"}),e.jsxs("span",{children:[i.servings," porções"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(pe,{className:"w-3 h-3"}),e.jsxs("span",{children:[i.prepTime," min"]})]}),i.averageRating>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ie,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:i.averageRating.toFixed(1)})]}),e.jsx("span",{className:"px-2 py-1 bg-snapfit-green/10 text-snapfit-green rounded-full text-xs",children:i.category==="breakfast"?"Café da Manhã":i.category==="lunch"?"Almoço":i.category==="dinner"?"Jantar":i.category==="snack"?"Lanche":i.category==="dessert"?"Sobremesa":"Bebida"})]}),e.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:i.totalCalories})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[i.totalProtein,"g"]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carbos"}),e.jsxs("div",{className:"text-sm font-medium text-green-500",children:[i.totalCarbs,"g"]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-sm font-medium text-yellow-500",children:[i.totalFat,"g"]})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>E(i),className:"text-xs text-gray-400 hover:text-white transition-colors",children:"Ver detalhes"}),e.jsxs("button",{onClick:()=>Z(i),className:"flex items-center gap-1 px-3 py-1.5 bg-snapfit-green/20 text-snapfit-green rounded-lg text-xs border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors",children:[e.jsx(se,{className:"w-3 h-3"}),"Adicionar à Refeição"]})]})]},i.id))}),te&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Explorar Receitas"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>H(!0),className:"flex items-center gap-2 px-3 py-2 bg-snapfit-green/10 text-snapfit-green rounded-lg hover:bg-snapfit-green/20 transition-colors",children:[e.jsx(ts,{className:"w-4 h-4"}),"Filtros"]}),e.jsx("button",{onClick:()=>W(!1),className:"p-2 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]})]}),v?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[1,2,3,4,5,6].map(i=>e.jsx("div",{className:"h-48 bg-gray-600 rounded-lg animate-pulse"},i))}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(xe=m==null?void 0:m.recipes)==null?void 0:xe.map(i=>e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:i.name}),e.jsx("p",{className:"text-sm text-gray-400 line-clamp-2",children:i.description})]}),e.jsx("button",{onClick:()=>me(i.id),disabled:y,className:"p-1.5 bg-snapfit-green/10 rounded-full hover:bg-snapfit-green/20 transition-colors disabled:opacity-50",title:"Adicionar aos favoritos",children:e.jsx(De,{className:"w-4 h-4 text-snapfit-green"})})]}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400 mb-3",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(pe,{className:"w-3 h-3"}),e.jsxs("span",{children:[i.prepTime," min"]})]}),i.averageRating>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ie,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:i.averageRating.toFixed(1)})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:i.totalCalories})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[i.totalProtein,"g"]})]})]}),e.jsx("button",{onClick:()=>E(i),className:"w-full text-xs text-gray-400 hover:text-white transition-colors py-2 border-t border-gray-600",children:"Ver detalhes completos"})]},i.id))}),((ge=m==null?void 0:m.recipes)==null?void 0:ge.length)===0&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ee,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Nenhuma receita encontrada"})]})]})}),re&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsx("div",{className:"max-w-md w-full",children:e.jsx(ha,{filters:a,onFiltersChange:i=>{d(i),H(!1)},onClose:()=>H(!1)})})}),w&&e.jsx(pa,{recipe:w,onClose:()=>E(null),onAddToFavorites:me,onRemoveFromFavorites:de,onRate:we,isFavorite:!0,isAddingToFavorites:y,isRemovingFromFavorites:j,isRating:c}),J&&e.jsx(fa,{recipe:J,onClose:()=>Z(null),onAddToMealPlan:ke}),k&&e.jsx(qs,{onSelect:je,onClose:()=>z(!1)})]})}const va=[{id:"micronutrients",label:"Análise Nutricional",icon:ue,description:"Micronutrientes e suplementos"},{id:"history",label:"Histórico",icon:Es,description:"Protocolos anteriores"},{id:"recipes",label:"Receitas Favoritas",icon:De,description:"Suas receitas salvas"}];function ya({onOpenMicronutrientsModal:s,onReuseProtocol:r}){const[o,t]=C.useState("micronutrients"),l=()=>{switch(o){case"micronutrients":return e.jsx(ga,{onOpenAnalysis:()=>s("analysis")});case"history":return e.jsx(ua,{showProtocolHistory:!0,onReuseProtocol:r});case"recipes":return e.jsx(ja,{});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Ferramentas e Análises"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Acesse análises avançadas, histórico e suas receitas favoritas"})]}),e.jsx("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20",children:va.map(x=>{const p=x.icon,b=x.id===o;return e.jsxs("button",{onClick:()=>t(x.id),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${b?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(p,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline text-sm",children:x.label}),e.jsx("span",{className:"sm:hidden text-xs",children:x.id==="micronutrients"?"Análise":x.id==="history"?"Histórico":"Receitas"})]},x.id)})}),e.jsx("div",{className:"min-h-[200px]",children:l()})]})}const Na=[{value:"breakfast",label:"Café da Manhã",time:"07:00"},{value:"morning_snack",label:"Lanche da Manhã",time:"10:00"},{value:"lunch",label:"Almoço",time:"12:00"},{value:"afternoon_snack",label:"Lanche da Tarde",time:"15:00"},{value:"dinner",label:"Jantar",time:"19:00"},{value:"evening_snack",label:"Ceia",time:"22:00"}];function wa({isOpen:s,onClose:r,selectedDate:o}){const t=Os(),{control:l,handleSubmit:x,watch:p,setValue:b,reset:y,formState:{errors:j,isValid:c}}=$s({mode:"onChange",defaultValues:{name:"",meal_time:"lunch",foods:[{name:"",quantity:100,unit:"g",calories:0,protein:0,carbs:0,fat:0}],notes:""}}),f=p("foods"),g=()=>{const d=p("foods");b("foods",[...d,{name:"",quantity:100,unit:"g",calories:0,protein:0,carbs:0,fat:0}])},u=d=>{const m=p("foods");m.length>1&&b("foods",m.filter((v,F)=>F!==d))},n=async d=>{try{await t.mutateAsync({...d,date:o}),R.success("Refeição adicionada com sucesso!",{position:"bottom-right"}),y(),r()}catch(m){console.error("Error adding meal:",m),R.error("Erro ao adicionar refeição",{position:"bottom-right"})}},a=()=>{y(),r()};return s?e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Be,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Nova Refeição"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Adicione uma nova refeição ao seu diário"})]})]}),e.jsx("button",{onClick:a,className:"p-2 hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("form",{onSubmit:x(n),className:"p-6 space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-white",children:"Nome da Refeição"}),e.jsx(U,{name:"name",control:l,rules:{required:"Nome da refeição é obrigatório"},render:({field:d})=>e.jsx("input",{...d,type:"text",className:`w-full p-3 bg-snapfit-dark-gray border rounded-lg text-white placeholder-gray-400 ${j.name?"border-red-500":"border-snapfit-green/30"}`,placeholder:"Ex: Almoço Especial"})}),j.name&&e.jsx("p",{className:"text-red-400 text-sm",children:j.name.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-white",children:"Horário da Refeição"}),e.jsx(U,{name:"meal_time",control:l,rules:{required:"Horário é obrigatório"},render:({field:d})=>e.jsx("select",{...d,className:`w-full p-3 bg-snapfit-dark-gray border rounded-lg text-white ${j.meal_time?"border-red-500":"border-snapfit-green/30"}`,children:Na.map(m=>e.jsxs("option",{value:m.value,children:[m.label," (",m.time,")"]},m.value))})}),j.meal_time&&e.jsx("p",{className:"text-red-400 text-sm",children:j.meal_time.message})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Alimentos"}),e.jsxs("button",{type:"button",onClick:g,className:"flex items-center gap-2 px-3 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(se,{className:"w-4 h-4"}),"Adicionar Alimento"]})]}),f.map((d,m)=>e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"md:col-span-2 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Nome do Alimento"}),e.jsx(U,{name:`foods.${m}.name`,control:l,rules:{required:"Nome do alimento é obrigatório"},render:({field:v})=>e.jsx("input",{...v,type:"text",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white placeholder-gray-400",placeholder:"Ex: Arroz integral"})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Quantidade"}),e.jsx(U,{name:`foods.${m}.quantity`,control:l,rules:{required:!0,min:.1},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Unidade"}),e.jsx(U,{name:`foods.${m}.unit`,control:l,render:({field:v})=>e.jsxs("select",{...v,className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white",children:[e.jsx("option",{value:"g",children:"g"}),e.jsx("option",{value:"ml",children:"ml"}),e.jsx("option",{value:"unidade",children:"unidade"}),e.jsx("option",{value:"colher",children:"colher"}),e.jsx("option",{value:"xícara",children:"xícara"})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Calorias"}),e.jsx(U,{name:`foods.${m}.calories`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Proteína (g)"}),e.jsx(U,{name:`foods.${m}.protein`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Carboidratos (g)"}),e.jsx(U,{name:`foods.${m}.carbs`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Gordura (g)"}),e.jsx(U,{name:`foods.${m}.fat`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]})]})]}),f.length>1&&e.jsx("div",{className:"mt-3 flex justify-end",children:e.jsx("button",{type:"button",onClick:()=>u(m),className:"text-red-400 hover:text-red-300 text-sm",children:"Remover alimento"})})]},m))]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-white",children:"Observações (opcional)"}),e.jsx(U,{name:"notes",control:l,render:({field:d})=>e.jsx("textarea",{...d,rows:3,className:"w-full p-3 bg-snapfit-dark-gray border border-snapfit-green/30 rounded-lg text-white placeholder-gray-400 resize-none",placeholder:"Adicione observações sobre a refeição..."})})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4 border-t border-snapfit-green/20",children:[e.jsx("button",{type:"button",onClick:a,className:"px-6 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancelar"}),e.jsxs("button",{type:"submit",disabled:!c||t.isPending,className:"flex items-center gap-2 px-6 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[t.isPending&&e.jsx("div",{className:"w-4 h-4 border-2 border-black/20 border-t-black rounded-full animate-spin"}),e.jsx(Be,{className:"w-4 h-4"}),"Adicionar Refeição"]})]})]})]})}):null}function Da(){var fe,be,je,ve,ye,de,Ne,me,we,ke,xe,ge,i,T,X,Ce,Le,Oe,Ve,Ke,Qe,Ue,Ge,We;const s=as(),[r,o]=B.useState("monday"),{data:t,isLoading:l}=Xs(),x=Ys(),p=Bs(),b=new Date().toISOString().split("T")[0],{data:y}=Vs(b,b),c=(h=>{const S=new Date,D=S.getDay(),V={sunday:0,monday:1,tuesday:2,wednesday:3,thursday:4,friday:5,saturday:6}[h]-D,K=new Date(S);return K.setDate(S.getDate()+V),K.toISOString().split("T")[0]})(r),{data:f,isLoading:g,error:u}=Y({queryKey:["diet-page","meals",c],queryFn:async()=>{var h,S,D,$;console.log("🔄 DietPage: Fetching meals for date:",c),console.log("📅 DietPage: Selected day:",r),console.log("🔑 DietPage: Access token:",localStorage.getItem("accessToken")?"Present":"Missing");try{const M=await q.get("users/protocols/diet/meals/active",{searchParams:{date:`${c} 00:00:00`}});return console.log("🍽️ DietPage: Full response:",M),console.log("📋 DietPage: Has protocol:",(h=M==null?void 0:M.data)==null?void 0:h.has_protocol),console.log("🥘 DietPage: Meals count:",((D=(S=M==null?void 0:M.data)==null?void 0:S.meals)==null?void 0:D.length)||0),($=M==null?void 0:M.data)!=null&&$.has_protocol||console.warn("⚠️ DietPage: User does not have an active diet protocol"),(M==null?void 0:M.data)||{has_protocol:!1,meals:[]}}catch(M){return console.error("❌ DietPage: API error:",M),{has_protocol:!1,meals:[]}}},staleTime:1e3*60*5,refetchOnWindowFocus:!1,retry:1}),n=B.useMemo(()=>{const h=t==null?void 0:t.nutritional_goals,S=y,D=(M,V)=>{if(h){const K=h[M]||h[M+"_target"]||h[M+"s"]||h["target_"+M]||V;return typeof K=="number"?K:V}return V},$=M=>{var K;const V=((K=S==null?void 0:S[M])==null?void 0:K.current)||(S==null?void 0:S[M])||0;return typeof V=="number"?V:0};return h?{protein:{current:$("protein"),target:D("protein",150)},carbs:{current:$("carbs"),target:D("carbs",200)},fat:{current:$("fat"),target:D("fat",70)},calories:{current:$("calories"),target:D("calories",2e3)}}:{protein:{current:$("protein"),target:150},carbs:{current:$("carbs"),target:200},fat:{current:$("fat"),target:70},calories:{current:$("calories"),target:2e3}}},[t,y]);(((fe=n.protein)==null?void 0:fe.current)||0)*4,(((be=n.carbs)==null?void 0:be.current)||0)*4,(((je=n.fat)==null?void 0:je.current)||0)*9,(((ve=n.protein)==null?void 0:ve.current)||0)*4,`${Math.round((((ye=n.protein)==null?void 0:ye.current)||0)*4)}`,(((de=n.carbs)==null?void 0:de.current)||0)*4,`${Math.round((((Ne=n.carbs)==null?void 0:Ne.current)||0)*4)}`,(((me=n.fat)==null?void 0:me.current)||0)*9,`${Math.round((((we=n.fat)==null?void 0:we.current)||0)*9)}`;const a={startDate:"2023-10-15",duration:"8 semanas",mealsCompleted:42,totalMeals:56,adherenceRate:75,waterIntake:{average:2.2,goal:3},calorieDeficit:{daily:350},weightLoss:{total:3.2}},d={monday:[{id:"meal-1",name:"Café da Manhã",time:"07:30",foods:[{id:"food-1",name:"Ovos",quantity:3,unit:"unidades",calories:210,protein:18,carbs:0,fat:15},{id:"food-2",name:"Aveia",quantity:40,unit:"g",calories:150,protein:5,carbs:27,fat:3},{id:"food-3",name:"Banana",quantity:1,unit:"unidade",calories:105,protein:1,carbs:27,fat:0}]},{id:"meal-2",name:"Almoço",time:"12:00",foods:[{id:"food-4",name:"Frango",quantity:150,unit:"g",calories:165,protein:31,carbs:0,fat:3.5},{id:"food-5",name:"Arroz",quantity:100,unit:"g",calories:130,protein:2.7,carbs:28,fat:.3},{id:"food-6",name:"Brócolis",quantity:100,unit:"g",calories:55,protein:3.7,carbs:11,fat:.6}]},{id:"meal-3",name:"Lanche",time:"16:00",foods:[{id:"food-7",name:"Whey Protein",quantity:30,unit:"g",calories:120,protein:24,carbs:3,fat:2},{id:"food-8",name:"Maçã",quantity:1,unit:"unidade",calories:95,protein:.5,carbs:25,fat:.3}]}],tuesday:[{id:"meal-5",name:"Café da Manhã",time:"07:30",foods:[{id:"food-12",name:"Iogurte Grego",quantity:200,unit:"g",calories:130,protein:22,carbs:8,fat:0},{id:"food-13",name:"Granola",quantity:30,unit:"g",calories:120,protein:3,carbs:20,fat:3},{id:"food-14",name:"Morangos",quantity:100,unit:"g",calories:32,protein:.7,carbs:7.7,fat:.3}]}],wednesday:[{id:"meal-9",name:"Café da Manhã",time:"07:30",foods:[{id:"food-20",name:"Panquecas Proteicas",quantity:2,unit:"unidades",calories:250,protein:20,carbs:25,fat:8}]}],thursday:[{id:"meal-13",name:"Café da Manhã",time:"07:30",foods:[{id:"food-25",name:"Omelete",quantity:1,unit:"porção",calories:220,protein:18,carbs:5,fat:16}]}],friday:[{id:"meal-17",name:"Café da Manhã",time:"07:30",foods:[{id:"food-30",name:"Smoothie Proteico",quantity:1,unit:"copo",calories:280,protein:25,carbs:30,fat:8}]}],saturday:[{id:"meal-21",name:"Café da Manhã",time:"08:30",foods:[{id:"food-35",name:"Pão Integral",quantity:2,unit:"fatias",calories:160,protein:8,carbs:30,fat:2}]}],sunday:[{id:"meal-25",name:"Café da Manhã",time:"09:00",foods:[{id:"food-40",name:"Tapioca",quantity:1,unit:"unidade",calories:130,protein:3,carbs:28,fat:.5}]}]},[m,v]=B.useState(!1),[F,P]=B.useState(!1),[Q,I]=B.useState(!1),[k,z]=B.useState(null),[te,W]=B.useState(!1),[re,H]=B.useState("analysis"),[w,E]=B.useState(!1),[J,Z]=B.useState(!1),[N,L]=B.useState(null),Re=async()=>{try{console.log("🔍 Checking for active diet protocol before showing options...");const h=await p.mutateAsync("diet");h&&h.id?(console.log("⚠️ Active diet protocol found, showing confirmation dialog"),L(()=>()=>P(!0)),Z(!0)):(console.log("✅ No active diet protocol found, proceeding directly"),P(!0))}catch{console.log("ℹ️ No active diet protocol found (error), proceeding directly"),P(!0)}},Ie=async()=>{try{console.log("✅ User confirmed diet protocol creation, proceeding without premature finalization"),N&&(N(),L(null)),Z(!1)}catch(h){console.error("Error in confirmation action:",h),Z(!1),L(null)}};B.useEffect(()=>{var h;console.log("🔍 DietPage - currentProtocol atualizado:",t),console.log("🔍 DietPage - loadingProtocol:",l),console.log("🍽️ DietPage - currentProtocol.meals:",t==null?void 0:t.meals),console.log("🎯 DietPage - nutritional_goals:",t==null?void 0:t.nutritional_goals),console.log("📊 DietPage - nutritionalSummary:",y),console.log("🥗 DietPage - nutritionGoals calculado - type:",typeof n),console.log("📅 DietPage - selectedDay:",r),console.log("🥘 DietPage - meals for selected day:",(h=t==null?void 0:t.meals)==null?void 0:h[r])},[t,l,y,n]),B.useEffect(()=>{var h;console.log("📅 DietPage - selectedDay changed to:",r),console.log("🥘 DietPage - meals for new selected day:",(h=t==null?void 0:t.meals)==null?void 0:h[r])},[r,t]);const ce=async()=>{if(window.confirm("Tem certeza que deseja remover este protocolo?")){if(console.log("🔍 Debug - currentProtocol:",t),console.log("🔍 Debug - currentProtocol?.id:",t==null?void 0:t.id),console.log("🔍 Debug - typeof currentProtocol?.id:",typeof(t==null?void 0:t.id)),!(t!=null&&t.id)){console.error("❌ ID do protocolo não encontrado:",t),R.error("Erro: ID do protocolo não encontrado");return}try{console.log("🗑️ Removendo protocolo com ID:",t.id),await x.mutateAsync(t.id),console.log("✅ Protocolo removido com sucesso, UI será atualizada automaticamente")}catch(S){console.error("Erro ao remover protocolo:",S),R.error("Erro ao remover protocolo. Tente novamente.")}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:"Dieta"}),F&&e.jsx(sa,{onGenerateAI:()=>{P(!1),s("/dashboard/diet/create-protocol/ai")},onCreateManual:()=>{P(!1),s("/dashboard/diet/create-protocol/manual")},onImportNutritionist:()=>{P(!1),s("/dashboard/diet/create-protocol/import")},onReadProtocol:()=>{P(!1),s("/dashboard/diet/create-protocol/import")},onClose:()=>P(!1)}),!t&&!l&&e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center",children:e.jsx(ee,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-2",children:"Nenhum protocolo ativo"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Você não possui um protocolo de dieta ativo. Crie um novo protocolo para começar."}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 mx-auto",onClick:Re,children:[e.jsx(se,{className:"w-4 h-4"}),"Criar Protocolo"]})]})}),t&&e.jsxs(B.Fragment,{children:[e.jsxs("div",{className:"card p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:(t==null?void 0:t.name)||"Protocolo de Cutting"}),e.jsx("div",{className:"flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30",children:e.jsx("span",{className:"text-xs sm:text-sm font-medium text-snapfit-green",children:"Cutting"})}),(t==null?void 0:t.lastUpdated)&&e.jsx("div",{className:"flex items-center gap-1 px-2 py-1 bg-blue-500/20 rounded-lg border border-blue-500/30",children:e.jsx("span",{className:"text-xs font-medium text-blue-400",children:"Editado"})})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("button",{onClick:()=>{t!=null&&t.id?(console.log("🔧 Navegando para edição do protocolo:",t.id),s(`/dashboard/diet/edit-protocol/${t.id}`)):(console.error("❌ Nenhum protocolo ativo para editar"),R.error("Nenhum protocolo ativo para editar"))},disabled:!t,className:"flex items-center gap-1.5 px-3 py-2 text-sm font-medium text-snapfit-green bg-snapfit-green/10 border border-snapfit-green/20 rounded-lg hover:bg-snapfit-green/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ee,{className:"w-4 h-4"}),"Editar Protocolo"]})}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 text-sm",onClick:Re,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M12 5v14"}),e.jsx("path",{d:"M5 12h14"})]}),"Criar Protocolo"]})})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Perda de gordura mantendo massa muscular"}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10 mb-6",children:[e.jsx("h3",{className:"font-bold text-white mb-3",children:"Metas Nutricionais do Protocolo"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((ke=n.calories)==null?void 0:ke.target)||0," kcal"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((xe=n.protein)==null?void 0:xe.target)||0,"g (",Math.round((((ge=n.protein)==null?void 0:ge.target)||0)*4/(((i=n.calories)==null?void 0:i.target)||1)*100),"%)"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carboidratos"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((T=n.carbs)==null?void 0:T.target)||0,"g (",Math.round((((X=n.carbs)==null?void 0:X.target)||0)*4/(((Ce=n.calories)==null?void 0:Ce.target)||1)*100),"%)"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((Le=n.fat)==null?void 0:Le.target)||0,"g (",Math.round((((Oe=n.fat)==null?void 0:Oe.target)||0)*9/(((Ve=n.calories)==null?void 0:Ve.target)||1)*100),"%)"]})]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx(aa,{protocolGoals:{calories:((Ke=n.calories)==null?void 0:Ke.target)||0,protein:((Qe=n.protein)==null?void 0:Qe.target)||0,carbs:((Ue=n.carbs)==null?void 0:Ue.target)||0,fat:((Ge=n.fat)==null?void 0:Ge.target)||0,water:2500}})}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Refeições"}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2",onClick:()=>E(!0),children:[e.jsx(se,{className:"w-4 h-4"}),"Nova Refeição"]})]}),e.jsxs("div",{className:"flex overflow-x-auto gap-2 pb-2 mb-4 hide-scrollbar",children:[e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="monday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("monday"),children:"Seg"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="tuesday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("tuesday"),children:"Ter"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="wednesday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("wednesday"),children:"Qua"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="thursday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("thursday"),children:"Qui"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="friday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("friday"),children:"Sex"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="saturday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("saturday"),children:"Sáb"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="sunday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>o("sunday"),children:"Dom"})]}),e.jsx("div",{className:"space-y-4",children:(We=(()=>{const h=(f==null?void 0:f.meals)||[],S=d[r],D=h.length>0?h:S||[];return console.log("🍽️ DietPage - Meals to show:",{selectedDay:r,selectedDate:c,apiMealsCount:(h==null?void 0:h.length)||0,fallbackMealsCount:(S==null?void 0:S.length)||0,usingAPI:h.length>0,mealsToShow:D.length,hasProtocol:f==null?void 0:f.has_protocol}),D})())==null?void 0:We.map(h=>{const S=h.foods||[],D=h.nutrients&&(h.nutrients.calories>0||h.nutrients.protein>0||h.nutrients.carbs>0||h.nutrients.fat>0);let $,M,V,K;return D?($=Math.round(h.nutrients.calories||0),M=Math.round((h.nutrients.protein||0)*10)/10,V=Math.round((h.nutrients.carbs||0)*10)/10,K=Math.round((h.nutrients.fat||0)*10)/10):($=S.reduce((A,_)=>{const O=_.calories||_.kcal||0;return A+(typeof O=="number"?O:0)},0),M=S.reduce((A,_)=>{const O=_.protein||_.proteins||0;return A+(typeof O=="number"?O:0)},0),V=S.reduce((A,_)=>{const O=_.carbs||_.carbohydrates||_.carb||0;return A+(typeof O=="number"?O:0)},0),K=S.reduce((A,_)=>{const O=_.fat||_.fats||_.lipids||0;return A+(typeof O=="number"?O:0)},0)),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex justify-between items-center mb-3",children:[e.jsx("h3",{className:"font-bold text-white",children:h.name}),e.jsx("span",{className:"text-sm text-gray-400",children:h.meal_time||h.time})]}),e.jsx("div",{className:"space-y-2",children:S.map((A,_)=>{const O=A.quantity||A.amount||0,os=A.unit||A.measure||"g";return e.jsxs("div",{className:"flex justify-between items-center py-2 border-b border-snapfit-gray/20 last:border-0",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-white",children:A.name||"Alimento"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[O," ",os]})]}),!D&&e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-snapfit-green",children:[Math.round(A.calories||A.kcal||0)," kcal"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["P: ",Math.round((A.protein||A.proteins||0)*10)/10,"g • C: ",Math.round((A.carbs||A.carbohydrates||A.carb||0)*10)/10,"g • G: ",Math.round((A.fat||A.fats||A.lipids||0)*10)/10,"g"]})]})]},A.id||_)})}),e.jsx("div",{className:"mt-4 pt-3 border-t border-snapfit-gray/20",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"Total da Refeição"}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-snapfit-green font-bold",children:[$," kcal"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["P: ",M.toFixed(1),"g • C: ",V.toFixed(1),"g • G: ",K.toFixed(1),"g"]})]})]})})]},h.id)})})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4",children:"Estatísticas do Protocolo"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Início"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:new Date(a.startDate).toLocaleDateString("pt-BR")})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Duração"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:a.duration})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Refeições Realizadas"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[a.mealsCompleted,"/",a.totalMeals]})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Adesão"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[a.adherenceRate,"%"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h3",{className:"text-base font-bold mb-3",children:"Adesão ao Protocolo"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Adesão Geral"}),e.jsxs("span",{children:[a.adherenceRate,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"h-2.5 rounded-full bg-yellow-500",style:{width:`${a.adherenceRate}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Refeições Completadas"}),e.jsxs("span",{children:[a.mealsCompleted,"/",a.totalMeals]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-snapfit-green h-2.5 rounded-full",style:{width:`${a.mealsCompleted/a.totalMeals*100}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Consumo de Água"}),e.jsxs("span",{children:[a.waterIntake.average,"L / ",a.waterIntake.goal,"L"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-500 h-2.5 rounded-full",style:{width:`${a.waterIntake.average/a.waterIntake.goal*100}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Déficit Calórico"}),e.jsxs("span",{children:[a.calorieDeficit.daily," kcal/dia"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-orange-500 h-2.5 rounded-full",style:{width:`${Math.min(a.calorieDeficit.daily/500*100,100)}%`}})})]})]})]}),e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h3",{className:"text-base font-bold mb-3",children:"Progresso"}),e.jsxs("div",{className:"flex flex-col items-center justify-center h-full",children:[e.jsx("div",{className:"mb-4",children:e.jsx(Ks,{value:a.weightLoss.total,max:5,label:"Perda de Peso",sublabel:`${a.weightLoss.total}kg`,color:"#B9FF43",size:120})}),e.jsxs("div",{className:"text-center mt-2",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Meta: 5kg em ",a.duration]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["Progresso: ",Math.round(a.weightLoss.total/5*100),"% concluído"]})]})]})]})]}),e.jsx(ea,{nutritionGoals:n,className:"mt-6"})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4",children:"Suplementos"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h3",{className:"font-bold text-white mb-1",children:"Whey Protein"}),e.jsx("div",{className:"text-sm text-gray-400",children:"30g • Após treino"}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"Tomar imediatamente após o treino"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h3",{className:"font-bold text-white mb-1",children:"Creatina"}),e.jsx("div",{className:"text-sm text-gray-400",children:"5g • Diariamente"}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"Tomar em qualquer momento do dia"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h3",{className:"font-bold text-white mb-1",children:"Multivitamínico"}),e.jsx("div",{className:"text-sm text-gray-400",children:"1 cápsula • Diariamente"}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"Tomar com o café da manhã"})]})]})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4",children:"Observações Gerais"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Manter boa hidratação durante o dia. Evitar alimentos processados e com açúcar adicionado. Priorizar proteínas magras e carboidratos complexos."})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{className:"flex justify-between items-center gap-1 text-sm mb-4 text-slate-500 hover:text-red-500 transition-colors disabled:opacity-50",onClick:ce,disabled:x.isPending||!t,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"w-4 h-4",children:[e.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),e.jsx("span",{children:"Remover Protocolo"})]})})]}),m&&e.jsx(_s,{initialMode:k,onProtocolGenerated:h=>{console.log("Protocolo de dieta gerado pela IA:",h),alert(`Protocolo de dieta "${h.name}" gerado com sucesso!`),v(!1),z(null)},onClose:()=>{v(!1),z(null)}}),Q&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20",children:e.jsx(zs,{onSave:h=>{console.log("Protocolo manual criado:",h),alert(`Protocolo "${h.name}" criado com sucesso!`),I(!1)},onCancel:()=>I(!1)})})}),e.jsx(ya,{onOpenMicronutrientsModal:h=>{H(h),W(!0)},onReuseProtocol:async h=>{var S;if(h.edit){console.log("Editando protocolo:",h);try{const D=await q.get("users/protocols/diet/active");(S=D==null?void 0:D.data)!=null&&S.id?s(`/dashboard/diet/edit-protocol/${D.data.id}`):s("/dashboard/diet/edit-protocol/mock-protocol-id-123")}catch(D){console.error("Error getting active protocol:",D),s("/dashboard/diet/edit-protocol/mock-protocol-id-123")}}else{console.log("Reutilizando protocolo:",h);try{alert(`Protocolo ${h.name} reutilizado com sucesso!`)}catch(D){console.error("Error reusing protocol:",D),alert("Erro ao reutilizar protocolo")}}}}),e.jsx(xa,{isOpen:te,onClose:()=>W(!1),initialTab:re}),e.jsx(wa,{isOpen:w,onClose:()=>E(!1),selectedDate:new Date().toISOString().split("T")[0]}),e.jsx(Ls,{isOpen:J,onClose:()=>{Z(!1),L(null)},onConfirm:Ie,title:"Protocolo Ativo Encontrado",message:"Ao criar um novo, o protocolo atual será finalizado. Deseja continuar?",confirmText:"Sim, Continuar",cancelText:"Cancelar",type:"warning",isLoading:p.isPending})]})}export{Da as DietPage};
