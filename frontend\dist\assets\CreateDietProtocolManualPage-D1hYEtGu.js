import{t as j,aB as y,j as s,aO as v,H as h,bi as N,y as d}from"./index-Dwgh6cj0.js";function _(){const m=j(),x=y(),f=async t=>{var u;try{const c=e=>({"weight-loss":1,maintenance:2,"muscle-gain":3})[e]||1,b=e=>{const n=[];return Object.entries(e).forEach(([l,r])=>{Array.isArray(r)&&r.forEach(o=>{n.push({name:o.name,day_of_week:l,meal_time:o.time,foods:o.foods.map(a=>({name:a.name,quantity:Number(a.quantity)||0,unit:a.unit||"g",calories:Number(a.calories)||0,protein:Number(a.protein)||0,carbs:Number(a.carbs)||0,fat:Number(a.fat)||0,fiber:Number(a.fiber)||0}))})})}),n},i={name:t.name,type_id:c(t.type),objective:t.objective||"",nutritional_goals:t.nutritional_goals||t.goals,meals:b(t.weeklyMeals),supplements:((u=t.supplements)==null?void 0:u.map(e=>({name:e.name,dosage:e.dosage,supplement_time:e.supplement_time,notes:e.notes||""})))||[],general_notes:t.notes||""};if(console.log("🚀 Sending protocol data to API:",i),!i.name)throw new Error("Protocol name is required");if(!i.type_id||typeof i.type_id!="number")throw new Error("Valid type_id is required");if(!i.nutritional_goals||typeof i.nutritional_goals!="object")throw new Error("Nutritional goals are required");if(!Array.isArray(i.meals))throw new Error("Meals must be an array");console.log("✅ Validation passed, using protocol creation hook..."),x.mutate({protocolData:i,protocolType:"diet",shouldFinalizeActive:!0},{onSuccess:e=>{console.log("✅ Diet protocol created successfully:",e),d.success("Protocolo de dieta salvo com sucesso!",{position:"bottom-right"}),setTimeout(()=>{m("/dashboard/diet")},2e3)},onError:e=>{var l,r,o,a,g;console.error("Error saving diet protocol:",e);let n="Erro ao salvar protocolo de dieta";(r=(l=e==null?void 0:e.response)==null?void 0:l.data)!=null&&r.message?Array.isArray(e.response.data.message)?n=e.response.data.message.join(", "):n=e.response.data.message:e!=null&&e.message&&(n=e.message),console.error("🚨 Detailed error:",{status:(o=e==null?void 0:e.response)==null?void 0:o.status,statusText:(a=e==null?void 0:e.response)==null?void 0:a.statusText,data:(g=e==null?void 0:e.response)==null?void 0:g.data,message:e==null?void 0:e.message}),d.error(n,{position:"bottom-right"})}})}catch(c){console.error("Error in handleSave:",c),d.error("Erro ao processar protocolo de dieta",{position:"bottom-right"})}},p=()=>{m("/dashboard/diet")};return s.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[s.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:s.jsx("div",{className:"flex items-center justify-between p-4",children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("button",{onClick:p,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:s.jsx(v,{className:"w-5 h-5"})}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:s.jsx(h,{className:"w-4 h-4 text-snapfit-green"})}),s.jsx("h1",{className:"text-lg font-medium text-white",children:"Criar Protocolo de Dieta Manual"})]})]})})}),s.jsx("div",{className:"p-4 pb-20",children:s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsx("div",{className:"bg-snapfit-green/10 rounded-xl p-4 border border-snapfit-green/20 mb-6",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5",children:s.jsx(h,{className:"w-4 h-4 text-snapfit-green"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Criação Manual de Protocolo"}),s.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[s.jsx("p",{children:"• Configure objetivos nutricionais personalizados"}),s.jsx("p",{children:"• Selecione alimentos do banco de dados integrado"}),s.jsx("p",{children:"• Organize refeições por dia da semana"}),s.jsx("p",{children:"• Adicione suplementos com horários específicos"}),s.jsx("p",{children:"• Cálculo automático de calorias e macronutrientes"})]})]})]})}),s.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20",children:s.jsx(N,{onSave:f,onCancel:p})})]})})]})}export{_ as CreateDietProtocolManualPage};
